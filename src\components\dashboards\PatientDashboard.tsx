import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Heart, 
  Calendar, 
  MessageCircle, 
  FileText, 
  Activity, 
  Pill, 
  Clock,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  User,
  Settings,
  Bell,
  Search
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

interface PatientDashboardProps {
  className?: string;
}

const PatientDashboard: React.FC<PatientDashboardProps> = ({ className }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');
  const [healthScore, setHealthScore] = useState(85);

  const [dashboardData, setDashboardData] = useState({
    upcomingAppointments: [],
    recentDiagnoses: [],
    medications: [],
    vitals: {
      bloodPressure: '',
      heartRate: '',
      temperature: '',
      weight: '',
      lastUpdated: ''
    }
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const response = await fetch('/api/patient/dashboard', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch dashboard data');
        }

        const data = await response.json();
        setDashboardData(data);
        setHealthScore(data.healthScore || 0);
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
        toast({
          title: "Error",
          description: "Failed to load dashboard data. Please check your connection.",
          variant: "destructive",
        });
      }
    };

    fetchDashboardData();
  }, [toast]);

  const quickActions = [
    {
      icon: MessageCircle,
      title: 'AI Consultation',
      description: 'Get instant medical advice',
      color: 'bg-blue-500',
      action: () => toast({ title: "Starting AI consultation..." })
    },
    {
      icon: Calendar,
      title: 'Book Appointment',
      description: 'Schedule with a doctor',
      color: 'bg-green-500',
      action: () => toast({ title: "Opening appointment booking..." })
    },
    {
      icon: FileText,
      title: 'View Records',
      description: 'Access medical history',
      color: 'bg-purple-500',
      action: () => toast({ title: "Loading medical records..." })
    },
    {
      icon: Pill,
      title: 'Medications',
      description: 'Manage prescriptions',
      color: 'bg-orange-500',
      action: () => toast({ title: "Opening medication manager..." })
    }
  ];

  const healthMetrics = [
    { label: 'Blood Pressure', value: '120/80', status: 'normal', trend: 'stable' },
    { label: 'Heart Rate', value: '72 bpm', status: 'normal', trend: 'stable' },
    { label: 'BMI', value: '23.5', status: 'normal', trend: 'improving' },
    { label: 'Cholesterol', value: '180 mg/dL', status: 'good', trend: 'improving' }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 ${className}`}>
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <Heart className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Welcome back, {user?.firstName || 'Patient'}!
                </h1>
                <p className="text-sm text-gray-500">Your health dashboard</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                <Search className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Bell className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Settings className="w-4 h-4" />
              </Button>
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-gray-600" />
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Health Score Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <Card className="bg-gradient-to-r from-blue-500 to-purple-500 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold mb-2">Health Score</h2>
                  <p className="text-blue-100">Based on your recent vitals and activities</p>
                </div>
                <div className="text-right">
                  <div className="text-4xl font-bold">{healthScore}</div>
                  <div className="text-sm text-blue-100">out of 100</div>
                  <Progress value={healthScore} className="w-32 mt-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.1 + index * 0.05 }}
              >
                <Card 
                  className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:-translate-y-1"
                  onClick={action.action}
                >
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center`}>
                        <action.icon className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{action.title}</h4>
                        <p className="text-sm text-gray-500">{action.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Upcoming Appointments */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Calendar className="w-5 h-5 text-blue-500" />
                    <span>Upcoming Appointments</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData.upcomingAppointments.map((appointment) => (
                      <div key={appointment.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                          <h4 className="font-semibold text-gray-900">{appointment.doctor}</h4>
                          <p className="text-sm text-gray-500">{appointment.specialty}</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline">{appointment.type}</Badge>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-900">{appointment.date}</p>
                          <p className="text-sm text-gray-500">{appointment.time}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                  <Button className="w-full mt-4" variant="outline">
                    View All Appointments
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Recent Diagnoses */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="w-5 h-5 text-purple-500" />
                    <span>Recent Diagnoses</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData.recentDiagnoses.map((diagnosis) => (
                      <div key={diagnosis.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                          <h4 className="font-semibold text-gray-900">{diagnosis.condition}</h4>
                          <p className="text-sm text-gray-500">{diagnosis.date}</p>
                        </div>
                        <div className="text-right">
                          <Badge 
                            variant={diagnosis.status === 'Treatment' ? 'default' : 'secondary'}
                            className="mb-1"
                          >
                            {diagnosis.status}
                          </Badge>
                          <p className="text-sm text-gray-500">{diagnosis.severity}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                  <Button className="w-full mt-4" variant="outline">
                    View Medical History
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Health Metrics */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Activity className="w-5 h-5 text-green-500" />
                    <span>Health Metrics</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {healthMetrics.map((metric, index) => (
                      <div key={metric.label} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{metric.label}</p>
                          <p className="text-sm text-gray-500">{metric.value}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          {metric.trend === 'improving' ? (
                            <TrendingUp className="w-4 h-4 text-green-500" />
                          ) : (
                            <CheckCircle className="w-4 h-4 text-blue-500" />
                          )}
                          <Badge 
                            variant={metric.status === 'normal' || metric.status === 'good' ? 'default' : 'destructive'}
                          >
                            {metric.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Medications */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Pill className="w-5 h-5 text-orange-500" />
                    <span>Current Medications</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData.medications.map((medication) => (
                      <div key={medication.id} className="p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold text-gray-900">{medication.name}</h4>
                          <Badge variant="outline">{medication.remaining} left</Badge>
                        </div>
                        <p className="text-sm text-gray-500 mb-1">{medication.dosage} - {medication.frequency}</p>
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-500">Next dose: {medication.nextDose}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                  <Button className="w-full mt-4" variant="outline">
                    Manage Medications
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Health Tips */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <AlertCircle className="w-5 h-5 text-yellow-500" />
                    <span>Health Tips</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-800">
                        💧 Remember to drink at least 8 glasses of water today
                      </p>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <p className="text-sm text-green-800">
                        🚶‍♂️ Take a 10-minute walk after meals to help digestion
                      </p>
                    </div>
                    <div className="p-3 bg-purple-50 rounded-lg">
                      <p className="text-sm text-purple-800">
                        😴 Aim for 7-9 hours of sleep for optimal health
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientDashboard;
