import { apiClient } from '@/services/api';

// Admin service now uses JWT authentication instead of X-Admin-Key
// The apiClient automatically includes the JWT token in Authorization header

export const adminService = {
  // Settings
  getSettings: async () => apiClient.get('/admin/settings'),
  saveSettings: async (settings: any) => apiClient.put('/admin/settings', settings),

  // Users
  listUsers: async () => apiClient.get('/admin/users'),
  addUser: async (user: any) => apiClient.post('/admin/users', user),
  toggleUserStatus: async (id: string) => apiClient.put(`/admin/users/${id}/toggle-status`),
  deleteUser: async (id: string) => apiClient.delete(`/admin/users/${id}`),

  // Payments overview
  listSubscriptions: async () => apiClient.get('/payments/subscriptions'),
  listPaymentLogs: async () => apiClient.get('/payments/logs'),
};

