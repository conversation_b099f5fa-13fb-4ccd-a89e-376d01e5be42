import { useState, useEffect, useCallback } from 'react';
import { diagnosisService, DiagnosisRequest, DiagnosisResult, DiagnosisHistory, Symptom } from '../services/diagnosisService';

export const useDiagnosis = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentDiagnosis, setCurrentDiagnosis] = useState<DiagnosisResult | null>(null);

  const createDiagnosis = async (diagnosisData: DiagnosisRequest): Promise<DiagnosisResult | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await diagnosisService.createDiagnosis(diagnosisData);
      
      if (response.success && response.data) {
        setCurrentDiagnosis(response.data);
        return response.data;
      } else {
        setError(response.error || 'Failed to create diagnosis');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create diagnosis');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateDiagnosis = async (diagnosisId: string, updateData: Partial<DiagnosisRequest>): Promise<DiagnosisResult | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await diagnosisService.updateDiagnosis(diagnosisId, updateData);
      
      if (response.success && response.data) {
        setCurrentDiagnosis(response.data);
        return response.data;
      } else {
        setError(response.error || 'Failed to update diagnosis');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update diagnosis');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const deleteDiagnosis = async (diagnosisId: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await diagnosisService.deleteDiagnosis(diagnosisId);
      
      if (response.success) {
        if (currentDiagnosis?._id === diagnosisId) {
          setCurrentDiagnosis(null);
        }
        return true;
      } else {
        setError(response.error || 'Failed to delete diagnosis');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete diagnosis');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    currentDiagnosis,
    createDiagnosis,
    updateDiagnosis,
    deleteDiagnosis,
    setCurrentDiagnosis
  };
};

export const useDiagnosisById = (diagnosisId: string | null) => {
  const [diagnosis, setDiagnosis] = useState<DiagnosisResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDiagnosis = useCallback(async () => {
    if (!diagnosisId) {
      setDiagnosis(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await diagnosisService.getDiagnosis(diagnosisId);
      
      if (response.success && response.data) {
        setDiagnosis(response.data);
      } else {
        setError(response.error || 'Failed to fetch diagnosis');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch diagnosis');
    } finally {
      setLoading(false);
    }
  }, [diagnosisId]);

  useEffect(() => {
    fetchDiagnosis();
  }, [fetchDiagnosis]);

  return {
    diagnosis,
    loading,
    error,
    refetch: fetchDiagnosis
  };
};

export const useDiagnosisHistory = (patientId?: string, page: number = 1, limit: number = 10) => {
  const [history, setHistory] = useState<DiagnosisResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const fetchHistory = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      let response;
      if (patientId) {
        response = await diagnosisService.getPatientDiagnoses(patientId, page, limit);
      } else {
        response = await diagnosisService.getAllDiagnoses(page, limit);
      }
      
      if (response.success && response.data) {
        setHistory(response.data.diagnoses);
        setTotal(response.data.total);
        setTotalPages(Math.ceil(response.data.total / limit));
      } else {
        setError(response.error || 'Failed to fetch diagnosis history');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch diagnosis history');
    } finally {
      setLoading(false);
    }
  }, [patientId, page, limit]);

  useEffect(() => {
    fetchHistory();
  }, [fetchHistory]);

  return {
    history,
    loading,
    error,
    total,
    totalPages,
    refetch: fetchHistory
  };
};

export const useSymptomSuggestions = (query: string) => {
  const [suggestions, setSuggestions] = useState<Symptom[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSuggestions = useCallback(async () => {
    if (!query || query.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await diagnosisService.getSymptomSuggestions(query);
      
      if (response.success && response.data) {
          // Convert string array to Symptom array if needed
          const symptomSuggestions = Array.isArray(response.data) 
            ? response.data.map((item: unknown) => 
                typeof item === 'string' 
                  ? { name: item, severity: 'mild' as const }
                  : item
              )
            : response.data;
          setSuggestions(symptomSuggestions);
        } else {
        setError(response.error || 'Failed to fetch symptom suggestions');
        setSuggestions([]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch symptom suggestions');
      setSuggestions([]);
    } finally {
      setLoading(false);
    }
  }, [query]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchSuggestions();
    }, 300); // Debounce the API call

    return () => clearTimeout(timeoutId);
  }, [fetchSuggestions]);

  return {
    suggestions,
    loading,
    error
  };
};

export const useDiagnosisStatistics = () => {
  const [statistics, setStatistics] = useState<unknown>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStatistics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await diagnosisService.getDiagnosisStatistics();
      
      if (response.success && response.data) {
        setStatistics(response.data);
      } else {
        setError(response.error || 'Failed to fetch diagnosis statistics');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch diagnosis statistics');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  return {
    statistics,
    loading,
    error,
    refetch: fetchStatistics
  };
};

// Hook for real-time diagnosis processing
export const useRealtimeDiagnosis = () => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState<DiagnosisResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const startDiagnosis = async (diagnosisData: DiagnosisRequest) => {
    setIsProcessing(true);
    setProgress(0);
    setResult(null);
    setError(null);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 500);

      const response = await diagnosisService.createDiagnosis(diagnosisData);
      
      clearInterval(progressInterval);
      setProgress(100);

      if (response.success && response.data) {
        setResult(response.data);
      } else {
        setError(response.error || 'Diagnosis failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Diagnosis failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const reset = () => {
    setIsProcessing(false);
    setProgress(0);
    setResult(null);
    setError(null);
  };

  return {
    isProcessing,
    progress,
    result,
    error,
    startDiagnosis,
    reset
  };
};