# 🔧 MEDORA Database Service Fix Guide

## 🚨 Current Issues Identified

1. **Vercel Deployment Protection**: Backend API is protected by Vercel authentication
2. **Missing Environment Variables**: Critical database and service variables not configured
3. **MongoDB Connection**: App running in demo mode without database connectivity
4. **Health Check Access**: API endpoints not publicly accessible

---

## 🛠️ Step-by-Step Fix Instructions

### Step 1: Disable Vercel Deployment Protection

**CRITICAL**: The backend API is currently protected and inaccessible.

1. **Go to Backend Project Dashboard**:
   ```
   https://vercel.com/bmds-projects-6efc3abf/medora-ai-backend
   ```

2. **Navigate to Settings → Deployment Protection**

3. **Disable Protection Options**:
   - Turn OFF "Vercel Authentication" for production deployments
   - OR add these paths to bypass list:
     - `/health`
     - `/api/status`
     - `/api/health/database`

### Step 2: Configure Environment Variables

**Backend Environment Variables** (medora-ai-backend project):

```bash
# Database Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/medora?retryWrites=true&w=majority
DB_NAME=medora

# Authentication & Security
JWT_SECRET=your-super-secure-jwt-secret-key-here-make-it-long-and-random
SESSION_SECRET=your-session-secret-key-here-also-make-it-random
JWT_EXPIRE=7d
BCRYPT_ROUNDS=12

# Server Configuration
NODE_ENV=production
FRONTEND_URL=https://medora-main-ixfecm88k-bmds-projects-6efc3abf.vercel.app

# CORS Configuration
ALLOWED_ORIGINS=https://medora-main-ixfecm88k-bmds-projects-6efc3abf.vercel.app,https://medora-main-bmds-projects-6efc3abf.vercel.app

# AI Services (Optional but recommended)
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo

# Payment Services (Optional)
PAYSTACK_SECRET_KEY=sk_live_your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=pk_live_your_paystack_public_key
```

**Frontend Environment Variables** (medora-main project):

```bash
# API Configuration
VITE_API_BASE_URL=https://medora-ai-backend-fg7vidc2i-bmds-projects-6efc3abf.vercel.app/api
VITE_API_URL=https://medora-ai-backend-fg7vidc2i-bmds-projects-6efc3abf.vercel.app

# Application Configuration
VITE_APP_NAME=MEDORA
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=AI-Powered Medical Assistant Platform

# Feature Flags
VITE_ENABLE_VOICE_CHAT=true
VITE_ENABLE_VIDEO_CALLS=true
VITE_ENABLE_AI_DIAGNOSIS=true
VITE_ENABLE_PAYMENTS=true
```

### Step 3: Set Up MongoDB Database

**Option A: MongoDB Atlas (Recommended)**

1. **Create MongoDB Atlas Account**:
   - Go to https://cloud.mongodb.com/
   - Create a free cluster

2. **Configure Database**:
   - Database Name: `medora`
   - Create a database user with read/write permissions
   - Whitelist IP addresses (0.0.0.0/0 for Vercel)

3. **Get Connection String**:
   ```
   mongodb+srv://<username>:<password>@<cluster>.mongodb.net/medora?retryWrites=true&w=majority
   ```

**Option B: Alternative Database Services**

- **Supabase**: PostgreSQL-based alternative
- **PlanetScale**: MySQL-based serverless database
- **Railway**: MongoDB hosting service

### Step 4: Update Environment Variables in Vercel

1. **Backend Project**:
   ```
   https://vercel.com/bmds-projects-6efc3abf/medora-ai-backend/settings/environment-variables
   ```

2. **Frontend Project**:
   ```
   https://vercel.com/bmds-projects-6efc3abf/medora-main/settings/environment-variables
   ```

3. **For Each Variable**:
   - Click "Add New"
   - Enter Name and Value
   - Select "Production, Preview, and Development"
   - Click "Save"

### Step 5: Redeploy Applications

After setting environment variables:

1. **Trigger Redeployment**:
   - Go to Deployments tab
   - Click on latest deployment
   - Click "Redeploy"

2. **Wait for Deployment**:
   - Monitor build logs for errors
   - Ensure both frontend and backend deploy successfully

---

## 🧪 Testing Database Service

### Health Check Endpoints

1. **Basic Health Check**:
   ```
   GET https://medora-ai-backend-fg7vidc2i-bmds-projects-6efc3abf.vercel.app/health
   ```

2. **API Status**:
   ```
   GET https://medora-ai-backend-fg7vidc2i-bmds-projects-6efc3abf.vercel.app/api/status
   ```

3. **Database Health Check**:
   ```
   GET https://medora-ai-backend-fg7vidc2i-bmds-projects-6efc3abf.vercel.app/api/health/database
   ```

### Expected Responses

**Healthy Database Response**:
```json
{
  "status": "success",
  "data": {
    "configured": true,
    "connected": true,
    "status": "healthy",
    "mode": "production",
    "ping": "success",
    "latency": "low"
  }
}
```

**Demo Mode Response**:
```json
{
  "status": "success",
  "data": {
    "configured": false,
    "connected": false,
    "status": "disconnected",
    "mode": "demo",
    "reason": "not_configured",
    "message": "MongoDB URI not configured - running in demo mode"
  }
}
```

---

## 🔍 Troubleshooting

### Common Issues

1. **"Authentication Required" Error**:
   - Vercel protection is still enabled
   - Follow Step 1 to disable protection

2. **"MongoDB not configured" Warning**:
   - MONGODB_URI environment variable not set
   - Follow Step 2 to configure variables

3. **Connection Timeout**:
   - Check MongoDB Atlas IP whitelist
   - Verify connection string format
   - Ensure database user has proper permissions

4. **CORS Errors**:
   - Update ALLOWED_ORIGINS environment variable
   - Include all frontend URLs

### Debug Commands

Test API connectivity:
```bash
# Test health endpoint
curl https://medora-ai-backend-fg7vidc2i-bmds-projects-6efc3abf.vercel.app/health

# Test database health
curl https://medora-ai-backend-fg7vidc2i-bmds-projects-6efc3abf.vercel.app/api/health/database
```

---

## ✅ Success Criteria

- [ ] Backend API accessible without authentication
- [ ] Health endpoints return 200 status
- [ ] Database shows "connected" status
- [ ] Frontend can communicate with backend
- [ ] No CORS errors in browser console
- [ ] User registration/login works
- [ ] AI chat functionality operational

---

## 📞 Next Steps

1. **Immediate**: Disable Vercel protection and set environment variables
2. **Short-term**: Set up MongoDB Atlas database
3. **Long-term**: Configure monitoring and alerts for database health

**Priority**: Fix Vercel protection first - this is blocking all API access!
