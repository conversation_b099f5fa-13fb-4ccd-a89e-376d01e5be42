import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardHeader, CardContent, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft, 
  Heart, 
  Users, 
  Award, 
  Target, 
  Shield, 
  Zap, 
  Globe,
  Stethoscope,
  Brain,
  Database,
  Lock
} from 'lucide-react';

const About = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: Brain,
      title: 'AI-Powered Diagnosis',
      description: 'Advanced machine learning algorithms provide accurate medical insights and diagnosis assistance.'
    },
    {
      icon: Stethoscope,
      title: 'Medical Consultation',
      description: 'Real-time voice and text-based medical consultations with AI-powered recommendations.'
    },
    {
      icon: Database,
      title: 'Comprehensive Records',
      description: 'Secure storage and management of patient medical records and health history.'
    },
    {
      icon: Shield,
      title: 'HIPAA Compliant',
      description: 'Enterprise-grade security ensuring patient data privacy and regulatory compliance.'
    },
    {
      icon: Zap,
      title: 'Real-time Analytics',
      description: 'Advanced analytics and reporting for healthcare professionals and administrators.'
    },
    {
      icon: Globe,
      title: 'Multi-language Support',
      description: 'Available in multiple languages to serve diverse healthcare communities worldwide.'
    }
  ];

  const team = [
    {
      name: 'Dr. <PERSON>',
      role: 'Chief Medical Officer',
      specialty: 'Internal Medicine',
      experience: '15+ years'
    },
    {
      name: 'Dr. <PERSON>',
      role: 'AI Research Director',
      specialty: 'Machine Learning',
      experience: '12+ years'
    },
    {
      name: 'Dr. Emily Rodriguez',
      role: 'Clinical Operations',
      specialty: 'Emergency Medicine',
      experience: '10+ years'
    },
    {
      name: 'Dr. James Thompson',
      role: 'Data Science Lead',
      specialty: 'Healthcare Analytics',
      experience: '8+ years'
    }
  ];

  const stats = [
    { label: 'Patients Served', value: '50,000+', icon: Users },
    { label: 'Accuracy Rate', value: '98.5%', icon: Target },
    { label: 'Healthcare Partners', value: '200+', icon: Heart },
    { label: 'Countries', value: '25+', icon: Globe }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/')}
              className="text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
            <div className="flex items-center space-x-3">
              <img 
                src="/lovable-uploads/logo-png.png" 
                alt="MEDORA Logo" 
                className="h-8 w-8 object-contain"
              />
              <span className="text-xl font-bold font-bruno" style={{color: '#9ACD32'}}>MEDORA</span>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 font-bruno">
            About <span style={{color: '#9ACD32'}}>MEDORA</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Revolutionizing healthcare through AI-powered medical assistance, providing accurate diagnosis, 
            comprehensive patient care, and intelligent health management solutions.
          </p>
          <div className="flex flex-wrap justify-center gap-2">
            <Badge variant="secondary" className="px-4 py-2">AI-Powered</Badge>
            <Badge variant="secondary" className="px-4 py-2">HIPAA Compliant</Badge>
            <Badge variant="secondary" className="px-4 py-2">Real-time</Badge>
            <Badge variant="secondary" className="px-4 py-2">Secure</Badge>
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <Card key={index} className="text-center">
              <CardContent className="pt-6">
                <stat.icon className="h-8 w-8 mx-auto mb-4 text-primary" />
                <div className="text-3xl font-bold text-foreground mb-2">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Mission Section */}
        <div className="mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="text-3xl text-center mb-4">Our Mission</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div>
                  <h3 className="text-2xl font-semibold mb-4">Transforming Healthcare with AI</h3>
                  <p className="text-muted-foreground mb-4">
                    MEDORA is dedicated to making quality healthcare accessible to everyone through 
                    cutting-edge artificial intelligence and machine learning technologies.
                  </p>
                  <p className="text-muted-foreground mb-4">
                    Our platform combines the expertise of medical professionals with the power of AI 
                    to provide accurate diagnoses, personalized treatment recommendations, and 
                    comprehensive health management solutions.
                  </p>
                  <p className="text-muted-foreground">
                    We believe that technology should enhance, not replace, the human touch in healthcare, 
                    empowering both patients and healthcare providers with intelligent tools and insights.
                  </p>
                </div>
                <div className="flex justify-center">
                  <div className="w-64 h-64 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center">
                    <img
                      src="/lovable-uploads/logo-png.png"
                      alt="MEDORA Logo"
                      className="h-32 w-32 object-contain"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Features Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Key Features</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <feature.icon className="h-12 w-12 text-primary mb-4" />
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Team Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Our Expert Team</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {team.map((member, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="w-20 h-20 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <Users className="h-10 w-10 text-primary" />
                  </div>
                  <CardTitle className="text-lg">{member.name}</CardTitle>
                  <p className="text-primary font-medium">{member.role}</p>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-2">{member.specialty}</p>
                  <Badge variant="outline">{member.experience}</Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <Card className="bg-gradient-to-r from-primary/10 to-secondary/10">
            <CardContent className="pt-8 pb-8">
              <h2 className="text-3xl font-bold mb-4">Ready to Experience MEDORA?</h2>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Join thousands of healthcare professionals and patients who trust MEDORA 
                for intelligent medical assistance and comprehensive healthcare management.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  size="lg" 
                  onClick={() => navigate('/signup')}
                  className="bg-primary text-primary-foreground hover:bg-primary/90"
                >
                  Get Started Today
                </Button>
                <Button 
                  size="lg" 
                  variant="outline"
                  onClick={() => navigate('/contact')}
                >
                  Contact Us
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default About;
