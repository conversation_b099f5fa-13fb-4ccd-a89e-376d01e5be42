import React, { useState, useEffect, createContext, useContext } from 'react';

export type Theme = 'light' | 'dark' | 'lemon' | 'system';

export const useSystemTheme = () => {
  const [theme, setTheme] = useState<Theme>('system');
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light');

  // Detect system theme preference
  const getSystemTheme = (): 'light' | 'dark' => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  };

  // Apply theme to document
  const applyTheme = (newTheme: Theme) => {
    const root = document.documentElement;
    
    // Remove existing theme classes
    root.classList.remove('light', 'dark', 'lemon');
    
    // Determine actual theme to apply
    let actualTheme = newTheme;
    if (newTheme === 'system') {
      actualTheme = systemTheme;
    }
    
    // Add theme class
    if (actualTheme === 'dark') {
      root.classList.add('dark');
    } else if (actualTheme === 'lemon') {
      root.classList.add('lemon');
    } else {
      root.classList.add('light');
    }
    
    // Update body background based on theme
    if (actualTheme === 'lemon') {
      document.body.style.background = 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)';
    } else if (actualTheme === 'dark') {
      document.body.style.background = '#000000';
    } else {
      document.body.style.background = 'linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%)';
    }
  };

  // Initialize theme from localStorage or system preference
  useEffect(() => {
    const savedTheme = localStorage.getItem('medora-theme') as Theme || 'system';
    const currentSystemTheme = getSystemTheme();
    
    setTheme(savedTheme);
    setSystemTheme(currentSystemTheme);
    applyTheme(savedTheme);
  }, []);

  // Listen for system theme changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      const newSystemTheme = e.matches ? 'dark' : 'light';
      setSystemTheme(newSystemTheme);
      
      // If current theme is system, reapply theme
      if (theme === 'system') {
        applyTheme('system');
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [theme]);

  // Update theme and save to localStorage
  const updateTheme = (newTheme: Theme) => {
    setTheme(newTheme);
    applyTheme(newTheme);
    localStorage.setItem('medora-theme', newTheme);
  };

  // Get current effective theme (resolves 'system' to actual theme)
  const getEffectiveTheme = (): 'light' | 'dark' | 'lemon' => {
    if (theme === 'system') {
      return systemTheme;
    }
    return theme as 'light' | 'dark' | 'lemon';
  };

  return {
    theme,
    systemTheme,
    effectiveTheme: getEffectiveTheme(),
    updateTheme,
    applyTheme,
  };
};

// Theme context for global theme management
interface ThemeContextType {
  theme: Theme;
  systemTheme: 'light' | 'dark';
  effectiveTheme: 'light' | 'dark' | 'lemon';
  updateTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const themeHook = useSystemTheme();
  
  const contextValue: ThemeContextType = {
    theme: themeHook.theme,
    systemTheme: themeHook.systemTheme,
    effectiveTheme: themeHook.effectiveTheme,
    updateTheme: themeHook.updateTheme,
  };
  
  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};
