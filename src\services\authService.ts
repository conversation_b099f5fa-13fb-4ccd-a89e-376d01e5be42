import { apiClient, ApiResponse } from './api';

export interface User {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'patient' | 'doctor' | 'admin' | 'nurse';
  phoneNumber?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  medicalHistory?: string[];
  emergencyContact?: {
    name: string;
    relationship: string;
    phoneNumber: string;
  };
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: 'patient' | 'doctor' | 'nurse' | 'admin';
  phoneNumber?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  specialty?: string;
  licenseNumber?: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

class AuthService {
  async login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> {
    console.log('🔗 Logging in user:', credentials.email);
    const response = await apiClient.post('/auth/login', credentials);

    console.log('📡 Login response:', response);

    // Check if API call failed
    if (response.success === false) {
      return {
        success: false,
        error: response.error || 'Login failed'
      };
    }

    // Handle backend response structure: { status: 'success', token, data: { user } }
    if (response.status === 'success' && response.token) {
      const authResponse: AuthResponse = {
        token: response.token,
        user: response.data.user
      };

      apiClient.setToken(response.token);

      return {
        success: true,
        data: authResponse
      };
    }

    return {
      success: false,
      error: response.message || 'Login failed'
    };
  }

  async register(userData: RegisterData): Promise<ApiResponse<AuthResponse>> {
    console.log('🔗 Registering user:', userData);
    const response = await apiClient.post('/auth/register', userData);

    console.log('📡 Registration response:', response);

    // Check if API call failed
    if (response.success === false) {
      return {
        success: false,
        error: response.error || 'Registration failed'
      };
    }

    // Handle backend response structure: { status: 'success', token, data: { user } }
    if (response.status === 'success' && response.token) {
      const authResponse: AuthResponse = {
        token: response.token,
        user: response.data.user
      };

      apiClient.setToken(response.token);

      return {
        success: true,
        data: authResponse
      };
    }

    return {
      success: false,
      error: response.message || 'Registration failed'
    };
  }

  async logout(): Promise<void> {
    try {
      await apiClient.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      apiClient.setToken(null);
    }
  }

  async getCurrentUser(): Promise<ApiResponse<User>> {
    return apiClient.get<User>('/auth/me');
  }

  async refreshToken(): Promise<ApiResponse<{ token: string }>> {
    const response = await apiClient.post<{ token: string }>('/auth/refresh');
    
    if (response.success && response.data?.token) {
      apiClient.setToken(response.data.token);
    }
    
    return response;
  }

  async forgotPassword(email: string): Promise<ApiResponse<{ message: string }>> {
    return apiClient.post<{ message: string }>('/auth/forgot-password', {
      email
    });
  }

  async resetPassword(email: string, newPassword: string): Promise<ApiResponse<{ message: string }>> {
    return apiClient.post<{ message: string }>('/auth/reset-password', {
      email,
      newPassword
    });
  }

  async updateProfile(profileData: Partial<User>): Promise<ApiResponse<User>> {
    return apiClient.put<User>('/auth/profile', profileData);
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse<{ message: string }>> {
    return apiClient.put<{ message: string }>('/auth/change-password', {
      currentPassword,
      newPassword
    });
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('authToken');
  }

  getToken(): string | null {
    return localStorage.getItem('authToken');
  }
}

export const authService = new AuthService();