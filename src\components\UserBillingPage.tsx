import React, { useState } from 'react';
import { CreditCard, Calendar, Download, AlertCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const UserBillingPage = () => {
  const [selectedPlan, setSelectedPlan] = useState('professional');

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-foreground font-unica">BILLING & SUBSCRIPTION</h1>
            <p className="text-muted-foreground mt-2">Manage your subscription and billing information</p>
          </div>
          <Button className="h-12">
            <Download className="h-5 w-5 mr-2" />
            Download Invoice
          </Button>
        </div>

        {/* Current Plan */}
        <Card className="p-8 neu-raised">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-semibold">Current Plan</h2>
            <Badge className="bg-emerald-500/20 text-emerald-400 neu-flat">Active</Badge>
          </div>
          <div className="grid md:grid-cols-3 gap-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Professional Plan</h3>
              <p className="text-3xl font-bold text-primary">$99/month</p>
              <p className="text-muted-foreground">Billed monthly</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Next billing date</h4>
              <p className="text-muted-foreground">January 15, 2025</p>
              <h4 className="font-medium mb-2 mt-4">Usage this month</h4>
              <p className="text-muted-foreground">2,450 / 5,000 queries</p>
            </div>
            <div className="flex flex-col space-y-3">
              <Button variant="outline" className="h-12">Change Plan</Button>
              <Button variant="outline" className="h-12">Cancel Subscription</Button>
            </div>
          </div>
        </Card>

        {/* Payment Methods */}
        <Card className="p-8 neu-raised">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-semibold">Payment Methods</h2>
            <Button className="h-12">
              <CreditCard className="h-5 w-5 mr-2" />
              Add Payment Method
            </Button>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border border-border rounded-lg neu-flat">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-8 bg-primary rounded flex items-center justify-center">
                  <span className="text-white text-xs font-bold">VISA</span>
                </div>
                <div>
                  <p className="font-medium">•••• •••• •••• 4242</p>
                  <p className="text-sm text-muted-foreground">Expires 12/2025</p>
                </div>
                <Badge className="bg-emerald-500/20 text-emerald-400">Default</Badge>
              </div>
              <Button variant="outline" size="sm">Remove</Button>
            </div>
          </div>
        </Card>

        {/* Billing History */}
        <Card className="p-8 neu-raised">
          <h2 className="text-2xl font-semibold mb-6">Billing History</h2>
          <div className="space-y-4">
            {[
              { date: "Dec 15, 2024", amount: "$99.00", status: "Paid", invoice: "INV-001" },
              { date: "Nov 15, 2024", amount: "$99.00", status: "Paid", invoice: "INV-002" },
              { date: "Oct 15, 2024", amount: "$99.00", status: "Paid", invoice: "INV-003" },
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-border rounded-lg neu-flat">
                <div className="flex items-center space-x-6">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{item.date}</p>
                    <p className="text-sm text-muted-foreground">{item.invoice}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="font-medium">{item.amount}</span>
                  <Badge className={item.status === 'Paid' ? 'bg-emerald-500/20 text-emerald-400' : 'bg-red-500/20 text-red-400'}>
                    {item.status}
                  </Badge>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default UserBillingPage;