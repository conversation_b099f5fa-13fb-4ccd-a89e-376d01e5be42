import React from 'react';
import { motion } from 'framer-motion';
import HealthcareAuth from '@/components/auth/HealthcareAuth';
import { useAuth } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';

const DoctorLogin: React.FC = () => {
  const { user, isAuthenticated } = useAuth();

  // Redirect if already authenticated as doctor
  if (isAuthenticated && user?.role === 'doctor') {
    return <Navigate to="/doctor-dashboard" replace />;
  }

  // Redirect if authenticated as different role
  if (isAuthenticated && user?.role !== 'doctor') {
    const redirectPath = user?.role === 'patient' ? '/patient-dashboard' : 
                        user?.role === 'nurse' ? '/nurse-dashboard' : 
                        user?.role === 'admin' ? '/admin-dashboard' : '/';
    return <Navigate to={redirectPath} replace />;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <HealthcareAuth 
        userType="doctor"
        redirectTo="/doctor-dashboard"
        onSuccess={() => {
          // Handle successful authentication
          console.log('Doctor authentication successful');
        }}
      />
    </motion.div>
  );
};

export default DoctorLogin;
