import React from 'react';
import { Outlet, NavLink, useLocation, useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  LogOut,
  Menu,
  Users,
  Settings,
  Monitor,
  FileText,
  Shield,
  Database,
  BarChart3,
  CreditCard,
  AlertTriangle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { useAuth } from '@/hooks/useAuth';

const AdminLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { logout } = useAuth();

  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: LayoutDashboard },
    { name: 'User Management', href: '/admin/users', icon: Users },
    { name: 'Payments', href: '/admin/payments', icon: CreditCard },
    { name: 'KYC Management', href: '/admin/kyc', icon: Shield },
    { name: 'Withdrawals', href: '/admin/withdrawals', icon: Database },
    { name: 'Penalties', href: '/admin/penalties', icon: AlertTriangle },
    { name: 'Settings', href: '/admin/settings', icon: Settings },
    { name: 'System Monitoring', href: '/monitoring', icon: Monitor },
    { name: 'Analytics', href: '/admin/analytics', icon: BarChart3 },
  ];

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const NavItems = () => (
    <nav className="space-y-2">
      {navigation.map((item) => {
        const isActive = location.pathname === item.href;
        return (
          <NavLink
            key={item.name}
            to={item.href}
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors",
              isActive
                ? "bg-primary text-primary-foreground"
                : "text-muted-foreground hover:text-foreground hover:bg-muted"
            )}
          >
            <item.icon className="h-4 w-4" />
            {item.name}
          </NavLink>
        );
      })}
    </nav>
  );

  return (
    <div className="min-h-screen bg-background">
      <div className="flex">
        {/* Desktop Sidebar */}
        <aside className="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 lg:border-r lg:bg-muted/10">
          <div className="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto">
            <div className="flex items-center flex-shrink-0 px-4 gap-3">
              <img src="/lovable-uploads/logo-png.png" alt="MEDORA Logo" className="h-8 w-8" />
              <h1 className="text-xl font-bold" style={{color: '#9ACD32'}}>MEDORA Admin</h1>
            </div>
            <div className="mt-8 flex-grow flex flex-col px-4">
              <NavItems />
            </div>
            <div className="flex-shrink-0 px-4 pb-4">
              <Button
                variant="ghost"
                className="w-full justify-start gap-3"
                style={{color: '#9ACD32'}}
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4" />
                Logout
              </Button>
            </div>
          </div>
        </aside>

        {/* Mobile Navigation */}
        <div className="lg:hidden">
          <div className="flex items-center justify-between p-4 border-b bg-background">
            <div className="flex items-center gap-3">
              <img src="/lovable-uploads/logo-png.png" alt="MEDORA Logo" className="h-8 w-8" />
              <h1 className="text-xl font-bold" style={{color: '#9ACD32'}}>MEDORA Admin</h1>
            </div>
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-64">
                <div className="mt-8">
                  <NavItems />
                </div>
                <div className="absolute bottom-4 left-4 right-4">
                  <Button
                    variant="ghost"
                    className="w-full justify-start gap-3"
                    style={{color: '#9ACD32'}}
                    onClick={handleLogout}
                  >
                    <LogOut className="h-4 w-4" />
                    Logout
                  </Button>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        {/* Main Content */}
        <main className="flex-1 lg:pl-64">
          <div className="p-6">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;