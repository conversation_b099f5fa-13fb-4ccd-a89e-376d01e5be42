const express = require('express');
const { protect } = require('../middleware/auth');
const logger = require('../utils/logger');
const axios = require('axios');

const router = express.Router();

// AI Service Configuration
const AI_CONFIG = {
  openai: {
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: 'https://api.openai.com/v1',
    model: 'gpt-3.5-turbo',
    maxTokens: 500,
    temperature: 0.7
  },
  fallback: {
    enabled: true,
    responses: [
      "I understand your concern. As a medical AI assistant, I recommend consulting with a healthcare professional for proper evaluation.",
      "Thank you for sharing your symptoms. While I can provide general guidance, a medical examination would be most appropriate.",
      "I'm here to help with health information. For accurate diagnosis and treatment, please consult with a qualified healthcare provider."
    ]
  }
};

// Enhanced AI Response Generation
async function generateAIResponse(message, conversationId, userId) {
  try {
    // Try OpenAI API first
    if (AI_CONFIG.openai.apiKey) {
      const response = await axios.post(
        `${AI_CONFIG.openai.baseURL}/chat/completions`,
        {
          model: AI_CONFIG.openai.model,
          messages: [
            {
              role: "system",
              content: `You are MEDORA, a professional medical AI assistant. Provide helpful, accurate medical information while always emphasizing the importance of consulting healthcare professionals for diagnosis and treatment. Be empathetic, professional, and never provide definitive diagnoses. Focus on symptom assessment, general health guidance, and when to seek medical care.`
            },
            {
              role: "user",
              content: message
            }
          ],
          max_tokens: AI_CONFIG.openai.maxTokens,
          temperature: AI_CONFIG.openai.temperature
        },
        {
          headers: {
            'Authorization': `Bearer ${AI_CONFIG.openai.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      return {
        response: response.data.choices[0].message.content,
        source: 'openai',
        model: AI_CONFIG.openai.model,
        confidence: 0.9
      };
    }
  } catch (error) {
    logger.warn('OpenAI API failed, using fallback:', error.message);
  }

  // Fallback to enhanced rule-based responses
  return generateFallbackResponse(message);
}

function generateFallbackResponse(message) {
  const lowerMessage = message.toLowerCase();

  // Enhanced medical response patterns
  const responsePatterns = {
    pain: [
      "I understand you're experiencing pain. Pain can have many causes, and the location, intensity, and duration are important factors. I'd recommend documenting when the pain occurs and consulting with a healthcare provider for proper evaluation.",
      "Pain symptoms should be evaluated by a medical professional. In the meantime, note any triggers, the pain scale (1-10), and whether it's affecting your daily activities."
    ],
    fever: [
      "Fever can indicate your body is fighting an infection. Monitor your temperature, stay hydrated, and seek medical attention if it persists above 101°F (38.3°C) or if you experience severe symptoms.",
      "A fever warrants medical attention, especially if accompanied by other symptoms. Keep track of your temperature and consider contacting a healthcare provider."
    ],
    headache: [
      "Headaches can have various causes. Note the frequency, intensity, and any triggers. If headaches are severe, sudden, or different from usual patterns, seek medical evaluation.",
      "Persistent or severe headaches should be evaluated by a healthcare professional. Keep a headache diary to help identify patterns or triggers."
    ],
    breathing: [
      "Breathing difficulties require immediate medical attention. If you're experiencing severe shortness of breath, chest pain, or difficulty breathing, seek emergency care immediately.",
      "Any breathing problems should be evaluated promptly by a healthcare provider. Don't delay seeking medical attention for respiratory symptoms."
    ],
    chest: [
      "Chest symptoms can be serious and should be evaluated by a healthcare professional immediately. If you're experiencing chest pain, pressure, or discomfort, especially with shortness of breath, seek emergency care.",
      "Chest-related symptoms require prompt medical evaluation. Don't hesitate to seek emergency care if you're concerned about chest pain or discomfort."
    ]
  };

  // Find matching pattern
  for (const [key, responses] of Object.entries(responsePatterns)) {
    if (lowerMessage.includes(key)) {
      return {
        response: responses[Math.floor(Math.random() * responses.length)],
        source: 'fallback',
        model: 'rule_based',
        confidence: 0.7
      };
    }
  }

  // Default response
  return {
    response: AI_CONFIG.fallback.responses[Math.floor(Math.random() * AI_CONFIG.fallback.responses.length)],
    source: 'fallback',
    model: 'default',
    confidence: 0.6
  };
}

// @route   POST /api/ai/chat
// @desc    Process AI chat message with real AI integration
// @access  Private
router.post('/chat', protect, async (req, res, next) => {
  try {
    const { message, conversationId } = req.body;

    if (!message) {
      return res.status(400).json({
        status: 'error',
        message: 'Message is required'
      });
    }

    const startTime = Date.now();

    // Generate AI response using enhanced service
    const aiResult = await generateAIResponse(message, conversationId, req.user.id);

    const processingTime = Date.now() - startTime;

    logger.info('AI Chat Response Generated', {
      userId: req.user.id,
      messageLength: message.length,
      responseLength: aiResult.response.length,
      conversationId,
      source: aiResult.source,
      model: aiResult.model,
      processingTime,
      confidence: aiResult.confidence
    });

    res.json({
      status: 'success',
      data: {
        response: aiResult.response,
        conversationId: conversationId || `conv_${Date.now()}`,
        timestamp: new Date().toISOString(),
        confidence: aiResult.confidence,
        source: aiResult.source,
        model: aiResult.model,
        processingTime
      }
    });

  } catch (error) {
    logger.error('AI Chat Error:', error);

    // Fallback response on error
    res.json({
      status: 'success',
      data: {
        response: "I apologize, but I'm experiencing technical difficulties. Please try again or consult with a healthcare professional for immediate assistance.",
        conversationId: conversationId || `conv_${Date.now()}`,
        timestamp: new Date().toISOString(),
        confidence: 0.5,
        source: 'error_fallback',
        model: 'fallback'
      }
    });
  }
});

// @route   POST /api/ai/voice
// @desc    Process voice input for AI
// @access  Private
router.post('/voice', protect, async (req, res, next) => {
  try {
    const { transcript, audioData, conversationId } = req.body;
    
    if (!transcript) {
      return res.status(400).json({
        status: 'error',
        message: 'Voice transcript is required'
      });
    }

    // Simulate voice processing
    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000));

    // Generate medical AI voice response
    const voiceResponses = [
      `I heard you mention ${transcript.toLowerCase()}. Let me help you with that. Can you provide more details about when this started?`,
      `Thank you for using voice input. I'm processing your medical query about ${transcript.toLowerCase()}. This could be related to several conditions.`,
      `I understand your concern about ${transcript.toLowerCase()}. As your AI medical assistant, I recommend we gather more information for a better assessment.`,
      `Your voice input has been processed. The symptoms you described - ${transcript.toLowerCase()} - warrant further discussion. How long have you been experiencing this?`,
      `I've analyzed your voice message regarding ${transcript.toLowerCase()}. While I can provide guidance, please remember that professional medical consultation is important.`
    ];

    const aiResponse = voiceResponses[Math.floor(Math.random() * voiceResponses.length)];

    logger.info('AI Voice Response Generated', {
      userId: req.user.id,
      transcriptLength: transcript.length,
      responseLength: aiResponse.length,
      conversationId,
      hasAudioData: !!audioData
    });

    res.json({
      status: 'success',
      data: {
        response: aiResponse,
        transcript: transcript,
        conversationId: conversationId || `voice_conv_${Date.now()}`,
        timestamp: new Date().toISOString(),
        confidence: 0.88 + Math.random() * 0.1,
        shouldSpeak: true // Indicate that response should be spoken
      }
    });

  } catch (error) {
    logger.error('AI Voice Error:', error);
    next(error);
  }
});

// @route   GET /api/ai/conversation/:id
// @desc    Get conversation history
// @access  Private
router.get('/conversation/:id', protect, async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // TODO: Implement actual conversation history retrieval from database
    const mockHistory = [];

    logger.info('Conversation History Retrieved', {
      userId: req.user.id,
      conversationId: id,
      messageCount: mockHistory.length
    });

    res.json({
      status: 'success',
      data: {
        conversationId: id,
        messages: mockHistory,
        totalMessages: mockHistory.length
      }
    });

  } catch (error) {
    logger.error('Conversation History Error:', error);
    next(error);
  }
});

// @route   POST /api/ai/analyze-symptoms
// @desc    Analyze symptoms with AI
// @access  Private
router.post('/analyze-symptoms', protect, async (req, res, next) => {
  try {
    const { symptoms, duration, severity, additionalInfo } = req.body;
    
    if (!symptoms || !Array.isArray(symptoms) || symptoms.length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Symptoms array is required'
      });
    }

    // Simulate AI symptom analysis
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

    const analysis = {
      primarySymptoms: symptoms.slice(0, 3),
      possibleConditions: [
        {
          condition: 'Common Cold',
          probability: 0.65,
          description: 'Based on the symptoms provided, this appears to be a common viral infection.'
        },
        {
          condition: 'Seasonal Allergies',
          probability: 0.25,
          description: 'Allergic reactions could explain some of the symptoms mentioned.'
        },
        {
          condition: 'Stress-related symptoms',
          probability: 0.10,
          description: 'Physical manifestations of stress or anxiety.'
        }
      ],
      recommendations: [
        'Rest and stay hydrated',
        'Monitor symptoms for changes',
        'Consult healthcare provider if symptoms worsen',
        'Consider over-the-counter remedies for symptom relief'
      ],
      urgencyLevel: severity > 7 ? 'high' : severity > 4 ? 'medium' : 'low',
      disclaimer: 'This analysis is for informational purposes only and should not replace professional medical advice.'
    };

    logger.info('Symptom Analysis Completed', {
      userId: req.user.id,
      symptomCount: symptoms.length,
      severity,
      duration,
      urgencyLevel: analysis.urgencyLevel
    });

    res.json({
      status: 'success',
      data: analysis
    });

  } catch (error) {
    logger.error('Symptom Analysis Error:', error);
    next(error);
  }
});

module.exports = router;
