
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { HelmetProvider } from 'react-helmet-async';
import { AuthProvider } from './hooks/useAuth.tsx';
import { useEffect, useState } from 'react';
import AOS from 'aos';
import 'aos/dist/aos.css';
import ThemeSwitcher from './components/ThemeSwitcher';
import { Preloader } from './components/ui/preloader';
import { ThemeProvider } from './hooks/useSystemTheme';
import AuthCallback from './pages/AuthCallback';
import AuthError from './pages/AuthError';

// Pages
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Login from "./pages/Login";
import Signup from "./pages/Signup";
import Dashboard from "./pages/Dashboard";
import MedicalDashboard from "./pages/MedicalDashboard";
import ApiDocumentation from "./pages/ApiDocumentation";
import UserManagement from "./pages/UserManagement";
import Pricing from "./pages/Pricing";
import Payment from "./pages/Payment";
import PaymentCallback from "./pages/PaymentCallback";
import Monitoring from "./pages/Monitoring";
import GlobalSettings from "./pages/GlobalSettings";
import Billing from "./pages/Billing";
import AccountManagement from "./pages/AccountManagement";
import Appointments from "./pages/Appointments";
import HealthRecords from "./pages/HealthRecords";
import About from "./pages/About";
import Contact from "./pages/Contact";
import HelpSupport from "./pages/HelpSupport";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsOfService from "./pages/TermsOfService";
import MedicalDisclaimer from "./pages/MedicalDisclaimer";
import AdminLogin from "./pages/AdminLogin";

// Role-specific login pages
import PatientLogin from "./pages/PatientLogin";
import DoctorLogin from "./pages/DoctorLogin";
import NurseLogin from "./pages/NurseLogin";

// Role-specific dashboard pages
import PatientDashboardPage from "./pages/PatientDashboardPage";
import DoctorDashboardPage from "./pages/DoctorDashboardPage";
import NurseDashboardPage from "./pages/NurseDashboardPage";

// Components
import { ForgotPasswordForm } from "./components/auth/ForgotPasswordForm";
import { ResetPasswordForm } from "./components/auth/ResetPasswordForm";
import { UserProfile } from "./components/auth/UserProfile";
import AdminLayout from "./components/admin/AdminLayout";
import AdminDashboard from "./components/admin/AdminDashboard";
import AdminUsers from "./components/admin/AdminUsers";
import AdminPayments from "./components/admin/AdminPayments";
import AdminSettings from "./components/admin/AdminSettings";
import AdminKYC from "./components/admin/AdminKYC";
import AdminWithdrawals from "./components/admin/AdminWithdrawals";
import AdminPenalties from "./components/admin/AdminPenalties";
import ProtectedRoute from "./components/ProtectedRoute";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const App = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    AOS.init({
      duration: 1000,
      easing: 'ease-out-cubic',
      once: true,
      offset: 100,
    });

    // Simulate app initialization
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2500);

    return () => clearTimeout(timer);
  }, []);

  return (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <AuthProvider>
            <TooltipProvider>
              <Toaster />
              <Sonner />
              <Preloader isLoading={isLoading} variant="ai" />
              <BrowserRouter>
            {/* Global Theme Switcher */}
            <div className="fixed top-4 right-4 z-50">
              <ThemeSwitcher />
            </div>
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<Index />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Signup />} />
              <Route path="/signup" element={<Signup />} />
              <Route path="/forgot-password" element={<ForgotPasswordForm />} />
              <Route path="/reset-password" element={<ResetPasswordForm />} />
              <Route path="/pricing" element={<Pricing />} />
              <Route path="/about" element={<About />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/help" element={<HelpSupport />} />
              <Route path="/support" element={<HelpSupport />} />
              <Route path="/privacy" element={<PrivacyPolicy />} />
              <Route path="/terms" element={<TermsOfService />} />
              <Route path="/disclaimer" element={<MedicalDisclaimer />} />

              {/* Role-specific Login Routes */}
              <Route path="/patient/login" element={<PatientLogin />} />
              <Route path="/patient/signup" element={<Signup />} />
              <Route path="/doctor/login" element={<DoctorLogin />} />
              <Route path="/doctor/signup" element={<Signup />} />
              <Route path="/nurse/login" element={<NurseLogin />} />
              <Route path="/nurse/signup" element={<Signup />} />
              <Route path="/staff/login" element={<DoctorLogin />} />
              <Route path="/staff/signup" element={<Signup />} />
              <Route path="/admin/login" element={<AdminLogin />} />

              {/* OAuth Routes */}
              <Route path="/auth/callback" element={<AuthCallback />} />
              <Route path="/auth/error" element={<AuthError />} />

              {/* Protected Routes - General */}
              <Route path="/dashboard" element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              } />
              <Route path="/profile" element={
                <ProtectedRoute>
                  <UserProfile />
                </ProtectedRoute>
              } />
              <Route path="/account" element={
                <ProtectedRoute>
                  <AccountManagement />
                </ProtectedRoute>
              } />
              <Route path="/billing" element={
                <ProtectedRoute>
                  <Billing />
                </ProtectedRoute>
              } />
              <Route path="/api-docs" element={
                <ProtectedRoute>
                  <ApiDocumentation />
                </ProtectedRoute>
              } />
              <Route path="/payment" element={
                <ProtectedRoute>
                  <Payment />
                </ProtectedRoute>
              } />
              <Route path="/payment/callback" element={
                <ProtectedRoute>
                  <PaymentCallback />
                </ProtectedRoute>
              } />

              {/* Role-specific Dashboard Routes */}
              <Route path="/patient-dashboard" element={
                <ProtectedRoute requiredRole="patient">
                  <PatientDashboardPage />
                </ProtectedRoute>
              } />
              <Route path="/doctor-dashboard" element={
                <ProtectedRoute requiredRole="doctor">
                  <DoctorDashboardPage />
                </ProtectedRoute>
              } />
              <Route path="/nurse-dashboard" element={
                <ProtectedRoute requiredRole="nurse">
                  <NurseDashboardPage />
                </ProtectedRoute>
              } />

              {/* Medical Staff Routes */}
              <Route path="/medical-dashboard" element={
                <ProtectedRoute>
                  <MedicalDashboard />
                </ProtectedRoute>
              } />
              <Route path="/appointments" element={
                <ProtectedRoute>
                  <Appointments />
                </ProtectedRoute>
              } />
              <Route path="/health-records" element={
                <ProtectedRoute>
                  <HealthRecords />
                </ProtectedRoute>
              } />

              {/* Admin Routes */}
              <Route path="/user-management" element={
                <ProtectedRoute requiredRole="admin">
                  <UserManagement />
                </ProtectedRoute>
              } />
              <Route path="/monitoring" element={
                <ProtectedRoute requiredRole="admin">
                  <Monitoring />
                </ProtectedRoute>
              } />
              <Route path="/settings" element={
                <ProtectedRoute requiredRole="admin">
                  <GlobalSettings />
                </ProtectedRoute>
              } />

              {/* Admin Routes */}
              <Route path="/admin" element={
                <ProtectedRoute requiredRole="admin">
                  <AdminLayout />
                </ProtectedRoute>
              }>
                <Route index element={<AdminDashboard />} />
                <Route path="users" element={<AdminUsers />} />
                <Route path="payments" element={<AdminPayments />} />
                <Route path="settings" element={<AdminSettings />} />
                <Route path="kyc" element={<AdminKYC />} />
                <Route path="withdrawals" element={<AdminWithdrawals />} />
                <Route path="penalties" element={<AdminPenalties />} />
              </Route>

              {/* Catch-all route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
            </BrowserRouter>
          </TooltipProvider>
        </AuthProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </HelmetProvider>
  );
};

export default App;
