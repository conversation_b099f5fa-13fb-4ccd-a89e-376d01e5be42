{"name": "medora-backend", "version": "1.0.0", "description": "MEDORA Backend - AI-Powered Medical Assistant API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "seed": "node scripts/createDemoUsers.js"}, "keywords": ["medical", "ai", "healthcare", "api", "nodejs", "express", "mongodb"], "author": "MEDORA Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0-rc.12", "chromadb": "^1.8.1", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.6.1", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-session": "^1.18.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "limiter": "^2.1.0", "lodash": "^4.17.21", "mammoth": "^1.6.0", "moment": "^2.29.4", "mongoose": "^8.18.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "openai": "^4.20.1", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-linkedin-oauth2": "^2.0.0", "passport-twitter": "^1.0.4", "pdf-parse": "^1.1.1", "playwright": "^1.40.1", "sharp": "^0.32.6", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^20.10.4", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.1.10"}, "engines": {"node": ">=16.0.0"}}