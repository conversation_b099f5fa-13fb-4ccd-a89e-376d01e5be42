import React from 'react';
import { motion } from 'framer-motion';
import HealthcareAuth from '@/components/auth/HealthcareAuth';
import { useAuth } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';

const NurseLogin: React.FC = () => {
  const { user, isAuthenticated } = useAuth();

  // Redirect if already authenticated as nurse
  if (isAuthenticated && user?.role === 'nurse') {
    return <Navigate to="/nurse-dashboard" replace />;
  }

  // Redirect if authenticated as different role
  if (isAuthenticated && user?.role !== 'nurse') {
    const redirectPath = user?.role === 'patient' ? '/patient-dashboard' : 
                        user?.role === 'doctor' ? '/doctor-dashboard' : 
                        user?.role === 'admin' ? '/admin-dashboard' : '/';
    return <Navigate to={redirectPath} replace />;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <HealthcareAuth 
        userType="nurse"
        redirectTo="/nurse-dashboard"
        onSuccess={() => {
          // Handle successful authentication
          console.log('Nurse authentication successful');
        }}
      />
    </motion.div>
  );
};

export default NurseLogin;
