import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  User, 
  Heart, 
  Pill, 
  FileText, 
  Calendar, 
  AlertTriangle, 
  TrendingUp,
  Plus,
  Edit,
  Eye,
  Download
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiClient } from '@/services/api';
import LabTestUpload from './LabTestUpload';

interface PatientRecord {
  _id: string;
  mrn: string;
  userId: {
    firstName: string;
    lastName: string;
    email: string;
    dateOfBirth: string;
    gender: string;
    phoneNumber: string;
  };
  medicalHistory: {
    allergies: Array<{
      allergen: string;
      reaction: string;
      severity: string;
      notes: string;
    }>;
    chronicConditions: Array<{
      condition: string;
      diagnosedDate: string;
      status: string;
      notes: string;
    }>;
    surgicalHistory: Array<{
      procedure: string;
      date: string;
      surgeon: string;
      hospital: string;
      complications: string;
      notes: string;
    }>;
    familyHistory: Array<{
      relationship: string;
      condition: string;
      ageAtDiagnosis: number;
      notes: string;
    }>;
  };
  medications: Array<{
    name: string;
    dosage: string;
    frequency: string;
    route: string;
    isActive: boolean;
    prescribedAt: string;
    startDate: string;
    endDate?: string;
    instructions: string;
  }>;
  vitalSigns: Array<{
    temperature?: { value: number; unit: string };
    bloodPressure?: { systolic: number; diastolic: number };
    heartRate?: { value: number };
    weight?: { value: number; unit: string };
    height?: { value: number; unit: string };
    bmi?: number;
    recordedAt: string;
  }>;
  labResults: Array<{
    testName: string;
    category: string;
    results: Array<{
      parameter: string;
      value: string;
      unit: string;
      referenceRange: string;
      status: string;
    }>;
    performedAt: string;
    status: string;
    notes: string;
  }>;
  appointments: Array<{
    scheduledAt: string;
    type: string;
    status: string;
    reason: string;
    notes: string;
  }>;
}

interface MedicalRecordsProps {
  patientId: string;
}

const MedicalRecords: React.FC<MedicalRecordsProps> = ({ patientId }) => {
  const [patient, setPatient] = useState<PatientRecord | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [editMode, setEditMode] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchPatientRecord();
  }, [patientId, fetchPatientRecord]);

  const fetchPatientRecord = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClient.get(`/api/patients/${patientId}`);
      setPatient((response as unknown).data.data.patient);
    } catch (error: unknown) {
      toast({
        title: "Failed to load patient record",
        description: error.response?.data?.message || "Unable to fetch patient data.",
        variant: "destructive"
      });
          } finally {
        setLoading(false);
      }
    }, [patientId, toast]);

  const updatePatientRecord = async (section: string, data: unknown) => {
    try {
      const updateData = { [section]: data };
      await api.put(`/api/patients/${patientId}`, updateData);
      
      toast({
        title: "Record updated",
        description: `${section} has been updated successfully.`
      });
      
      await fetchPatientRecord();
      setEditMode(null);
    } catch (error: unknown) {
      toast({
        title: "Update failed",
        description: error.response?.data?.message || "Failed to update patient record.",
        variant: "destructive"
      });
    }
  };

  const getStatusBadge = (status: string, type: 'priority' | 'status' | 'severity' = 'status') => {
    const variants: Record<string, unknown> = {
      priority: {
        low: 'secondary',
        medium: 'default',
        high: 'destructive',
        emergency: 'destructive'
      },
      status: {
        active: 'default',
        inactive: 'secondary',
        completed: 'secondary',
        pending: 'outline',
        normal: 'secondary',
        abnormal: 'destructive',
        critical: 'destructive'
      },
      severity: {
        mild: 'secondary',
        moderate: 'default',
        severe: 'destructive',
        life_threatening: 'destructive'
      }
    };

    return (
      <Badge variant={variants[type][status] || 'outline'}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading patient record...</p>
        </div>
      </div>
    );
  }

  if (!patient) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-600">Patient record not found</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Patient Header */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-xl">
                  {patient.userId.firstName} {patient.userId.lastName}
                </CardTitle>
                <CardDescription>
                  MRN: {patient.mrn} • DOB: {new Date(patient.userId.dateOfBirth).toLocaleDateString()}
                </CardDescription>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Medical Records Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3 md:grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="medications">Medications</TabsTrigger>
          <TabsTrigger value="vitals">Vitals</TabsTrigger>
          <TabsTrigger value="labs">Lab Tests</TabsTrigger>
          <TabsTrigger value="appointments">Appointments</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Quick Stats</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Age:</span>
                  <span className="text-sm font-medium">
                    {Math.floor((Date.now() - new Date(patient.userId.dateOfBirth).getTime()) / (365.25 * 24 * 60 * 60 * 1000))} years
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Gender:</span>
                  <span className="text-sm font-medium">{patient.userId.gender}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Active Medications:</span>
                  <span className="text-sm font-medium">
                    {patient.medications.filter(m => m.isActive).length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Lab Tests:</span>
                  <span className="text-sm font-medium">{patient.labResults.length}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Allergies
                </CardTitle>
              </CardHeader>
              <CardContent>
                {patient.medicalHistory.allergies.length > 0 ? (
                  <div className="space-y-2">
                    {patient.medicalHistory.allergies.slice(0, 3).map((allergy, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm">{allergy.allergen}</span>
                        {getStatusBadge(allergy.severity, 'severity')}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No known allergies</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Latest Vitals
                </CardTitle>
              </CardHeader>
              <CardContent>
                {patient.vitalSigns.length > 0 ? (
                  <div className="space-y-2">
                    {patient.vitalSigns[patient.vitalSigns.length - 1].bloodPressure && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">BP:</span>
                        <span className="text-sm font-medium">
                          {patient.vitalSigns[patient.vitalSigns.length - 1].bloodPressure?.systolic}/
                          {patient.vitalSigns[patient.vitalSigns.length - 1].bloodPressure?.diastolic}
                        </span>
                      </div>
                    )}
                    {patient.vitalSigns[patient.vitalSigns.length - 1].heartRate && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">HR:</span>
                        <span className="text-sm font-medium">
                          {patient.vitalSigns[patient.vitalSigns.length - 1].heartRate?.value} bpm
                        </span>
                      </div>
                    )}
                    {patient.vitalSigns[patient.vitalSigns.length - 1].temperature && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Temp:</span>
                        <span className="text-sm font-medium">
                          {patient.vitalSigns[patient.vitalSigns.length - 1].temperature?.value}°
                          {patient.vitalSigns[patient.vitalSigns.length - 1].temperature?.unit === 'celsius' ? 'C' : 'F'}
                        </span>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No vital signs recorded</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Medical History Tab */}
        <TabsContent value="history" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Chronic Conditions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Chronic Conditions</CardTitle>
                <Button variant="outline" size="sm" className="w-fit">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Condition
                </Button>
              </CardHeader>
              <CardContent>
                {patient.medicalHistory.chronicConditions.length > 0 ? (
                  <div className="space-y-3">
                    {patient.medicalHistory.chronicConditions.map((condition, index) => (
                      <div key={index} className="border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{condition.condition}</h4>
                          {getStatusBadge(condition.status)}
                        </div>
                        <p className="text-sm text-gray-600">
                          Diagnosed: {new Date(condition.diagnosedDate).toLocaleDateString()}
                        </p>
                        {condition.notes && (
                          <p className="text-sm text-gray-700 mt-1">{condition.notes}</p>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No chronic conditions recorded</p>
                )}
              </CardContent>
            </Card>

            {/* Surgical History */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Surgical History</CardTitle>
                <Button variant="outline" size="sm" className="w-fit">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Surgery
                </Button>
              </CardHeader>
              <CardContent>
                {patient.medicalHistory.surgicalHistory.length > 0 ? (
                  <div className="space-y-3">
                    {patient.medicalHistory.surgicalHistory.map((surgery, index) => (
                      <div key={index} className="border rounded-lg p-3">
                        <h4 className="font-medium">{surgery.procedure}</h4>
                        <p className="text-sm text-gray-600">
                          {new Date(surgery.date).toLocaleDateString()} • {surgery.surgeon}
                        </p>
                        <p className="text-sm text-gray-600">{surgery.hospital}</p>
                        {surgery.complications && (
                          <p className="text-sm text-red-600 mt-1">
                            Complications: {surgery.complications}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No surgical history recorded</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Medications Tab */}
        <TabsContent value="medications" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Pill className="h-5 w-5" />
                    Current Medications
                  </CardTitle>
                  <CardDescription>Active prescriptions and medication history</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Medication
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {patient.medications.filter(m => m.isActive).length > 0 ? (
                <div className="space-y-4">
                  {patient.medications.filter(m => m.isActive).map((medication, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{medication.name}</h4>
                        <Badge variant="default">Active</Badge>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Dosage:</span>
                          <p className="font-medium">{medication.dosage}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Frequency:</span>
                          <p className="font-medium">{medication.frequency}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Route:</span>
                          <p className="font-medium">{medication.route}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Started:</span>
                          <p className="font-medium">
                            {new Date(medication.startDate).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      {medication.instructions && (
                        <p className="text-sm text-gray-700 mt-2">{medication.instructions}</p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No active medications</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Vital Signs Tab */}
        <TabsContent value="vitals" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Heart className="h-5 w-5" />
                    Vital Signs History
                  </CardTitle>
                  <CardDescription>Track patient vital signs over time</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Vitals
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {patient.vitalSigns.length > 0 ? (
                <div className="space-y-4">
                  {patient.vitalSigns.slice(-5).reverse().map((vitals, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-sm font-medium">
                          {new Date(vitals.recordedAt).toLocaleDateString()} at{' '}
                          {new Date(vitals.recordedAt).toLocaleTimeString()}
                        </span>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        {vitals.bloodPressure && (
                          <div>
                            <span className="text-gray-600">Blood Pressure:</span>
                            <p className="font-medium">
                              {vitals.bloodPressure.systolic}/{vitals.bloodPressure.diastolic} mmHg
                            </p>
                          </div>
                        )}
                        {vitals.heartRate && (
                          <div>
                            <span className="text-gray-600">Heart Rate:</span>
                            <p className="font-medium">{vitals.heartRate.value} bpm</p>
                          </div>
                        )}
                        {vitals.temperature && (
                          <div>
                            <span className="text-gray-600">Temperature:</span>
                            <p className="font-medium">
                              {vitals.temperature.value}°{vitals.temperature.unit === 'celsius' ? 'C' : 'F'}
                            </p>
                          </div>
                        )}
                        {vitals.weight && (
                          <div>
                            <span className="text-gray-600">Weight:</span>
                            <p className="font-medium">{vitals.weight.value} {vitals.weight.unit}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No vital signs recorded</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Lab Tests Tab */}
        <TabsContent value="labs" className="space-y-4">
          <LabTestUpload 
            patientId={patientId} 
            onTestUploaded={() => fetchPatientRecord()} 
          />
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Lab Test History
              </CardTitle>
            </CardHeader>
            <CardContent>
              {patient.labResults.length > 0 ? (
                <div className="space-y-4">
                  {patient.labResults.slice(-5).reverse().map((lab, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium">{lab.testName}</h4>
                          <p className="text-sm text-gray-600">
                            {new Date(lab.performedAt).toLocaleDateString()} • 
                            {lab.category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </p>
                        </div>
                        {getStatusBadge(lab.status)}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {lab.results.slice(0, 6).map((result, resultIndex) => (
                          <div key={resultIndex} className="text-sm">
                            <span className="text-gray-600">{result.parameter}:</span>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{result.value} {result.unit}</span>
                              {getStatusBadge(result.status)}
                            </div>
                            <span className="text-xs text-gray-500">Ref: {result.referenceRange}</span>
                          </div>
                        ))}
                      </div>
                      {lab.notes && (
                        <p className="text-sm text-gray-700 mt-3 p-2 bg-gray-50 rounded">
                          {lab.notes}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No lab tests recorded</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Appointments Tab */}
        <TabsContent value="appointments" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Appointments
                  </CardTitle>
                  <CardDescription>Upcoming and past appointments</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Schedule Appointment
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {patient.appointments.length > 0 ? (
                <div className="space-y-4">
                  {patient.appointments.slice(-5).reverse().map((appointment, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <h4 className="font-medium">{appointment.type.replace('_', ' ')}</h4>
                          <p className="text-sm text-gray-600">
                            {new Date(appointment.scheduledAt).toLocaleDateString()} at{' '}
                            {new Date(appointment.scheduledAt).toLocaleTimeString()}
                          </p>
                        </div>
                        {getStatusBadge(appointment.status)}
                      </div>
                      {appointment.reason && (
                        <p className="text-sm text-gray-700">Reason: {appointment.reason}</p>
                      )}
                      {appointment.notes && (
                        <p className="text-sm text-gray-700 mt-2 p-2 bg-gray-50 rounded">
                          {appointment.notes}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No appointments scheduled</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MedicalRecords;
