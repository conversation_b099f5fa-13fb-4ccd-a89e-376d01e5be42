# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
backend/.env
backend/.env.local
backend/.env.development.local
backend/.env.test.local
backend/.env.production.local

# Database
*.db
*.sqlite
*.sqlite3

# Uploads and user content
uploads/
public/uploads/
backend/uploads/

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Testing
coverage/
.nyc_output/
junit.xml

# Cache
.cache/
.eslintcache
.stylelintcache

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Backup files
*.bak
*.backup
*.old

.vercel
