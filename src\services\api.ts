// Base API configuration and client
import config from '../config';

const API_BASE_URL = config.api.baseUrl;

interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  status?: number;
}

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('authToken');
  }

  setToken(token: string | null) {
    this.token = token;
    if (token) {
      localStorage.setItem('authToken', token);
    } else {
      localStorage.removeItem('authToken');
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      ...(options.body instanceof FormData ? {} : { 'Content-Type': 'application/json' }),
      ...options.headers,
    };

    if (this.token) {
      (headers as Record<string, string>).Authorization = `Bearer ${this.token}`;
    }

    // Debug logging
    console.log('🔗 API Request:', {
      url,
      method: options.method || 'GET',
      headers,
      hasBody: !!options.body,
      baseURL: this.baseURL
    });

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      console.log('📡 API Response:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        url: response.url
      });

      let data: any;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('Failed to parse JSON response:', jsonError);
        data = { message: 'Invalid server response' };
      }

      console.log('📡 API Response Data:', data);

      if (!response.ok) {
        console.error('❌ API Error Response:', data);
        // Return error response instead of throwing
        return {
          success: false,
          error: data.message || `HTTP error! status: ${response.status}`,
          status: response.status,
          data: data
        };
      }

      console.log('✅ API Success Response:', data);
      return data;
    } catch (error) {
      console.error('💥 API request failed:', {
        error,
        url,
        endpoint,
        baseURL: this.baseURL,
        message: error instanceof Error ? error.message : 'Unknown error'
      });

      // Return error response instead of throwing
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error occurred',
        data: null
      };
    }
  }

  async get<T>(endpoint: string, config?: { headers?: Record<string, string> }): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET', headers: config?.headers });
  }

  async post<T>(endpoint: string, data?: unknown, config?: { headers?: Record<string, string> }): Promise<ApiResponse<T>> {
    const isFormData = data instanceof FormData;
    
    return this.request<T>(endpoint, {
      method: 'POST',
      body: isFormData ? data : (data ? JSON.stringify(data) : undefined),
      headers: isFormData ? config?.headers : undefined,
    });
  }

  async put<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  async patch<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
}

// Create and export the API client instance
export const apiClient = new ApiClient(API_BASE_URL);
export type { ApiResponse };