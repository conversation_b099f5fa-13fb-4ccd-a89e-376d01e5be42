const mongoose = require('mongoose');

// Sub-schema for PubMed articles
const pubmedArticleSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
  },
  authors: {
    type: String,
    trim: true,
  },
  journal: {
    type: String,
    trim: true,
  },
  abstract: {
    type: String,
    trim: true,
  },
  pmid: {
    type: String,
    trim: true,
  },
  url: {
    type: String,
    trim: true,
  },
  publicationDate: {
    type: Date,
  },
}, { _id: false });

// Sub-schema for drug information
const drugInfoSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  genericName: {
    type: String,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  uses: {
    type: String,
    trim: true,
  },
  sideEffects: {
    type: String,
    trim: true,
  },
  warnings: {
    type: String,
    trim: true,
  },
  dosage: {
    type: String,
    trim: true,
  },
  interactions: {
    type: String,
    trim: true,
  },
  contraindications: {
    type: [String],
    default: [],
  },
  pregnancyCategory: {
    type: String,
    enum: ['A', 'B', 'C', 'D', 'X', 'N'],
  },
}, { _id: false });

// Sub-schema for medical news
const medicalNewsSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
  },
  summary: {
    type: String,
    trim: true,
  },
  content: {
    type: String,
    trim: true,
  },
  url: {
    type: String,
    trim: true,
  },
  publishedAt: {
    type: Date,
  },
  category: {
    type: String,
    enum: ['research', 'treatment', 'prevention', 'technology', 'policy', 'other'],
    default: 'other',
  },
  relevanceScore: {
    type: Number,
    min: 0,
    max: 1,
    default: 0.5,
  },
}, { _id: false });

// Sub-schema for disease information
const diseaseInfoSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  overview: {
    type: String,
    trim: true,
  },
  symptoms: {
    type: String,
    trim: true,
  },
  causes: {
    type: String,
    trim: true,
  },
  riskFactors: {
    type: String,
    trim: true,
  },
  complications: {
    type: String,
    trim: true,
  },
  prevention: {
    type: String,
    trim: true,
  },
  diagnosis: {
    type: String,
    trim: true,
  },
  treatment: {
    type: String,
    trim: true,
  },
  prognosis: {
    type: String,
    trim: true,
  },
  icdCodes: {
    type: [String],
    default: [],
  },
  prevalence: {
    type: String,
    trim: true,
  },
}, { _id: false });

// Sub-schema for clinical trials
const clinicalTrialSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
  },
  nctId: {
    type: String,
    trim: true,
  },
  status: {
    type: String,
    enum: ['recruiting', 'active', 'completed', 'suspended', 'terminated', 'withdrawn'],
    default: 'recruiting',
  },
  condition: {
    type: String,
    trim: true,
  },
  intervention: {
    type: String,
    trim: true,
  },
  location: {
    type: String,
    trim: true,
  },
  sponsor: {
    type: String,
    trim: true,
  },
  phase: {
    type: String,
    enum: ['Phase 1', 'Phase 2', 'Phase 3', 'Phase 4', 'Not Applicable'],
  },
  enrollmentCount: {
    type: Number,
    min: 0,
  },
  startDate: {
    type: Date,
  },
  completionDate: {
    type: Date,
  },
  url: {
    type: String,
    trim: true,
  },
}, { _id: false });

// Sub-schema for medical guidelines
const medicalGuidelineSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
  },
  summary: {
    type: String,
    trim: true,
  },
  organization: {
    type: String,
    trim: true,
  },
  url: {
    type: String,
    trim: true,
  },
  publicationDate: {
    type: Date,
  },
  lastUpdated: {
    type: Date,
  },
  evidenceLevel: {
    type: String,
    enum: ['A', 'B', 'C', 'D', 'Expert Opinion'],
  },
  specialty: {
    type: String,
    trim: true,
  },
}, { _id: false });

// Main scraped data schema
const scrapedDataSchema = new mongoose.Schema({
  query: {
    type: String,
    required: true,
    trim: true,
    index: true,
  },
  dataType: {
    type: String,
    required: true,
    enum: ['pubmed', 'drug_info', 'medical_news', 'disease_info', 'clinical_trials', 'guidelines', 'comprehensive'],
    index: true,
  },
  source: {
    type: String,
    required: true,
    trim: true,
    index: true,
  },
  
  // Data fields based on type
  pubmedArticles: [pubmedArticleSchema],
  drugInfo: drugInfoSchema,
  medicalNews: [medicalNewsSchema],
  diseaseInfo: diseaseInfoSchema,
  clinicalTrials: [clinicalTrialSchema],
  guidelines: [medicalGuidelineSchema],
  
  // Comprehensive data (when multiple sources are scraped)
  comprehensiveData: {
    research: [pubmedArticleSchema],
    news: [medicalNewsSchema],
    diseaseInfo: diseaseInfoSchema,
    clinicalTrials: [clinicalTrialSchema],
    guidelines: [medicalGuidelineSchema],
  },
  
  // Metadata
  scrapedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  scrapedAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
  processingTime: {
    type: Number, // in milliseconds
    min: 0,
  },
  
  // Quality metrics
  qualityScore: {
    type: Number,
    min: 0,
    max: 1,
    default: 0.5,
  },
  relevanceScore: {
    type: Number,
    min: 0,
    max: 1,
    default: 0.5,
  },
  
  // Status and validation
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'validated'],
    default: 'completed',
    index: true,
  },
  validatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  validatedAt: {
    type: Date,
  },
  
  // Error handling
  processingErrors: [{
    source: String,
    message: String,
    timestamp: {
      type: Date,
      default: Date.now,
    },
  }],
  
  // Usage tracking
  accessCount: {
    type: Number,
    default: 0,
    min: 0,
  },
  lastAccessed: {
    type: Date,
  },
  
  // Expiration and caching
  expiresAt: {
    type: Date,
    index: { expireAfterSeconds: 0 },
  },
  cacheKey: {
    type: String,
    unique: true,
    sparse: true,
  },
  
  // Tags and categorization
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
  }],
  category: {
    type: String,
    enum: ['diagnosis', 'treatment', 'research', 'drug_info', 'guidelines', 'news', 'trials'],
    index: true,
  },
  
  // Related data
  relatedPatients: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
  }],
  relatedDiagnoses: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Diagnosis',
  }],
  
  // Privacy and compliance
  containsPHI: {
    type: Boolean,
    default: false,
  },
  dataRetentionPolicy: {
    type: String,
    enum: ['30_days', '90_days', '1_year', '5_years', 'indefinite'],
    default: '1_year',
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for performance
scrapedDataSchema.index({ query: 'text', 'drugInfo.name': 'text', 'diseaseInfo.name': 'text' });
scrapedDataSchema.index({ scrapedBy: 1, scrapedAt: -1 });
scrapedDataSchema.index({ dataType: 1, source: 1, scrapedAt: -1 });
scrapedDataSchema.index({ status: 1, scrapedAt: -1 });
scrapedDataSchema.index({ category: 1, tags: 1 });
scrapedDataSchema.index({ 'relatedPatients': 1 });
scrapedDataSchema.index({ 'relatedDiagnoses': 1 });

// Virtual for age of data
scrapedDataSchema.virtual('dataAge').get(function() {
  return Date.now() - this.scrapedAt.getTime();
});

// Virtual for data freshness (0-1 scale)
scrapedDataSchema.virtual('freshness').get(function() {
  const ageInDays = this.dataAge / (1000 * 60 * 60 * 24);
  const maxFreshDays = 30; // Consider data stale after 30 days
  return Math.max(0, 1 - (ageInDays / maxFreshDays));
});

// Virtual for total items count
scrapedDataSchema.virtual('totalItems').get(function() {
  let count = 0;
  if (this.pubmedArticles) count += this.pubmedArticles.length;
  if (this.medicalNews) count += this.medicalNews.length;
  if (this.clinicalTrials) count += this.clinicalTrials.length;
  if (this.guidelines) count += this.guidelines.length;
  if (this.drugInfo) count += 1;
  if (this.diseaseInfo) count += 1;
  return count;
});

// Pre-save middleware
scrapedDataSchema.pre('save', function(next) {
  // Generate cache key
  if (!this.cacheKey) {
    this.cacheKey = `${this.dataType}_${this.query}_${this.source}`.replace(/\s+/g, '_').toLowerCase();
  }
  
  // Set expiration based on data retention policy
  if (!this.expiresAt) {
    const now = new Date();
    switch (this.dataRetentionPolicy) {
      case '30_days':
        this.expiresAt = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
        break;
      case '90_days':
        this.expiresAt = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
        break;
      case '1_year':
        this.expiresAt = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
        break;
      case '5_years':
        this.expiresAt = new Date(now.getTime() + 5 * 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        // indefinite - no expiration
        this.expiresAt = undefined;
    }
  }
  
  // Auto-categorize based on data type
  if (!this.category) {
    switch (this.dataType) {
      case 'drug_info':
        this.category = 'drug_info';
        break;
      case 'disease_info':
        this.category = 'diagnosis';
        break;
      case 'clinical_trials':
        this.category = 'trials';
        break;
      case 'medical_news':
        this.category = 'news';
        break;
      case 'guidelines':
        this.category = 'guidelines';
        break;
      case 'pubmed':
        this.category = 'research';
        break;
      default:
        this.category = 'research';
    }
  }
  
  next();
});

// Instance methods
scrapedDataSchema.methods.incrementAccess = function() {
  this.accessCount += 1;
  this.lastAccessed = new Date();
  return this.save();
};

scrapedDataSchema.methods.validateData = function(userId) {
  this.status = 'validated';
  this.validatedBy = userId;
  this.validatedAt = new Date();
  return this.save();
};

scrapedDataSchema.methods.addError = function(source, message) {
  this.processingErrors.push({ source, message });
  if (this.status === 'completed') {
    this.status = 'failed';
  }
  return this.save();
};

scrapedDataSchema.methods.isExpired = function() {
  return this.expiresAt && new Date() > this.expiresAt;
};

scrapedDataSchema.methods.isFresh = function(maxAgeInDays = 7) {
  const ageInDays = this.dataAge / (1000 * 60 * 60 * 24);
  return ageInDays <= maxAgeInDays;
};

// Static methods
scrapedDataSchema.statics.findByQuery = function(query, dataType = null) {
  const searchCriteria = { query: new RegExp(query, 'i') };
  if (dataType) {
    searchCriteria.dataType = dataType;
  }
  return this.find(searchCriteria).sort({ scrapedAt: -1 });
};

scrapedDataSchema.statics.findFreshData = function(query, dataType, maxAgeInDays = 7) {
  const cutoffDate = new Date(Date.now() - maxAgeInDays * 24 * 60 * 60 * 1000);
  return this.findOne({
    query: new RegExp(query, 'i'),
    dataType,
    scrapedAt: { $gte: cutoffDate },
    status: { $in: ['completed', 'validated'] },
  }).sort({ scrapedAt: -1 });
};

scrapedDataSchema.statics.getUsageStats = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        scrapedAt: {
          $gte: startDate,
          $lte: endDate,
        },
      },
    },
    {
      $group: {
        _id: {
          dataType: '$dataType',
          source: '$source',
        },
        count: { $sum: 1 },
        totalAccess: { $sum: '$accessCount' },
        avgQualityScore: { $avg: '$qualityScore' },
        avgRelevanceScore: { $avg: '$relevanceScore' },
      },
    },
    {
      $sort: { count: -1 },
    },
  ]);
};

scrapedDataSchema.statics.cleanupExpiredData = function() {
  return this.deleteMany({
    expiresAt: { $lt: new Date() },
  });
};

module.exports = mongoose.model('ScrapedData', scrapedDataSchema);