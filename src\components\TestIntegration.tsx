import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, Activity } from 'lucide-react';
import { useAuth } from '../hooks/useAuth.tsx';
import { usePatients } from '../hooks/usePatients';
import { useDiagnosis } from '../hooks/useDiagnosis';
import { useMLAnalysis } from '../hooks/useML';
import { apiClient } from '../services/api';

const TestIntegration: React.FC = () => {
  const [testResults, setTestResults] = useState<{
    backend: 'pending' | 'success' | 'error';
    auth: 'pending' | 'success' | 'error';
    patients: 'pending' | 'success' | 'error';
    diagnosis: 'pending' | 'success' | 'error';
    ml: 'pending' | 'success' | 'error';
  }>({
    backend: 'pending',
    auth: 'pending',
    patients: 'pending',
    diagnosis: 'pending',
    ml: 'pending'
  });

  const [testMessages, setTestMessages] = useState<{
    backend: string;
    auth: string;
    patients: string;
    diagnosis: string;
    ml: string;
  }>({
    backend: '',
    auth: '',
    patients: '',
    diagnosis: '',
    ml: ''
  });

  const [isRunning, setIsRunning] = useState(false);
  const { user, isAuthenticated } = useAuth();

  const updateTestResult = (test: keyof typeof testResults, status: 'success' | 'error', message: string) => {
    setTestResults(prev => ({ ...prev, [test]: status }));
    setTestMessages(prev => ({ ...prev, [test]: message }));
  };

  const testBackendConnection = async () => {
    try {
      const response = await apiClient.get('/health');
      if (response.success) {
        updateTestResult('backend', 'success', 'Backend server is running and accessible');
      } else {
        updateTestResult('backend', 'error', 'Backend responded but with error');
      }
    } catch (error) {
      updateTestResult('backend', 'error', `Failed to connect to backend: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const testAuthService = async () => {
    try {
      // Test if auth service can check current user status
      const response = await apiClient.get('/auth/me');
      if (response.success) {
        updateTestResult('auth', 'success', 'Authentication service is working');
      } else {
        updateTestResult('auth', 'success', 'Auth service accessible (not logged in)');
      }
    } catch (error) {
      // If it's an authentication error, that means the service is working
      if (error instanceof Error && error.message.includes('Access token required')) {
        updateTestResult('auth', 'success', 'Auth service accessible (authentication required)');
      } else {
        updateTestResult('auth', 'error', `Auth service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  };

  const testPatientsService = async () => {
    try {
      const response = await apiClient.get('/patients?page=1&limit=1');
      if (response.success) {
        updateTestResult('patients', 'success', 'Patients service is working');
      } else {
        updateTestResult('patients', 'error', 'Patients service responded with error');
      }
    } catch (error) {
      // If it's an authentication error, that means the service is working
      if (error instanceof Error && (error.message.includes('Access token required') || error.message.includes('Unauthorized'))) {
        updateTestResult('patients', 'success', 'Patients service accessible (authentication required)');
      } else {
        updateTestResult('patients', 'error', `Patients service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  };

  const testDiagnosisService = async () => {
    try {
      const response = await apiClient.get('/diagnosis?page=1&limit=1');
      if (response.success) {
        updateTestResult('diagnosis', 'success', 'Diagnosis service is working');
      } else {
        updateTestResult('diagnosis', 'error', 'Diagnosis service responded with error');
      }
    } catch (error) {
      // If it's an authentication error, that means the service is working
      if (error instanceof Error && (error.message.includes('Access token required') || error.message.includes('Unauthorized'))) {
        updateTestResult('diagnosis', 'success', 'Diagnosis service accessible (authentication required)');
      } else {
        updateTestResult('diagnosis', 'error', `Diagnosis service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  };

  const testMLService = async () => {
    try {
      const response = await apiClient.get('/ml/models/status');
      if (response.success) {
        updateTestResult('ml', 'success', 'ML service is working');
      } else {
        updateTestResult('ml', 'error', 'ML service responded with error');
      }
    } catch (error) {
      updateTestResult('ml', 'error', `ML service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    
    // Reset all tests to pending
    setTestResults({
      backend: 'pending',
      auth: 'pending',
      patients: 'pending',
      diagnosis: 'pending',
      ml: 'pending'
    });

    // Run tests sequentially
    await testBackendConnection();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testAuthService();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testPatientsService();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testDiagnosisService();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testMLService();
    
    setIsRunning(false);
  };

  const getStatusIcon = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'pending':
        return <Loader2 className="h-4 w-4 animate-spin text-yellow-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusColor = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600';
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Frontend-Backend Integration Test
          </CardTitle>
          <CardDescription>
            Test the connection and functionality between the frontend and backend services
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Authentication Status</h3>
              <p className="text-sm text-muted-foreground">
                {isAuthenticated ? `Logged in as: ${user?.email || 'Unknown'}` : 'Not authenticated'}
              </p>
            </div>
            <Button onClick={runAllTests} disabled={isRunning}>
              {isRunning ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Running Tests...
                </>
              ) : (
                'Run Integration Tests'
              )}
            </Button>
          </div>

          <div className="grid gap-4">
            {/* Backend Connection Test */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                {getStatusIcon(testResults.backend)}
                <div>
                  <h4 className="font-medium">Backend Connection</h4>
                  <p className="text-sm text-muted-foreground">Health check endpoint</p>
                </div>
              </div>
              <div className={`text-sm ${getStatusColor(testResults.backend)}`}>
                {testMessages.backend || 'Not tested'}
              </div>
            </div>

            {/* Auth Service Test */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                {getStatusIcon(testResults.auth)}
                <div>
                  <h4 className="font-medium">Authentication Service</h4>
                  <p className="text-sm text-muted-foreground">User authentication endpoints</p>
                </div>
              </div>
              <div className={`text-sm ${getStatusColor(testResults.auth)}`}>
                {testMessages.auth || 'Not tested'}
              </div>
            </div>

            {/* Patients Service Test */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                {getStatusIcon(testResults.patients)}
                <div>
                  <h4 className="font-medium">Patients Service</h4>
                  <p className="text-sm text-muted-foreground">Patient management endpoints</p>
                </div>
              </div>
              <div className={`text-sm ${getStatusColor(testResults.patients)}`}>
                {testMessages.patients || 'Not tested'}
              </div>
            </div>

            {/* Diagnosis Service Test */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                {getStatusIcon(testResults.diagnosis)}
                <div>
                  <h4 className="font-medium">Diagnosis Service</h4>
                  <p className="text-sm text-muted-foreground">Medical diagnosis endpoints</p>
                </div>
              </div>
              <div className={`text-sm ${getStatusColor(testResults.diagnosis)}`}>
                {testMessages.diagnosis || 'Not tested'}
              </div>
            </div>

            {/* ML Service Test */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                {getStatusIcon(testResults.ml)}
                <div>
                  <h4 className="font-medium">Machine Learning Service</h4>
                  <p className="text-sm text-muted-foreground">AI analysis endpoints</p>
                </div>
              </div>
              <div className={`text-sm ${getStatusColor(testResults.ml)}`}>
                {testMessages.ml || 'Not tested'}
              </div>
            </div>
          </div>

          {/* Overall Status */}
          <Alert>
            <AlertDescription>
              <strong>Integration Status:</strong> {' '}
              {Object.values(testResults).every(status => status === 'success') && !isRunning
                ? 'All services are working correctly! 🎉'
                : Object.values(testResults).some(status => status === 'error') && !isRunning
                ? 'Some services have issues. Check the details above.'
                : isRunning
                ? 'Testing in progress...'
                : 'Click "Run Integration Tests" to check all services.'}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestIntegration;