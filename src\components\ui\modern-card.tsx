import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardProps } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface ModernCardProps extends CardProps {
  glassmorphism?: boolean;
  hover3d?: boolean;
  glow?: boolean;
  gradient?: boolean;
  borderGlow?: boolean;
  interactive?: boolean;
  delay?: number;
}

const ModernCard = React.forwardRef<HTMLDivElement, ModernCardProps>(
  ({ 
    className, 
    glassmorphism = false,
    hover3d = false,
    glow = false,
    gradient = false,
    borderGlow = false,
    interactive = true,
    delay = 0,
    children,
    ...props 
  }, ref) => {
    const [mousePosition, setMousePosition] = React.useState({ x: 0, y: 0 });
    const [isHovered, setIsHovered] = React.useState(false);

    const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
      if (!hover3d) return;
      
      const rect = e.currentTarget.getBoundingClientRect();
      const x = (e.clientX - rect.left - rect.width / 2) / rect.width;
      const y = (e.clientY - rect.top - rect.height / 2) / rect.height;
      
      setMousePosition({ x: x * 20, y: y * 20 });
    };

    const handleMouseEnter = () => {
      setIsHovered(true);
    };

    const handleMouseLeave = () => {
      setIsHovered(false);
      setMousePosition({ x: 0, y: 0 });
    };

    const cardVariants = {
      hidden: { 
        opacity: 0, 
        y: 20,
        scale: 0.95
      },
      visible: { 
        opacity: 1, 
        y: 0,
        scale: 1,
        transition: {
          duration: 0.6,
          delay: delay,
          ease: [0.25, 0.46, 0.45, 0.94]
        }
      },
      hover: {
        y: hover3d ? -8 : -4,
        scale: interactive ? 1.02 : 1,
        rotateX: hover3d ? mousePosition.y * 0.5 : 0,
        rotateY: hover3d ? mousePosition.x * 0.5 : 0,
        transition: {
          duration: 0.3,
          ease: "easeOut"
        }
      }
    };

    const glowVariants = {
      initial: { 
        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)" 
      },
      hover: { 
        boxShadow: glow 
          ? "0 20px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04), 0 0 0 1px rgba(59, 130, 246, 0.05)"
          : "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
        transition: { duration: 0.3 }
      }
    };

    const borderGlowVariants = {
      initial: { 
        background: "transparent"
      },
      hover: {
        background: borderGlow 
          ? "linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%)"
          : "transparent",
        transition: { duration: 0.3 }
      }
    };

    return (
      <motion.div
        className="relative group"
        variants={cardVariants}
        initial="hidden"
        whileInView="visible"
        whileHover="hover"
        viewport={{ once: true, margin: "-100px" }}
        onMouseMove={handleMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        style={{
          perspective: hover3d ? "1000px" : "none",
          transformStyle: hover3d ? "preserve-3d" : "flat"
        }}
      >
        {/* Border Glow Effect */}
        {borderGlow && (
          <motion.div
            className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            variants={borderGlowVariants}
            style={{
              background: "linear-gradient(90deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 51, 234, 0.2) 100%)",
              padding: "1px",
            }}
          >
            <div className="w-full h-full bg-background rounded-lg" />
          </motion.div>
        )}

        <motion.div variants={glowVariants}>
          <Card
            ref={ref}
            className={cn(
              "relative transition-all duration-300 border-border/50",
              glassmorphism && "bg-background/80 backdrop-blur-xl border-white/10",
              gradient && "bg-gradient-to-br from-background to-background/50",
              hover3d && "transform-gpu",
              interactive && "cursor-pointer",
              glow && "shadow-lg",
              className
            )}
            {...props}
          >
            {/* Glassmorphism overlay */}
            {glassmorphism && (
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-lg pointer-events-none" />
            )}

            {/* Gradient overlay */}
            {gradient && (
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-purple-500/5 rounded-lg pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            )}

            {/* Shine effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 opacity-0 pointer-events-none rounded-lg"
              animate={isHovered ? {
                x: ["-100%", "100%"],
                opacity: [0, 1, 0],
                transition: { duration: 1, ease: "easeInOut" }
              } : {}}
            />

            {/* Content */}
            <div className="relative z-10">
              {children}
            </div>

            {/* 3D depth indicator */}
            {hover3d && (
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-primary/5 to-purple-500/5 rounded-lg pointer-events-none opacity-0"
                animate={{
                  opacity: isHovered ? 0.3 : 0,
                  transition: { duration: 0.3 }
                }}
              />
            )}
          </Card>
        </motion.div>

        {/* Floating particles effect */}
        {glow && isHovered && (
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-primary/60 rounded-full"
                initial={{
                  x: Math.random() * 100 + "%",
                  y: "100%",
                  opacity: 0
                }}
                animate={{
                  y: "-20%",
                  opacity: [0, 1, 0],
                  transition: {
                    duration: 2 + i * 0.5,
                    repeat: Infinity,
                    delay: i * 0.3
                  }
                }}
              />
            ))}
          </div>
        )}
      </motion.div>
    );
  }
);

ModernCard.displayName = "ModernCard";

export { ModernCard };
