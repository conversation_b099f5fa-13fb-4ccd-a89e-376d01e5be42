import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Copy, ExternalLink, Download, Code, Book, Key } from 'lucide-react';
import { toast } from 'sonner';

const ApiDocumentation = () => {
  const [selectedEndpoint, setSelectedEndpoint] = useState(null);

  const apiEndpoints = [
    {
      method: 'POST',
      endpoint: '/api/auth/login',
      description: 'Authenticate user and get access token',
      parameters: [
        { name: 'email', type: 'string', required: true, description: 'User email address' },
        { name: 'password', type: 'string', required: true, description: 'User password' }
      ],
      response: {
        success: {
          token: 'string',
          user: {
            id: 'string',
            email: 'string',
            firstName: 'string',
            lastName: 'string'
          }
        }
      }
    },
    {
      method: 'POST',
      endpoint: '/api/auth/register',
      description: 'Register a new user account',
      parameters: [
        { name: 'firstName', type: 'string', required: true, description: 'User first name' },
        { name: 'lastName', type: 'string', required: true, description: 'User last name' },
        { name: 'email', type: 'string', required: true, description: 'User email address' },
        { name: 'password', type: 'string', required: true, description: 'User password (min 8 characters)' }
      ],
      response: {
        success: {
          message: 'User registered successfully',
          user: {
            id: 'string',
            email: 'string',
            firstName: 'string',
            lastName: 'string'
          }
        }
      }
    },
    {
      method: 'GET',
      endpoint: '/api/patients',
      description: 'Get list of patients',
      parameters: [
        { name: 'page', type: 'number', required: false, description: 'Page number for pagination' },
        { name: 'limit', type: 'number', required: false, description: 'Number of patients per page' },
        { name: 'search', type: 'string', required: false, description: 'Search term for patient name or MRN' }
      ],
      response: {
        success: {
          patients: 'array',
          total: 'number',
          page: 'number',
          totalPages: 'number'
        }
      }
    },
    {
      method: 'POST',
      endpoint: '/api/patients',
      description: 'Create a new patient record',
      parameters: [
        { name: 'firstName', type: 'string', required: true, description: 'Patient first name' },
        { name: 'lastName', type: 'string', required: true, description: 'Patient last name' },
        { name: 'dateOfBirth', type: 'string', required: true, description: 'Patient date of birth (YYYY-MM-DD)' },
        { name: 'gender', type: 'string', required: true, description: 'Patient gender' },
        { name: 'email', type: 'string', required: false, description: 'Patient email address' },
        { name: 'phone', type: 'string', required: false, description: 'Patient phone number' }
      ],
      response: {
        success: {
          message: 'Patient created successfully',
          patient: {
            id: 'string',
            mrn: 'string',
            firstName: 'string',
            lastName: 'string'
          }
        }
      }
    },
    {
      method: 'POST',
      endpoint: '/api/ml/symptom-analysis',
      description: 'Analyze patient symptoms using AI',
      parameters: [
        { name: 'symptoms', type: 'array', required: true, description: 'Array of symptom descriptions' },
        { name: 'patientAge', type: 'number', required: true, description: 'Patient age' },
        { name: 'patientGender', type: 'string', required: true, description: 'Patient gender' },
        { name: 'medicalHistory', type: 'array', required: false, description: 'Patient medical history' }
      ],
      response: {
        success: {
          analysis: {
            possibleConditions: 'array',
            riskLevel: 'string',
            recommendations: 'array',
            confidence: 'number'
          }
        }
      }
    },
    {
      method: 'POST',
      endpoint: '/api/ml/drug-interactions',
      description: 'Check for drug interactions',
      parameters: [
        { name: 'medications', type: 'array', required: true, description: 'Array of medication names' },
        { name: 'patientAge', type: 'number', required: true, description: 'Patient age' },
        { name: 'allergies', type: 'array', required: false, description: 'Known allergies' }
      ],
      response: {
        success: {
          interactions: 'array',
          warnings: 'array',
          recommendations: 'array'
        }
      }
    }
  ];

  const codeExamples = {
    javascript: `// JavaScript/Node.js Example
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

const data = await response.json();
console.log(data);`,
    python: `# Python Example
import requests

url = 'https://api.medora.com/api/auth/login'
data = {
    'email': '<EMAIL>',
    'password': 'password123'
}

response = requests.post(url, json=data)
result = response.json()
print(result)`,
    curl: `# cURL Example
curl -X POST https://api.medora.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'`
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard!');
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-white">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <img 
                src="/lovable-uploads/logo-png.png" 
                alt="MEDORA Logo" 
                className="h-10 w-10 object-contain"
              />
              <div>
                <h1 className="text-3xl font-bold" style={{color: '#9ACD32'}}>MEDORA API Documentation</h1>
                <p className="text-muted-foreground">Comprehensive API reference for the MEDORA AI consultation system</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Key className="h-4 w-4" />
                Get API Key
              </Button>
              <Button style={{backgroundColor: '#9ACD32', color: '#000000'}} className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Download SDK
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="authentication">Authentication</TabsTrigger>
            <TabsTrigger value="endpoints">API Endpoints</TabsTrigger>
            <TabsTrigger value="examples">Code Examples</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Book className="h-5 w-5" style={{color: '#9ACD32'}} />
                  Getting Started
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">
                  The MEDORA API provides access to our AI-powered medical consultation system. 
                  Use our API to integrate intelligent diagnosis assistance, patient management, 
                  and medical analytics into your applications.
                </p>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-semibold mb-2" style={{color: '#9ACD32'}}>Base URL</h3>
                    <code className="text-sm bg-muted p-2 rounded block">https://api.medora.com</code>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-semibold mb-2" style={{color: '#9ACD32'}}>API Version</h3>
                    <code className="text-sm bg-muted p-2 rounded block">v1</code>
                  </div>
                </div>

                <div className="p-4 border rounded-lg">
                  <h3 className="font-semibold mb-2" style={{color: '#9ACD32'}}>Rate Limits</h3>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• 1000 requests per hour for authenticated users</li>
                    <li>• 100 requests per hour for unauthenticated requests</li>
                    <li>• ML endpoints: 50 requests per hour</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="authentication" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>API Authentication</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">
                  MEDORA API uses Bearer token authentication. Include your API token in the Authorization header.
                </p>
                
                <div className="p-4 bg-muted rounded-lg">
                  <h4 className="font-semibold mb-2">Authorization Header</h4>
                  <code className="text-sm">Authorization: Bearer YOUR_API_TOKEN</code>
                </div>

                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2" style={{color: '#9ACD32'}}>Getting Your API Key</h4>
                  <ol className="text-sm text-muted-foreground space-y-1">
                    <li>1. Sign up for a MEDORA account</li>
                    <li>2. Navigate to your dashboard</li>
                    <li>3. Go to API Settings</li>
                    <li>4. Generate a new API key</li>
                  </ol>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="endpoints" className="space-y-6">
            <div className="space-y-4">
              {apiEndpoints.map((endpoint, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Badge 
                          variant={endpoint.method === 'GET' ? 'secondary' : 'default'}
                          style={endpoint.method !== 'GET' ? {backgroundColor: '#9ACD32', color: '#000000'} : {}}
                        >
                          {endpoint.method}
                        </Badge>
                        <code className="text-base">{endpoint.endpoint}</code>
                      </CardTitle>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => copyToClipboard(endpoint.endpoint)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground">{endpoint.description}</p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-2">Parameters</h4>
                        <div className="space-y-2">
                          {endpoint.parameters.map((param, paramIndex) => (
                            <div key={paramIndex} className="flex items-center justify-between p-2 border rounded">
                              <div>
                                <code className="text-sm font-medium">{param.name}</code>
                                <span className="text-xs text-muted-foreground ml-2">({param.type})</span>
                                {param.required && <Badge variant="destructive" className="ml-2 text-xs">Required</Badge>}
                              </div>
                              <p className="text-sm text-muted-foreground">{param.description}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold mb-2">Response</h4>
                        <div className="p-4 bg-muted rounded-lg">
                          <pre className="text-sm overflow-x-auto">
                            {JSON.stringify(endpoint.response.success, null, 2)}
                          </pre>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="examples" className="space-y-6">
            <div className="space-y-4">
              {Object.entries(codeExamples).map(([language, code]) => (
                <Card key={language}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="capitalize">{language} Example</CardTitle>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => copyToClipboard(code)}
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Copy
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="p-4 bg-muted rounded-lg">
                      <pre className="text-sm overflow-x-auto whitespace-pre-wrap">
                        {code}
                      </pre>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default ApiDocumentation;