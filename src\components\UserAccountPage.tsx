import React, { useState } from 'react';
import { User, Mail, Phone, Shield, Key, Bell } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import medoraAvatar from '@/assets/robot-avatar.jpg';

const UserAccountPage = () => {
  const [notifications, setNotifications] = useState(true);
  const [emailUpdates, setEmailUpdates] = useState(false);

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto space-y-8">
        <div>
          <h1 className="text-4xl font-bold text-foreground font-unica">ACCOUNT SETTINGS</h1>
          <p className="text-muted-foreground mt-2">Manage your account information and preferences</p>
        </div>

        {/* Profile Section */}
        <Card className="p-8 neu-raised">
          <h2 className="text-2xl font-semibold mb-6 flex items-center">
            <User className="h-6 w-6 mr-3 text-primary" />
            Profile Information
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div className="flex items-center space-x-6">
                <Avatar className="h-20 w-20 neu-raised">
                  <AvatarImage src={medoraAvatar} alt="Profile" />
                  <AvatarFallback>U</AvatarFallback>
                </Avatar>
                <Button variant="outline" className="h-12">Change Photo</Button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Full Name</label>
                  <Input placeholder="John Doe" className="h-12 neu-inset" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Email Address</label>
                  <Input placeholder="<EMAIL>" type="email" className="h-12 neu-inset" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Phone Number</label>
                  <Input placeholder="+****************" className="h-12 neu-inset" />
                </div>
              </div>
            </div>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2">Organization</label>
                <Input placeholder="Medical Practice Name" className="h-12 neu-inset" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Job Title</label>
                <Input placeholder="Chief Medical Officer" className="h-12 neu-inset" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Location</label>
                <Input placeholder="New York, NY" className="h-12 neu-inset" />
              </div>
              <Button className="w-full h-12 mt-6">Save Changes</Button>
            </div>
          </div>
        </Card>

        {/* Security Settings */}
        <Card className="p-8 neu-raised">
          <h2 className="text-2xl font-semibold mb-6 flex items-center">
            <Shield className="h-6 w-6 mr-3 text-primary" />
            Security Settings
          </h2>
          <div className="space-y-6">
            <div className="flex items-center justify-between p-4 border border-border rounded-lg neu-flat">
              <div className="flex items-center space-x-4">
                <Key className="h-5 w-5 text-muted-foreground" />
                <div>
                  <h3 className="font-medium">Password</h3>
                  <p className="text-sm text-muted-foreground">Last changed 2 months ago</p>
                </div>
              </div>
              <Button variant="outline">Change Password</Button>
            </div>
            
            <div className="flex items-center justify-between p-4 border border-border rounded-lg neu-flat">
              <div className="flex items-center space-x-4">
                <Shield className="h-5 w-5 text-muted-foreground" />
                <div>
                  <h3 className="font-medium">Two-Factor Authentication</h3>
                  <p className="text-sm text-muted-foreground">Add an extra layer of security</p>
                </div>
              </div>
              <Button variant="outline">Enable 2FA</Button>
            </div>
          </div>
        </Card>

        {/* Notification Preferences */}
        <Card className="p-8 neu-raised">
          <h2 className="text-2xl font-semibold mb-6 flex items-center">
            <Bell className="h-6 w-6 mr-3 text-primary" />
            Notification Preferences
          </h2>
          <div className="space-y-6">
            <div className="flex items-center justify-between p-4 border border-border rounded-lg neu-flat">
              <div>
                <h3 className="font-medium">Push Notifications</h3>
                <p className="text-sm text-muted-foreground">Receive notifications about patient updates</p>
              </div>
              <Switch checked={notifications} onCheckedChange={setNotifications} />
            </div>
            
            <div className="flex items-center justify-between p-4 border border-border rounded-lg neu-flat">
              <div>
                <h3 className="font-medium">Email Updates</h3>
                <p className="text-sm text-muted-foreground">Weekly summary and feature updates</p>
              </div>
              <Switch checked={emailUpdates} onCheckedChange={setEmailUpdates} />
            </div>
          </div>
        </Card>

        {/* Danger Zone */}
        <Card className="p-8 neu-raised border-red-500/20">
          <h2 className="text-2xl font-semibold mb-6 text-red-400">Danger Zone</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border border-red-500/20 rounded-lg">
              <div>
                <h3 className="font-medium text-red-400">Export Account Data</h3>
                <p className="text-sm text-muted-foreground">Download all your data</p>
              </div>
              <Button variant="outline" className="border-red-500/50 text-red-400">Export Data</Button>
            </div>
            
            <div className="flex items-center justify-between p-4 border border-red-500/20 rounded-lg">
              <div>
                <h3 className="font-medium text-red-400">Delete Account</h3>
                <p className="text-sm text-muted-foreground">Permanently delete your account</p>
              </div>
              <Button variant="outline" className="border-red-500/50 text-red-400">Delete Account</Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default UserAccountPage;