import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Palette, Globe, Shield, Mail, Database, Zap } from 'lucide-react';

const AdminSettings = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Global Settings</h1>
        <p className="text-muted-foreground">
          Configure your application settings and preferences
        </p>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="appearance">Appearance</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Application Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="appName">Application Name</Label>
                  <Input id="appName" defaultValue="MEDORA - Medical AI Consultant" />
                </div>
                <div>
                  <Label htmlFor="appVersion">Version</Label>
                  <Input id="appVersion" defaultValue="1.0.0" />
                </div>
              </div>
              <div>
                <Label htmlFor="appDescription">Description</Label>
                <Textarea 
                  id="appDescription" 
                  defaultValue="AI-powered medical consultant for healthcare professionals" 
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contactEmail">Contact Email</Label>
                  <Input id="contactEmail" defaultValue="<EMAIL>" type="email" />
                </div>
                <div>
                  <Label htmlFor="supportPhone">Support Phone</Label>
                  <Input id="supportPhone" defaultValue="+****************" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>System Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="maintenance">Maintenance Mode</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable maintenance mode to show a maintenance page to users
                  </p>
                </div>
                <Switch id="maintenance" />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="registration">User Registration</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow new users to register accounts
                  </p>
                </div>
                <Switch id="registration" defaultChecked />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="analytics">Analytics Tracking</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable usage analytics and tracking
                  </p>
                </div>
                <Switch id="analytics" defaultChecked />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="appearance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                UI Customization
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="primaryColor">Primary Color</Label>
                  <div className="flex gap-2">
                    <Input type="color" defaultValue="#0070f3" className="w-16 h-10" />
                    <Input defaultValue="#0070f3" className="flex-1" />
                  </div>
                </div>
                <div>
                  <Label htmlFor="accentColor">Accent Color</Label>
                  <div className="flex gap-2">
                    <Input type="color" defaultValue="#f59e0b" className="w-16 h-10" />
                    <Input defaultValue="#f59e0b" className="flex-1" />
                  </div>
                </div>
              </div>
              <div>
                <Label htmlFor="logoUrl">Logo URL</Label>
                <Input id="logoUrl" placeholder="https://example.com/logo.png" />
              </div>
              <div>
                <Label htmlFor="faviconUrl">Favicon URL</Label>
                <Input id="faviconUrl" placeholder="https://example.com/favicon.ico" />
              </div>
              <div>
                <Label htmlFor="defaultTheme">Default Theme</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select theme" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Custom CSS</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="customCss">Additional CSS</Label>
                <Textarea 
                  id="customCss" 
                  placeholder="/* Add your custom CSS here */"
                  className="font-mono text-sm min-h-32"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seo" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                SEO Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="siteTitle">Site Title</Label>
                <Input id="siteTitle" defaultValue="MEDORA - Medical AI Consultant" />
                <p className="text-sm text-muted-foreground mt-1">
                  Recommended length: 50-60 characters
                </p>
              </div>
              <div>
                <Label htmlFor="metaDescription">Meta Description</Label>
                <Textarea 
                  id="metaDescription" 
                  defaultValue="AI-powered medical consultant for healthcare professionals. Get instant medical advice, patient analysis, and medical research with advanced AI technology."
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Recommended length: 150-160 characters
                </p>
              </div>
              <div>
                <Label htmlFor="keywords">Keywords</Label>
                <Input 
                  id="keywords" 
                  defaultValue="medical AI, medical consultant, patient analysis, medical research"
                  placeholder="Separate with commas"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="ogImage">Open Graph Image</Label>
                  <Input id="ogImage" placeholder="https://example.com/og-image.jpg" />
                </div>
                <div>
                  <Label htmlFor="twitterCard">Twitter Card Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="summary">Summary</SelectItem>
                      <SelectItem value="summary_large_image">Summary Large Image</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Structured Data</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="structuredData">Enable Structured Data</Label>
                  <p className="text-sm text-muted-foreground">
                    Add JSON-LD structured data for better SEO
                  </p>
                </div>
                <Switch id="structuredData" defaultChecked />
              </div>
              <div>
                <Label htmlFor="organizationSchema">Organization Schema</Label>
                <Textarea 
                  id="organizationSchema"
                  placeholder="JSON-LD organization schema"
                  className="font-mono text-sm"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="twoFactor">Two-Factor Authentication</Label>
                  <p className="text-sm text-muted-foreground">
                    Require 2FA for admin accounts
                  </p>
                </div>
                <Switch id="twoFactor" defaultChecked />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="passwordPolicy">Strict Password Policy</Label>
                  <p className="text-sm text-muted-foreground">
                    Enforce strong passwords with complexity requirements
                  </p>
                </div>
                <Switch id="passwordPolicy" defaultChecked />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="sessionTimeout">Session Timeout</Label>
                  <p className="text-sm text-muted-foreground">
                    Auto-logout users after inactivity
                  </p>
                </div>
                <Select>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 min</SelectItem>
                    <SelectItem value="30">30 min</SelectItem>
                    <SelectItem value="60">1 hour</SelectItem>
                    <SelectItem value="120">2 hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="ipWhitelist">IP Whitelist</Label>
                  <p className="text-sm text-muted-foreground">
                    Restrict admin access to specific IP addresses
                  </p>
                </div>
                <Switch id="ipWhitelist" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>API Security</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="rateLimiting">Rate Limiting</Label>
                <div className="flex gap-2 mt-1">
                  <Input placeholder="1000" className="w-24" />
                  <span className="flex items-center text-sm text-muted-foreground">requests per hour</span>
                </div>
              </div>
              <div>
                <Label htmlFor="corsOrigins">CORS Origins</Label>
                <Textarea 
                  id="corsOrigins"
                  placeholder="https://example.com&#10;https://app.example.com"
                  className="text-sm"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Email Notifications
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="smtpHost">SMTP Host</Label>
                <Input id="smtpHost" placeholder="smtp.gmail.com" />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="smtpPort">SMTP Port</Label>
                  <Input id="smtpPort" placeholder="587" />
                </div>
                <div>
                  <Label htmlFor="smtpSecurity">Security</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="tls">TLS</SelectItem>
                      <SelectItem value="ssl">SSL</SelectItem>
                      <SelectItem value="none">None</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="smtpUsername">Username</Label>
                  <Input id="smtpUsername" placeholder="<EMAIL>" />
                </div>
                <div>
                  <Label htmlFor="smtpPassword">Password</Label>
                  <Input id="smtpPassword" type="password" placeholder="••••••••" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="newUserNotif">New User Registration</Label>
                  <p className="text-sm text-muted-foreground">
                    Notify admins when new users register
                  </p>
                </div>
                <Switch id="newUserNotif" defaultChecked />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="paymentNotif">Payment Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Notify on successful and failed payments
                  </p>
                </div>
                <Switch id="paymentNotif" defaultChecked />
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="errorNotif">System Errors</Label>
                  <p className="text-sm text-muted-foreground">
                    Notify admins of critical system errors
                  </p>
                </div>
                <Switch id="errorNotif" defaultChecked />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Third-party Integrations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                { name: 'Google Analytics', status: 'Connected', description: 'Website analytics and tracking' },
                { name: 'Stripe', status: 'Connected', description: 'Payment processing' },
                { name: 'SendGrid', status: 'Not Connected', description: 'Email delivery service' },
                { name: 'Slack', status: 'Not Connected', description: 'Team notifications' },
                { name: 'Discord', status: 'Not Connected', description: 'Community integration' }
              ].map((integration) => (
                <div key={integration.name} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">{integration.name}</p>
                    <p className="text-sm text-muted-foreground">{integration.description}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={integration.status === 'Connected' ? 'default' : 'secondary'}>
                      {integration.status}
                    </Badge>
                    <Button size="sm" variant="outline">
                      {integration.status === 'Connected' ? 'Configure' : 'Connect'}
                    </Button>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Webhook Endpoints</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="webhookUrl">Webhook URL</Label>
                <Input id="webhookUrl" placeholder="https://your-app.com/webhook" />
              </div>
              <div>
                <Label htmlFor="webhookSecret">Webhook Secret</Label>
                <Input id="webhookSecret" type="password" placeholder="••••••••" />
              </div>
              <div className="flex gap-2">
                <Button size="sm">Test Webhook</Button>
                <Button size="sm" variant="outline">Generate Secret</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end gap-2 pt-6 border-t">
        <Button variant="outline">Reset to Defaults</Button>
        <Button>Save All Settings</Button>
      </div>
    </div>
  );
};

export default AdminSettings;