// Email Templates for MEDORA System

export interface EmailTemplateData {
  userName: string;
  userEmail: string;
  verificationLink?: string;
  resetLink?: string;
  confirmationCode?: string;
  expiryTime?: string;
  supportEmail?: string;
  companyName?: string;
}

export const emailTemplates = {
  // Welcome Email Template
  welcome: (data: EmailTemplateData) => `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to MEDORA</title>
      <style>
        body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #10b981 0%, #ec4899 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #fff; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .logo { font-size: 28px; font-weight: bold; color: white; }
        .title { color: #10b981; font-size: 24px; margin: 20px 0; }
        .button { display: inline-block; background: linear-gradient(135deg, #10b981 0%, #**********%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">MEDORA</div>
          <p style="color: white; margin: 0;">AI-Powered Medical Assistant</p>
        </div>
        <div class="content">
          <h1 class="title">Welcome to MEDORA! 🎉</h1>
          <p>Dear ${data.userName},</p>
          <p>Welcome to MEDORA - your AI-powered medical assistant that's revolutionizing healthcare!</p>
          <p>We're excited to have you on board and can't wait to help you transform your medical practice with cutting-edge AI technology.</p>
          
          <h3>What you can do with MEDORA:</h3>
          <ul>
            <li>🤖 AI-powered diagnosis and symptom analysis</li>
            <li>💬 Real-time voice and text consultations</li>
            <li>📊 Comprehensive patient management</li>
            <li>🔬 Advanced medical research integration</li>
            <li>📱 Mobile-responsive interface</li>
          </ul>
          
          <div style="text-align: center;">
            <a href="https://medora.ai/dashboard" class="button">Get Started Now</a>
          </div>
          
          <p>If you have any questions or need assistance, our support team is here to help!</p>
          
          <p>Best regards,<br>The MEDORA Team</p>
        </div>
        <div class="footer">
          <p>© 2024 MEDORA. All rights reserved.</p>
          <p>This email was sent to ${data.userEmail}</p>
        </div>
      </div>
    </body>
    </html>
  `,

  // Email Verification Template
  emailVerification: (data: EmailTemplateData) => `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verify Your Email - MEDORA</title>
      <style>
        body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #10b981 0%, #ec4899 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #fff; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .logo { font-size: 28px; font-weight: bold; color: white; }
        .title { color: #10b981; font-size: 24px; margin: 20px 0; }
        .button { display: inline-block; background: linear-gradient(135deg, #10b981 0%, #**********%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; margin: 20px 0; }
        .code { background: #f3f4f6; padding: 15px; border-radius: 8px; text-align: center; font-size: 24px; font-weight: bold; color: #10b981; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 8px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">MEDORA</div>
          <p style="color: white; margin: 0;">Email Verification</p>
        </div>
        <div class="content">
          <h1 class="title">Verify Your Email Address</h1>
          <p>Dear ${data.userName},</p>
          <p>Thank you for signing up for MEDORA! To complete your registration and access your account, please verify your email address.</p>
          
          <div style="text-align: center;">
            <a href="${data.verificationLink}" class="button">Verify Email Address</a>
          </div>
          
          <p>Or copy and paste this verification code:</p>
          <div class="code">${data.confirmationCode}</div>
          
          <div class="warning">
            <strong>⚠️ Security Notice:</strong> This verification link will expire in ${data.expiryTime}. If you didn't create a MEDORA account, please ignore this email.
          </div>
          
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #10b981;">${data.verificationLink}</p>
          
          <p>Best regards,<br>The MEDORA Team</p>
        </div>
        <div class="footer">
          <p>© 2024 MEDORA. All rights reserved.</p>
          <p>This email was sent to ${data.userEmail}</p>
          <p>Need help? Contact us at ${data.supportEmail}</p>
        </div>
      </div>
    </body>
    </html>
  `,

  // Password Reset Template
  passwordReset: (data: EmailTemplateData) => `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your Password - MEDORA</title>
      <style>
        body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #10b981 0%, #ec4899 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #fff; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .logo { font-size: 28px; font-weight: bold; color: white; }
        .title { color: #10b981; font-size: 24px; margin: 20px 0; }
        .button { display: inline-block; background: linear-gradient(135deg, #10b981 0%, #**********%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .security { background: #f0fdf4; border: 1px solid #10b981; padding: 15px; border-radius: 8px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">MEDORA</div>
          <p style="color: white; margin: 0;">Password Reset</p>
        </div>
        <div class="content">
          <h1 class="title">Reset Your Password</h1>
          <p>Dear ${data.userName},</p>
          <p>We received a request to reset your password for your MEDORA account. Click the button below to create a new password:</p>
          
          <div style="text-align: center;">
            <a href="${data.resetLink}" class="button">Reset Password</a>
          </div>
          
          <div class="warning">
            <strong>⚠️ Important:</strong> This password reset link will expire in ${data.expiryTime}. If you didn't request a password reset, please ignore this email.
          </div>
          
          <div class="security">
            <strong>🔒 Security Tips:</strong>
            <ul>
              <li>Use a strong, unique password</li>
              <li>Never share your password with anyone</li>
              <li>Enable two-factor authentication for extra security</li>
            </ul>
          </div>
          
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #10b981;">${data.resetLink}</p>
          
          <p>Best regards,<br>The MEDORA Security Team</p>
        </div>
        <div class="footer">
          <p>© 2024 MEDORA. All rights reserved.</p>
          <p>This email was sent to ${data.userEmail}</p>
          <p>Need help? Contact us at ${data.supportEmail}</p>
        </div>
      </div>
    </body>
    </html>
  `,

  // Account Confirmation Template
  accountConfirmed: (data: EmailTemplateData) => `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Account Confirmed - MEDORA</title>
      <style>
        body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #10b981 0%, #ec4899 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #fff; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .logo { font-size: 28px; font-weight: bold; color: white; }
        .title { color: #10b981; font-size: 24px; margin: 20px 0; }
        .button { display: inline-block; background: linear-gradient(135deg, #10b981 0%, #**********%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; margin: 20px 0; }
        .success { background: #f0fdf4; border: 1px solid #10b981; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">MEDORA</div>
          <p style="color: white; margin: 0;">Account Confirmed</p>
        </div>
        <div class="content">
          <h1 class="title">🎉 Your Account is Confirmed!</h1>
          <p>Dear ${data.userName},</p>
          <p>Great news! Your MEDORA account has been successfully confirmed and is now fully active.</p>
          
          <div class="success">
            <strong>✅ Account Status: Active</strong><br>
            Your email address has been verified and your account is ready to use.
          </div>
          
          <h3>What's Next?</h3>
          <ul>
            <li>Complete your profile setup</li>
            <li>Explore the AI-powered features</li>
            <li>Start managing your patients</li>
            <li>Try the voice consultation feature</li>
          </ul>
          
          <div style="text-align: center;">
            <a href="https://medora.ai/dashboard" class="button">Access Your Dashboard</a>
          </div>
          
          <p>If you have any questions or need assistance getting started, our support team is here to help!</p>
          
          <p>Best regards,<br>The MEDORA Team</p>
        </div>
        <div class="footer">
          <p>© 2024 MEDORA. All rights reserved.</p>
          <p>This email was sent to ${data.userEmail}</p>
          <p>Need help? Contact us at ${data.supportEmail}</p>
        </div>
      </div>
    </body>
    </html>
  `,

  // Payment Confirmation Template
  paymentConfirmation: (data: EmailTemplateData) => `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Payment Confirmation - MEDORA</title>
      <style>
        body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #10b981 0%, #ec4899 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #fff; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .logo { font-size: 28px; font-weight: bold; color: white; }
        .title { color: #10b981; font-size: 24px; margin: 20px 0; }
        .receipt { background: #f8fafc; border: 1px solid #e2e8f0; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">MEDORA</div>
          <p style="color: white; margin: 0;">Payment Confirmation</p>
        </div>
        <div class="content">
          <h1 class="title">Payment Successful! 💳</h1>
          <p>Dear ${data.userName},</p>
          <p>Thank you for your payment! Your MEDORA subscription has been successfully processed.</p>
          
          <div class="receipt">
            <h3>Payment Details</h3>
            <p><strong>Amount:</strong> $99.00</p>
            <p><strong>Plan:</strong> Professional</p>
            <p><strong>Billing Cycle:</strong> Monthly</p>
            <p><strong>Next Billing Date:</strong> February 15, 2024</p>
          </div>
          
          <p>Your account has been upgraded and you now have access to all Professional plan features:</p>
          <ul>
            <li>Up to 500 patients</li>
            <li>2000 consultations/month</li>
            <li>Advanced AI features</li>
            <li>Priority support</li>
            <li>Custom integrations</li>
          </ul>
          
          <p>If you have any questions about your subscription or billing, please don't hesitate to contact our support team.</p>
          
          <p>Best regards,<br>The MEDORA Billing Team</p>
        </div>
        <div class="footer">
          <p>© 2024 MEDORA. All rights reserved.</p>
          <p>This email was sent to ${data.userEmail}</p>
          <p>Billing support: <EMAIL></p>
        </div>
      </div>
    </body>
    </html>
  `,

  // Security Alert Template
  securityAlert: (data: EmailTemplateData) => `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Security Alert - MEDORA</title>
      <style>
        body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #fff; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .logo { font-size: 28px; font-weight: bold; color: white; }
        .title { color: #ef4444; font-size: 24px; margin: 20px 0; }
        .alert { background: #fef2f2; border: 1px solid #ef4444; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .button { display: inline-block; background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">MEDORA</div>
          <p style="color: white; margin: 0;">Security Alert</p>
        </div>
        <div class="content">
          <h1 class="title">🚨 Security Alert</h1>
          <p>Dear ${data.userName},</p>
          <p>We detected unusual activity on your MEDORA account that may require your attention.</p>
          
          <div class="alert">
            <strong>⚠️ Suspicious Activity Detected:</strong><br>
            • Multiple failed login attempts<br>
            • Login from unrecognized device<br>
            • Unusual access pattern
          </div>
          
          <p>If this was you, no action is required. However, if you don't recognize this activity, we recommend:</p>
          <ol>
            <li>Change your password immediately</li>
            <li>Enable two-factor authentication</li>
            <li>Review your recent account activity</li>
            <li>Contact our security team if needed</li>
          </ol>
          
          <div style="text-align: center;">
            <a href="https://medora.ai/security" class="button">Review Account Security</a>
          </div>
          
          <p>Your security is our top priority. If you have any concerns, please contact our security team immediately.</p>
          
          <p>Best regards,<br>The MEDORA Security Team</p>
        </div>
        <div class="footer">
          <p>© 2024 MEDORA. All rights reserved.</p>
          <p>This email was sent to ${data.userEmail}</p>
          <p>Security support: <EMAIL></p>
        </div>
      </div>
    </body>
    </html>
  `
};

// Helper function to send emails
export const sendEmail = async (template: keyof typeof emailTemplates, data: EmailTemplateData, recipient: string) => {
  try {
    const htmlContent = emailTemplates[template](data);
    
    // Here you would integrate with your email service (SendGrid, AWS SES, etc.)
    console.log(`Sending ${template} email to ${recipient}`);
    console.log('Email content:', htmlContent);
    
    // Simulate email sending
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return { success: true, message: 'Email sent successfully' };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, message: 'Failed to send email' };
  }
};

// Email service configuration
export const emailConfig = {
  fromEmail: '<EMAIL>',
  fromName: 'MEDORA Team',
  supportEmail: '<EMAIL>',
  securityEmail: '<EMAIL>',
  billingEmail: '<EMAIL>'
};
