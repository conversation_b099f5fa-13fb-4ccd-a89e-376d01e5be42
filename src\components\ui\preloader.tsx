import React, { useEffect, useState } from 'react';
import { Activity, Zap, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PreloaderProps {
  isLoading: boolean;
  onComplete?: () => void;
  variant?: 'medical' | 'ai' | 'minimal';
  showProgress?: boolean;
}

export const Preloader: React.FC<PreloaderProps> = ({
  isLoading,
  onComplete,
  variant = 'ai',
  showProgress = true
}) => {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);

  const loadingSteps = [
    'Initializing MEDORA AI...',
    'Loading Medical AI Models...',
    'Connecting to Medical Database...',
    'Preparing Intelligent Interface...',
    'Finalizing AI Systems...',
    'Almost Ready...'
  ];

  useEffect(() => {
    if (!isLoading) return;

    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setTimeout(() => onComplete?.(), 500);
          return 100;
        }
        return prev + Math.random() * 15;
      });
    }, 200);

    const stepInterval = setInterval(() => {
      setCurrentStep(prev => (prev + 1) % loadingSteps.length);
    }, 800);

    return () => {
      clearInterval(interval);
      clearInterval(stepInterval);
    };
  }, [isLoading, onComplete]);

  if (!isLoading) return null;

  const renderIcon = () => {
    switch (variant) {
      case 'medical':
        return <Activity className="w-16 h-16 text-green-500 animate-pulse" />;
      case 'ai':
        return (
          <div className="relative">
            <img
              src="/lovable-uploads/logo-png.png"
              alt="MEDORA Logo"
              className="w-16 h-16 object-contain animate-pulse"
              style={{
                filter: 'drop-shadow(0 0 20px rgba(59, 130, 246, 0.5))',
                animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite, glow 3s ease-in-out infinite alternate'
              }}
            />
            <div className="absolute inset-0 w-16 h-16 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 animate-spin"
                 style={{ animationDuration: '3s' }} />
          </div>
        );
      default:
        return <Zap className="w-16 h-16 text-blue-500 animate-pulse" />;
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-[#ADF802]/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(30)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-[#ADF802] rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center space-y-8">
        {/* Logo and Icon */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-[#ADF802] to-purple-500 blur-3xl opacity-30 animate-pulse"></div>
          <div className="relative">
            {renderIcon()}
          </div>
        </div>

        {/* Brand Name */}
        <div className="space-y-2">
          <h1 className="text-6xl font-bold bg-gradient-to-r from-white via-[#ADF802] to-white bg-clip-text text-transparent">
            MEDORA
          </h1>
          <p className="text-white/70 text-lg">AI-Powered Medical Assistant</p>
        </div>

        {/* Loading Steps */}
        <div className="space-y-4">
          <div className="flex items-center justify-center space-x-2 text-white/80">
            <Sparkles className="w-4 h-4 animate-spin" />
            <span className="text-sm font-medium">{loadingSteps[currentStep]}</span>
          </div>

          {/* Progress Bar */}
          {showProgress && (
            <div className="w-80 mx-auto">
              <div className="bg-white/10 rounded-full h-2 overflow-hidden">
                <div 
                  className="bg-gradient-to-r from-[#ADF802] to-[#84cc16] h-full rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${Math.min(progress, 100)}%` }}
                ></div>
              </div>
              <div className="text-center mt-2 text-white/60 text-sm">
                {Math.round(Math.min(progress, 100))}%
              </div>
            </div>
          )}
        </div>

        {/* Animated Dots */}
        <div className="flex justify-center space-x-2">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className="w-3 h-3 bg-[#ADF802] rounded-full animate-bounce"
              style={{ animationDelay: `${i * 0.2}s` }}
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Simple Loading Overlay for components
export const LoadingOverlay: React.FC<{
  isLoading: boolean;
  children: React.ReactNode;
  className?: string;
}> = ({ isLoading, children, className }) => {
  return (
    <div className={cn('relative', className)}>
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-10">
          <div className="text-center space-y-4">
            <img
              src="/lovable-uploads/logo-png.png"
              alt="MEDORA AI"
              className="w-8 h-8 animate-pulse mx-auto object-contain"
            />
            <p className="text-sm text-muted-foreground">Loading...</p>
          </div>
        </div>
      )}
    </div>
  );
};

// Skeleton Components for better loading states
export const SkeletonCard = ({ className }: { className?: string }) => (
  <div className={cn('animate-pulse space-y-4', className)}>
    <div className="bg-muted rounded-lg h-48 w-full"></div>
    <div className="space-y-2">
      <div className="bg-muted h-4 rounded w-3/4"></div>
      <div className="bg-muted h-4 rounded w-1/2"></div>
    </div>
  </div>
);

export const SkeletonText = ({ 
  lines = 3, 
  className 
}: { 
  lines?: number; 
  className?: string; 
}) => (
  <div className={cn('animate-pulse space-y-2', className)}>
    {Array.from({ length: lines }).map((_, i) => (
      <div
        key={i}
        className={cn(
          'bg-muted h-4 rounded',
          i === lines - 1 ? 'w-3/4' : 'w-full'
        )}
      ></div>
    ))}
  </div>
);

export const SkeletonAvatar = ({ 
  size = 'md' 
}: { 
  size?: 'sm' | 'md' | 'lg' 
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };

  return (
    <div className={cn('animate-pulse bg-muted rounded-full', sizeClasses[size])}></div>
  );
};

export default Preloader;
