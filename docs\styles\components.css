/* Neumorphic Cards */
.card {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: 30px;
    margin: 30px 0;
    box-shadow: var(--shadow-neumorphic);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 25px 25px 70px #d1d9e6, -25px -25px 70px #ffffff;
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    background: var(--gradient-accent);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    box-shadow: var(--shadow-3d);
}

.card-icon i {
    font-size: 1.5rem;
    color: white;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Step Guide Styles */
.step-guide {
    display: grid;
    gap: 30px;
    margin: 40px 0;
}

.step {
    display: flex;
    align-items: flex-start;
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 25px;
    box-shadow: var(--shadow-neumorphic);
    position: relative;
}

.step::before {
    content: '';
    position: absolute;
    left: 35px;
    top: 80px;
    width: 2px;
    height: calc(100% + 30px);
    background: var(--gradient-primary);
    z-index: 1;
}

.step:last-child::before {
    display: none;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    margin-right: 20px;
    box-shadow: var(--shadow-3d);
    position: relative;
    z-index: 2;
}

.step-content {
    flex: 1;
}

.step-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.step-description {
    color: var(--text-secondary);
    margin-bottom: 15px;
}

/* Visual Flow Diagrams */
.flow-diagram {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px 0;
    flex-wrap: wrap;
    gap: 20px;
}

.flow-item {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow-neumorphic);
    min-width: 150px;
    position: relative;
    transition: all 0.3s ease;
}

.flow-item:hover {
    transform: translateY(-5px);
    box-shadow: 25px 25px 70px #d1d9e6, -25px -25px 70px #ffffff;
}

.flow-item::after {
    content: '→';
    position: absolute;
    right: -30px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
    color: var(--primary-color);
    font-weight: bold;
}

.flow-item:last-child::after {
    display: none;
}

.flow-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: var(--gradient-accent);
    margin: 0 auto 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

/* Interactive Elements */
.interactive-demo {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 30px;
    margin: 30px 0;
    box-shadow: var(--shadow-neumorphic);
    border: 2px dashed var(--primary-color);
}

.demo-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.demo-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
}

.demo-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.demo-content {
    background: var(--bg-primary);
    border-radius: 10px;
    padding: 20px;
    margin-top: 15px;
}

/* Progress Indicators */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-primary);
    border-radius: 4px;
    overflow: hidden;
    margin: 20px 0;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.progress-step::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 50%;
    width: 100%;
    height: 2px;
    background: var(--bg-primary);
    z-index: 1;
}

.progress-step:last-child::after {
    display: none;
}

.progress-step.completed::after {
    background: var(--gradient-primary);
}

.progress-step-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--bg-primary);
    border: 2px solid var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.progress-step.completed .progress-step-circle {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    color: white;
}

.progress-step-label {
    margin-top: 10px;
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-align: center;
}

/* Tabs */
.tabs {
    margin: 30px 0;
}

.tab-list {
    display: flex;
    background: var(--bg-primary);
    border-radius: 10px;
    padding: 5px;
    margin-bottom: 20px;
}

.tab-button {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: var(--text-secondary);
}

.tab-button.active {
    background: var(--bg-secondary);
    color: var(--primary-color);
    box-shadow: var(--shadow-neumorphic);
}

.tab-content {
    background: var(--bg-secondary);
    border-radius: 15px;
    padding: 30px;
    box-shadow: var(--shadow-neumorphic);
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* Tooltips */
.tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 115%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--text-primary);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .flow-diagram {
        flex-direction: column;
    }
    
    .flow-item::after {
        content: '↓';
        right: 50%;
        top: 100%;
        transform: translateX(50%);
    }
    
    .step {
        flex-direction: column;
        text-align: center;
    }
    
    .step::before {
        display: none;
    }
    
    .tab-list {
        flex-direction: column;
    }
    
    .progress-steps {
        flex-direction: column;
        gap: 20px;
    }
    
    .progress-step::after {
        display: none;
    }
}

/* Flow Diagrams */
.flow-diagram {
    margin: 2rem 0;
}

.flow-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    box-shadow: var(--shadow-neumorphic);
}

.flow-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.flow-box {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: var(--shadow-neumorphic);
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    transition: all 0.3s ease;
}

.flow-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.flow-arrow {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* Security Checklist */
.security-checklist {
    margin: 2rem 0;
}

.checklist-column {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.checklist-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow-neumorphic);
    transition: all 0.3s ease;
}

.checklist-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.checkmark {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    flex-shrink: 0;
}

/* Step Guide Enhancements */
.step-guide {
    margin: 2rem 0;
}

.step-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 1.5rem;
    background: white;
    border-radius: 15px;
    box-shadow: var(--shadow-neumorphic);
    transition: all 0.3s ease;
}

.step-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.step-number {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.step-content h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-weight: 600;
}

.step-content p {
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .flow-container {
        flex-direction: column;
        text-align: center;
    }

    .flow-arrow {
        transform: rotate(90deg);
    }

    .step-item {
        flex-direction: column;
        text-align: center;
    }

    .checklist-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}
