// Initialize AOS (Animate On Scroll)
document.addEventListener('DOMContentLoaded', function() {
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        offset: 100
    });
});

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add active state to navigation
window.addEventListener('scroll', () => {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-item a');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
});

// Print functionality
function printManual() {
    window.print();
}

// Add print button
const printButton = document.createElement('button');
printButton.innerHTML = '<i class="fas fa-print"></i> Print Manual';
printButton.style.cssText = `
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: var(--shadow-3d);
    font-weight: 600;
    z-index: 1000;
    transition: all 0.3s ease;
`;
printButton.onclick = printManual;

// Add hover effect to print button
printButton.addEventListener('mouseenter', function() {
    this.style.transform = 'translateY(-3px)';
    this.style.boxShadow = '0 15px 35px rgba(0,0,0,0.3), 0 3px 10px rgba(0,0,0,0.2)';
});

printButton.addEventListener('mouseleave', function() {
    this.style.transform = 'translateY(0)';
    this.style.boxShadow = 'var(--shadow-3d)';
});

document.body.appendChild(printButton);

// Code block copy functionality
document.addEventListener('DOMContentLoaded', function() {
    const codeBlocks = document.querySelectorAll('.code-block');
    
    codeBlocks.forEach(block => {
        const copyButton = document.createElement('button');
        copyButton.innerHTML = '<i class="fas fa-copy"></i>';
        copyButton.className = 'copy-button';
        copyButton.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0;
        `;
        
        block.style.position = 'relative';
        block.appendChild(copyButton);
        
        // Show copy button on hover
        block.addEventListener('mouseenter', () => {
            copyButton.style.opacity = '1';
        });
        
        block.addEventListener('mouseleave', () => {
            copyButton.style.opacity = '0';
        });
        
        // Copy functionality
        copyButton.addEventListener('click', async () => {
            const code = block.querySelector('pre').textContent;
            try {
                await navigator.clipboard.writeText(code);
                copyButton.innerHTML = '<i class="fas fa-check"></i>';
                copyButton.style.background = 'rgba(74, 222, 128, 0.8)';
                
                setTimeout(() => {
                    copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                    copyButton.style.background = 'rgba(255, 255, 255, 0.1)';
                }, 2000);
            } catch (err) {
                console.error('Failed to copy code:', err);
            }
        });
    });
});

// Search functionality
function createSearchBox() {
    const searchContainer = document.createElement('div');
    searchContainer.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1001;
    `;
    
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'Search manual...';
    searchInput.style.cssText = `
        padding: 10px 15px;
        border: none;
        border-radius: 25px;
        background: var(--bg-secondary);
        box-shadow: var(--shadow-neumorphic);
        outline: none;
        width: 250px;
        font-family: 'Inter', sans-serif;
    `;
    
    searchContainer.appendChild(searchInput);
    document.body.appendChild(searchContainer);
    
    // Search functionality
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const sections = document.querySelectorAll('section');
        
        sections.forEach(section => {
            const text = section.textContent.toLowerCase();
            if (searchTerm && text.includes(searchTerm)) {
                section.style.display = 'block';
                // Highlight search terms
                highlightSearchTerm(section, searchTerm);
            } else if (searchTerm) {
                section.style.display = 'none';
            } else {
                section.style.display = 'block';
                removeHighlights(section);
            }
        });
    });
}

function highlightSearchTerm(element, term) {
    // Simple highlighting implementation
    const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );
    
    const textNodes = [];
    let node;
    
    while (node = walker.nextNode()) {
        textNodes.push(node);
    }
    
    textNodes.forEach(textNode => {
        const text = textNode.textContent;
        const regex = new RegExp(`(${term})`, 'gi');
        if (regex.test(text)) {
            const highlightedText = text.replace(regex, '<mark>$1</mark>');
            const span = document.createElement('span');
            span.innerHTML = highlightedText;
            textNode.parentNode.replaceChild(span, textNode);
        }
    });
}

function removeHighlights(element) {
    const marks = element.querySelectorAll('mark');
    marks.forEach(mark => {
        mark.outerHTML = mark.innerHTML;
    });
}

// Initialize search box
document.addEventListener('DOMContentLoaded', createSearchBox);

// Progress tracking
function updateProgress() {
    const sections = document.querySelectorAll('section[id]');
    const totalSections = sections.length;
    let visibleSections = 0;
    
    sections.forEach(section => {
        const rect = section.getBoundingClientRect();
        if (rect.top < window.innerHeight && rect.bottom > 0) {
            visibleSections++;
        }
    });
    
    const progress = (visibleSections / totalSections) * 100;
    
    // Update progress bar if it exists
    const progressBar = document.querySelector('.progress-fill');
    if (progressBar) {
        progressBar.style.width = `${progress}%`;
    }
}

// Update progress on scroll
window.addEventListener('scroll', updateProgress);

// Theme switcher
function createThemeSwitcher() {
    const themeButton = document.createElement('button');
    themeButton.innerHTML = '<i class="fas fa-moon"></i>';
    themeButton.style.cssText = `
        position: fixed;
        bottom: 100px;
        right: 30px;
        background: var(--bg-secondary);
        border: none;
        padding: 15px;
        border-radius: 50%;
        cursor: pointer;
        box-shadow: var(--shadow-neumorphic);
        font-size: 1.2rem;
        color: var(--text-primary);
        z-index: 1000;
        transition: all 0.3s ease;
    `;
    
    let isDark = false;
    
    themeButton.addEventListener('click', function() {
        isDark = !isDark;
        
        if (isDark) {
            document.documentElement.style.setProperty('--bg-primary', '#1e293b');
            document.documentElement.style.setProperty('--bg-secondary', '#334155');
            document.documentElement.style.setProperty('--text-primary', '#f1f5f9');
            document.documentElement.style.setProperty('--text-secondary', '#cbd5e1');
            this.innerHTML = '<i class="fas fa-sun"></i>';
        } else {
            document.documentElement.style.setProperty('--bg-primary', '#f8fafc');
            document.documentElement.style.setProperty('--bg-secondary', '#ffffff');
            document.documentElement.style.setProperty('--text-primary', '#1e293b');
            document.documentElement.style.setProperty('--text-secondary', '#64748b');
            this.innerHTML = '<i class="fas fa-moon"></i>';
        }
    });
    
    document.body.appendChild(themeButton);
}

// Initialize theme switcher
document.addEventListener('DOMContentLoaded', createThemeSwitcher);

// Scroll to top button
function createScrollToTop() {
    const scrollButton = document.createElement('button');
    scrollButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
    scrollButton.style.cssText = `
        position: fixed;
        bottom: 170px;
        right: 30px;
        background: var(--gradient-primary);
        color: white;
        border: none;
        padding: 15px;
        border-radius: 50%;
        cursor: pointer;
        box-shadow: var(--shadow-3d);
        font-size: 1.2rem;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    `;
    
    scrollButton.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // Show/hide scroll button based on scroll position
    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            scrollButton.style.opacity = '1';
            scrollButton.style.visibility = 'visible';
        } else {
            scrollButton.style.opacity = '0';
            scrollButton.style.visibility = 'hidden';
        }
    });
    
    document.body.appendChild(scrollButton);
}

// Initialize scroll to top button
document.addEventListener('DOMContentLoaded', createScrollToTop);
