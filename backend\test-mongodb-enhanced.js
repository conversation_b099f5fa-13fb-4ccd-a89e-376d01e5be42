const mongoose = require('mongoose');

// Test MongoDB connection with enhanced error handling
const MONGODB_URI = 'mongodb+srv://medora:<EMAIL>/medora?retryWrites=true&w=majority';

async function testDatabaseConnection() {
  try {
    console.log('🚀 Starting MongoDB Atlas connection test...');
    console.log('🔗 Cluster:', 'medora.rauhtwy.mongodb.net');
    console.log('📊 Database:', 'medora');
    console.log('👤 User:', 'medora');
    console.log('🔐 URI:', MONGODB_URI.replace(/:[^:@]*@/, ':****@'));
    console.log('');
    
    console.log('⏳ Attempting connection...');
    await mongoose.connect(MONGODB_URI, {
      serverSelectionTimeoutMS: 15000, // 15 seconds timeout
      socketTimeoutMS: 45000, // 45 seconds
      maxPoolSize: 10,
      minPoolSize: 1,
    });
    
    console.log('✅ Successfully connected to MongoDB Atlas!');
    console.log('');
    
    // Test basic database operations
    console.log('🔍 Testing database operations...');
    const db = mongoose.connection.db;
    
    // Ping the database
    const adminDb = db.admin();
    const pingResult = await adminDb.ping();
    console.log('📡 Database ping successful:', pingResult);
    
    // List collections
    const collections = await db.listCollections().toArray();
    console.log('📁 Available collections:', collections.length > 0 ? collections.map(c => c.name) : 'No collections found');
    
    // Test document operations
    console.log('');
    console.log('📝 Testing document operations...');
    const testCollection = db.collection('connection_test');
    
    const testDoc = { 
      test: true, 
      timestamp: new Date(),
      message: 'MEDORA MongoDB Atlas connection test successful',
      version: '1.0.0'
    };
    
    // Insert test document
    const insertResult = await testCollection.insertOne(testDoc);
    console.log('✅ Test document inserted with ID:', insertResult.insertedId);
    
    // Retrieve test document
    const retrievedDoc = await testCollection.findOne({ test: true });
    console.log('📄 Test document retrieved:', {
      id: retrievedDoc._id,
      message: retrievedDoc.message,
      timestamp: retrievedDoc.timestamp
    });
    
    // Count documents
    const docCount = await testCollection.countDocuments();
    console.log('📊 Total test documents:', docCount);
    
    // Clean up test document
    const deleteResult = await testCollection.deleteOne({ test: true });
    console.log('🧹 Test document cleaned up, deleted:', deleteResult.deletedCount);
    
    // Test connection info
    const connectionState = mongoose.connection.readyState;
    const states = ['disconnected', 'connected', 'connecting', 'disconnecting'];
    console.log('🔗 Connection state:', states[connectionState]);
    
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    console.log('');
    console.log('🎉 Database connection test completed successfully!');
    console.log('✅ Your MongoDB Atlas setup is working correctly!');
    
  } catch (error) {
    console.error('');
    console.error('❌ Database connection failed!');
    console.error('💥 Error:', error.message);
    console.error('🔢 Error code:', error.code || 'N/A');
    console.error('📛 Error name:', error.name || 'N/A');
    console.error('');
    
    // Specific error handling
    if (error.code === 8000 || error.message.includes('authentication failed')) {
      console.log('🔍 AUTHENTICATION ERROR DETECTED');
      console.log('');
      console.log('Please check the following in MongoDB Atlas:');
      console.log('1. 👤 Database Access → Verify user "medora" exists');
      console.log('2. 🔑 Password is exactly: Americana123456789');
      console.log('3. 🛡️  User has "readWrite" or "Atlas admin" role');
      console.log('4. 📊 Database name "medora" is correct');
      console.log('5. 🌐 User is not restricted to specific databases');
      
    } else if (error.message.includes('ENOTFOUND') || error.message.includes('timeout')) {
      console.log('🌐 NETWORK ERROR DETECTED');
      console.log('');
      console.log('Please check the following:');
      console.log('1. 🔒 MongoDB Atlas Network Access → Whitelist your IP');
      console.log('2. 🌍 Internet connection is stable');
      console.log('3. 🛡️  Firewall is not blocking MongoDB connections (port 27017)');
      console.log('4. 🏢 Corporate network may be blocking external database connections');
      
    } else if (error.message.includes('bad auth')) {
      console.log('🔐 AUTHENTICATION CREDENTIALS ERROR');
      console.log('');
      console.log('The username or password is incorrect:');
      console.log('1. 👤 Username: medora');
      console.log('2. 🔑 Password: Americana123456789');
      console.log('3. 🔄 Try resetting the password in MongoDB Atlas');
      
    } else {
      console.log('❓ UNKNOWN ERROR');
      console.log('');
      console.log('This might be a configuration or network issue.');
    }
    
    console.log('');
    console.log('📋 For detailed troubleshooting, see: MONGODB_TROUBLESHOOTING.md');
    console.log('🌐 MongoDB Atlas Dashboard: https://cloud.mongodb.com/');
    
    process.exit(1);
  }
}

// Run the test
testDatabaseConnection();
