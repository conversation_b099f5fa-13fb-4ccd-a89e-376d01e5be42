const cron = require('node-cron');
const ScrapedData = require('../models/ScrapedData');
const dataProcessor = require('./dataProcessor');
const scraperService = require('./scraperService');
const logger = require('../utils/logger');

class ScheduledJobsService {
  constructor() {
    this.jobs = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize all scheduled jobs
   */
  init() {
    if (this.isInitialized) {
      logger.warn('Scheduled jobs already initialized');
      return;
    }

    try {
      // Process unprocessed scraped data every 30 minutes
      this.scheduleDataProcessing();
      
      // Clean up old processed data every day at 2 AM
      this.scheduleDataCleanup();
      
      // Update medical information every 6 hours
      this.scheduleMedicalDataUpdate();
      
      // Generate daily analytics report
      this.scheduleDailyAnalytics();
      
      this.isInitialized = true;
      logger.info('All scheduled jobs initialized successfully');
    } catch (error) {
      logger.error('Error initializing scheduled jobs:', error);
      throw error;
    }
  }

  /**
   * Schedule data processing job
   */
  scheduleDataProcessing() {
    const job = cron.schedule('*/30 * * * *', async () => {
      try {
        logger.info('Starting scheduled data processing job');
        
        // Find unprocessed scraped data
        const unprocessedData = await ScrapedData.find({
          'processed.processedAt': { $exists: false },
          createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
        }).limit(50);

        if (unprocessedData.length === 0) {
          logger.info('No unprocessed data found');
          return;
        }

        logger.info(`Processing ${unprocessedData.length} unprocessed data items`);
        
        const results = await Promise.allSettled(
          unprocessedData.map(async (data) => {
            try {
              await dataProcessor.processScrapedData(data.toObject());
              return { success: true, id: data._id };
            } catch (error) {
              logger.error(`Error processing data ${data._id}:`, error);
              return { success: false, id: data._id, error: error.message };
            }
          })
        );

        const successful = results.filter(r => r.value?.success).length;
        const failed = results.filter(r => !r.value?.success).length;
        
        logger.info(`Data processing job completed: ${successful} successful, ${failed} failed`);
      } catch (error) {
        logger.error('Error in scheduled data processing job:', error);
      }
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    this.jobs.set('dataProcessing', job);
    job.start();
    logger.info('Data processing job scheduled (every 30 minutes)');
  }

  /**
   * Schedule data cleanup job
   */
  scheduleDataCleanup() {
    const job = cron.schedule('0 2 * * *', async () => {
      try {
        logger.info('Starting scheduled data cleanup job');
        
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        
        // Remove old scraped data (older than 30 days)
        const deleteResult = await ScrapedData.deleteMany({
          createdAt: { $lt: thirtyDaysAgo },
          'metadata.keepPermanently': { $ne: true }
        });
        
        logger.info(`Data cleanup completed: ${deleteResult.deletedCount} old records removed`);
        
        // Archive important data instead of deleting
        const archiveResult = await ScrapedData.updateMany(
          {
            createdAt: { $lt: thirtyDaysAgo },
            'metadata.keepPermanently': true,
            archived: { $ne: true }
          },
          {
            $set: { archived: true, archivedAt: new Date() }
          }
        );
        
        logger.info(`Data archival completed: ${archiveResult.modifiedCount} records archived`);
      } catch (error) {
        logger.error('Error in scheduled data cleanup job:', error);
      }
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    this.jobs.set('dataCleanup', job);
    job.start();
    logger.info('Data cleanup job scheduled (daily at 2 AM UTC)');
  }

  /**
   * Schedule medical data update job
   */
  scheduleMedicalDataUpdate() {
    const job = cron.schedule('0 */6 * * *', async () => {
      try {
        logger.info('Starting scheduled medical data update job');
        
        // Update medical information from reliable sources
        const updateSources = [
          'https://www.mayoclinic.org',
          'https://www.webmd.com',
          'https://medlineplus.gov'
        ];
        
        const updatePromises = updateSources.map(async (source) => {
          try {
            // Scrape latest medical information
            const scrapedData = await scraperService.scrapeWebsite(source, {
              maxPages: 5,
              respectRobots: true,
              delay: 2000
            });
            
            // Process the scraped data
            if (scrapedData && scrapedData.length > 0) {
              await Promise.all(
                scrapedData.map(data => dataProcessor.processScrapedData(data))
              );
            }
            
            return { source, success: true, count: scrapedData?.length || 0 };
          } catch (error) {
            logger.error(`Error updating data from ${source}:`, error);
            return { source, success: false, error: error.message };
          }
        });
        
        const results = await Promise.allSettled(updatePromises);
        const successful = results.filter(r => r.value?.success).length;
        
        logger.info(`Medical data update completed: ${successful}/${updateSources.length} sources updated`);
      } catch (error) {
        logger.error('Error in scheduled medical data update job:', error);
      }
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    this.jobs.set('medicalDataUpdate', job);
    job.start();
    logger.info('Medical data update job scheduled (every 6 hours)');
  }

  /**
   * Schedule daily analytics job
   */
  scheduleDailyAnalytics() {
    const job = cron.schedule('0 1 * * *', async () => {
      try {
        logger.info('Starting scheduled daily analytics job');
        
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        yesterday.setHours(0, 0, 0, 0);
        
        const today = new Date(yesterday);
        today.setDate(today.getDate() + 1);
        
        // Generate analytics for yesterday
        const analytics = await this.generateDailyAnalytics(yesterday, today);
        
        // Log analytics summary
        logger.info('Daily analytics generated:', {
          date: yesterday.toISOString().split('T')[0],
          ...analytics.summary
        });
        
        // Store analytics in database (you might want to create an Analytics model)
        // await Analytics.create({ date: yesterday, data: analytics });
        
      } catch (error) {
        logger.error('Error in scheduled daily analytics job:', error);
      }
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    this.jobs.set('dailyAnalytics', job);
    job.start();
    logger.info('Daily analytics job scheduled (daily at 1 AM UTC)');
  }

  /**
   * Generate daily analytics
   * @param {Date} startDate - Start date for analytics
   * @param {Date} endDate - End date for analytics
   * @returns {Object} Analytics data
   */
  async generateDailyAnalytics(startDate, endDate) {
    try {
      const [scrapedDataStats, processingStats] = await Promise.all([
        // Scraped data statistics
        ScrapedData.aggregate([
          {
            $match: {
              createdAt: { $gte: startDate, $lt: endDate }
            }
          },
          {
            $group: {
              _id: '$source',
              count: { $sum: 1 },
              avgProcessingTime: { $avg: '$metadata.processingTime' },
              totalSize: { $sum: '$metadata.size' }
            }
          }
        ]),
        
        // Processing statistics
        ScrapedData.aggregate([
          {
            $match: {
              'processed.processedAt': { $gte: startDate, $lt: endDate }
            }
          },
          {
            $group: {
              _id: null,
              totalProcessed: { $sum: 1 },
              avgSymptoms: { $avg: { $size: '$processed.extractedSymptoms' } },
              avgConditions: { $avg: { $size: '$processed.extractedConditions' } },
              avgReadability: { $avg: '$processed.readabilityScore' }
            }
          }
        ])
      ]);

      return {
        date: startDate,
        summary: {
          totalScraped: scrapedDataStats.reduce((sum, stat) => sum + stat.count, 0),
          totalProcessed: processingStats[0]?.totalProcessed || 0,
          avgSymptoms: Math.round((processingStats[0]?.avgSymptoms || 0) * 100) / 100,
          avgConditions: Math.round((processingStats[0]?.avgConditions || 0) * 100) / 100,
          avgReadability: Math.round((processingStats[0]?.avgReadability || 0) * 100) / 100
        },
        sourceBreakdown: scrapedDataStats,
        processingStats: processingStats[0] || {}
      };
    } catch (error) {
      logger.error('Error generating daily analytics:', error);
      throw error;
    }
  }

  /**
   * Stop a specific job
   * @param {string} jobName - Name of the job to stop
   */
  stopJob(jobName) {
    const job = this.jobs.get(jobName);
    if (job) {
      job.stop();
      logger.info(`Stopped scheduled job: ${jobName}`);
    } else {
      logger.warn(`Job not found: ${jobName}`);
    }
  }

  /**
   * Start a specific job
   * @param {string} jobName - Name of the job to start
   */
  startJob(jobName) {
    const job = this.jobs.get(jobName);
    if (job) {
      job.start();
      logger.info(`Started scheduled job: ${jobName}`);
    } else {
      logger.warn(`Job not found: ${jobName}`);
    }
  }

  /**
   * Stop all scheduled jobs
   */
  stopAll() {
    this.jobs.forEach((job, name) => {
      job.stop();
      logger.info(`Stopped job: ${name}`);
    });
    logger.info('All scheduled jobs stopped');
  }

  /**
   * Get status of all jobs
   * @returns {Object} Status of all jobs
   */
  getJobsStatus() {
    const status = {};
    this.jobs.forEach((job, name) => {
      status[name] = {
        running: job.running || false,
        scheduled: job.scheduled || false
      };
    });
    return status;
  }

  /**
   * Manually trigger a job
   * @param {string} jobName - Name of the job to trigger
   */
  async triggerJob(jobName) {
    try {
      logger.info(`Manually triggering job: ${jobName}`);
      
      switch (jobName) {
        case 'dataProcessing':
          await this.scheduleDataProcessing();
          break;
        case 'dataCleanup':
          await this.scheduleDataCleanup();
          break;
        case 'medicalDataUpdate':
          await this.scheduleMedicalDataUpdate();
          break;
        case 'dailyAnalytics':
          await this.scheduleDailyAnalytics();
          break;
        default:
          throw new Error(`Unknown job: ${jobName}`);
      }
      
      logger.info(`Successfully triggered job: ${jobName}`);
    } catch (error) {
      logger.error(`Error triggering job ${jobName}:`, error);
      throw error;
    }
  }
}

module.exports = new ScheduledJobsService();