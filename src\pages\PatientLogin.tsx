import React from 'react';
import { motion } from 'framer-motion';
import PatientAuth from '@/components/auth/PatientAuth';
import { useAuth } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';

const PatientLogin: React.FC = () => {
  const { user, isAuthenticated } = useAuth();

  // Redirect if already authenticated as patient
  if (isAuthenticated && user?.role === 'patient') {
    return <Navigate to="/patient-dashboard" replace />;
  }

  // Redirect if authenticated as different role
  if (isAuthenticated && user?.role !== 'patient') {
    const redirectPath = user?.role === 'doctor' ? '/doctor-dashboard' : 
                        user?.role === 'nurse' ? '/nurse-dashboard' : 
                        user?.role === 'admin' ? '/admin-dashboard' : '/';
    return <Navigate to={redirectPath} replace />;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <PatientAuth 
        redirectTo="/patient-dashboard"
        onSuccess={() => {
          // Handle successful authentication
          console.log('Patient authentication successful');
        }}
      />
    </motion.div>
  );
};

export default PatientLogin;
