import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardHeader, CardContent, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  ArrowLeft, 
  Search, 
  MessageCircle, 
  Phone, 
  Mail, 
  Clock,
  HelpCircle,
  Book,
  Video,
  Download,
  Users,
  Shield,
  Zap,
  ChevronDown,
  ChevronRight,
  ExternalLink
} from 'lucide-react';

const HelpSupport = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  const faqs = [
    {
      question: "How do I get started with MEDORA?",
      answer: "Getting started is easy! Simply sign up for an account, complete your profile, and you can immediately begin using our AI-powered medical assistant. For healthcare providers, we recommend completing the verification process to access advanced features."
    },
    {
      question: "Is MEDORA HIPAA compliant?",
      answer: "Yes, MEDORA is fully HIPAA compliant. We implement enterprise-grade security measures including end-to-end encryption, secure data storage, and strict access controls to protect patient information."
    },
    {
      question: "What types of medical conditions can MEDORA help with?",
      answer: "MEDORA can assist with a wide range of medical conditions including general health inquiries, symptom analysis, medication information, and preliminary diagnosis support. However, it should not replace professional medical advice."
    },
    {
      question: "How accurate is the AI diagnosis?",
      answer: "Our AI has a 98.5% accuracy rate based on extensive testing. However, AI recommendations should always be verified by qualified healthcare professionals and should not be used as the sole basis for medical decisions."
    },
    {
      question: "Can I integrate MEDORA with my existing EMR system?",
      answer: "Yes, MEDORA offers API integration capabilities that allow seamless integration with most major EMR systems. Contact our support team for specific integration requirements."
    },
    {
      question: "What are the subscription plans available?",
      answer: "We offer multiple plans including a free tier for basic features, professional plans for individual practitioners, and enterprise solutions for healthcare organizations. Visit our pricing page for detailed information."
    },
    {
      question: "How do I report a technical issue?",
      answer: "You can report technical issues through our support portal, email <NAME_EMAIL>, or use the live chat feature. Include detailed information about the issue for faster resolution."
    },
    {
      question: "Is my data secure with MEDORA?",
      answer: "Absolutely. We use bank-level encryption, secure cloud infrastructure, and follow strict data protection protocols. Your data is never shared with third parties without explicit consent."
    }
  ];

  const supportChannels = [
    {
      title: "Live Chat",
      description: "Get instant help from our support team",
      icon: MessageCircle,
      availability: "24/7",
      action: "Start Chat",
      color: "bg-blue-500"
    },
    {
      title: "Email Support",
      description: "Send us detailed questions or feedback",
      icon: Mail,
      availability: "Response within 24 hours",
      action: "Send Email",
      color: "bg-green-500"
    },
    {
      title: "Phone Support",
      description: "Speak directly with our experts",
      icon: Phone,
      availability: "Mon-Fri 9AM-6PM EST",
      action: "Call Now",
      color: "bg-purple-500"
    }
  ];

  const resources = [
    {
      title: "User Guide",
      description: "Complete guide to using MEDORA",
      icon: Book,
      type: "Documentation"
    },
    {
      title: "Video Tutorials",
      description: "Step-by-step video instructions",
      icon: Video,
      type: "Video"
    },
    {
      title: "API Documentation",
      description: "Technical documentation for developers",
      icon: Download,
      type: "Technical"
    },
    {
      title: "Community Forum",
      description: "Connect with other MEDORA users",
      icon: Users,
      type: "Community"
    }
  ];

  const toggleFaq = (index: number) => {
    setExpandedFaq(expandedFaq === index ? null : index);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/')}
              className="text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
            <div className="flex items-center space-x-3">
              <img 
                src="/lovable-uploads/logo-png.png" 
                alt="MEDORA Logo" 
                className="h-8 w-8 object-contain"
              />
              <span className="text-xl font-bold" style={{color: '#9ACD32'}}>MEDORA</span>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold mb-4">Help & Support</h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Get the help you need to make the most of MEDORA's AI-powered medical assistance
          </p>
          
          {/* Search Bar */}
          <div className="max-w-md mx-auto relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search for help..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Support Channels */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">Get Support</h2>
          <div className="grid md:grid-cols-3 gap-6">
            {supportChannels.map((channel, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6 text-center">
                  <div className={`w-16 h-16 ${channel.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                    <channel.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{channel.title}</h3>
                  <p className="text-muted-foreground mb-3">{channel.description}</p>
                  <Badge variant="outline" className="mb-4">
                    <Clock className="h-3 w-3 mr-1" />
                    {channel.availability}
                  </Badge>
                  <Button className="w-full">{channel.action}</Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">Frequently Asked Questions</h2>
          <div className="max-w-3xl mx-auto space-y-4">
            {faqs.map((faq, index) => (
              <Card key={index} className="overflow-hidden">
                <CardHeader 
                  className="cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => toggleFaq(index)}
                >
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{faq.question}</CardTitle>
                    {expandedFaq === index ? (
                      <ChevronDown className="h-5 w-5" />
                    ) : (
                      <ChevronRight className="h-5 w-5" />
                    )}
                  </div>
                </CardHeader>
                {expandedFaq === index && (
                  <CardContent>
                    <p className="text-muted-foreground">{faq.answer}</p>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        </div>

        {/* Resources Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">Resources</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {resources.map((resource, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <resource.icon className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">{resource.title}</h3>
                  <p className="text-muted-foreground text-sm mb-3">{resource.description}</p>
                  <Badge variant="secondary">{resource.type}</Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Contact Form */}
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl text-center">Still Need Help?</CardTitle>
              <p className="text-center text-muted-foreground">
                Send us a message and we'll get back to you as soon as possible
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Name</label>
                  <Input placeholder="Your name" />
                </div>
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <Input placeholder="<EMAIL>" type="email" />
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Subject</label>
                <Input placeholder="How can we help?" />
              </div>
              <div>
                <label className="text-sm font-medium">Message</label>
                <Textarea placeholder="Describe your issue or question..." rows={5} />
              </div>
              <Button className="w-full">Send Message</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default HelpSupport;
