const natural = require('natural');
const compromise = require('compromise');
const ScrapedData = require('../models/ScrapedData');
const MLAnalysis = require('../models/MLAnalysis');
const logger = require('../utils/logger');
const mlService = require('./mlService');

class DataProcessor {
  constructor() {
    this.stemmer = natural.PorterStemmer;
    this.tokenizer = new natural.WordTokenizer();
    this.sentiment = new natural.SentimentAnalyzer('English', 
      natural.PorterStemmer, ['negation', 'dann']);
  }

  /**
   * Process raw scraped medical data
   * @param {Object} rawData - Raw scraped data
   * @returns {Object} Processed data
   */
  async processScrapedData(rawData) {
    try {
      logger.info('Processing scraped medical data', { source: rawData.source });

      const processedData = {
        ...rawData,
        processed: {
          extractedSymptoms: await this.extractSymptoms(rawData.content),
          extractedConditions: await this.extractConditions(rawData.content),
          extractedTreatments: await this.extractTreatments(rawData.content),
          keyPhrases: await this.extractKeyPhrases(rawData.content),
          sentiment: await this.analyzeSentiment(rawData.content),
          readabilityScore: this.calculateReadability(rawData.content),
          medicalTerms: await this.extractMedicalTerms(rawData.content),
          processedAt: new Date()
        }
      };

      // Save processed data
      const scrapedDataDoc = new ScrapedData(processedData);
      await scrapedDataDoc.save();

      logger.info('Successfully processed scraped data', { 
        id: scrapedDataDoc._id,
        symptomsFound: processedData.processed.extractedSymptoms.length,
        conditionsFound: processedData.processed.extractedConditions.length
      });

      return processedData;
    } catch (error) {
      logger.error('Error processing scraped data:', error);
      throw error;
    }
  }

  /**
   * Extract symptoms from medical text
   * @param {string} text - Medical text content
   * @returns {Array} Array of extracted symptoms
   */
  async extractSymptoms(text) {
    try {
      const doc = compromise(text);
      const symptoms = [];

      // Common symptom patterns
      const symptomPatterns = [
        /\b(pain|ache|aching|hurt|hurting|sore|soreness)\b/gi,
        /\b(fever|temperature|hot|chills|sweating)\b/gi,
        /\b(nausea|vomiting|dizzy|dizziness|headache)\b/gi,
        /\b(cough|coughing|shortness of breath|breathing)\b/gi,
        /\b(fatigue|tired|weakness|exhaustion)\b/gi,
        /\b(rash|itching|swelling|inflammation)\b/gi,
        /\b(bleeding|bruising|numbness|tingling)\b/gi
      ];

      // Extract using patterns
      symptomPatterns.forEach(pattern => {
        const matches = text.match(pattern);
        if (matches) {
          symptoms.push(...matches.map(match => match.toLowerCase().trim()));
        }
      });

      // Extract using NLP
      const sentences = doc.sentences().out('array');
      sentences.forEach(sentence => {
        const sentenceDoc = compromise(sentence);
        const nouns = sentenceDoc.nouns().out('array');
        const adjectives = sentenceDoc.adjectives().out('array');
        
        // Look for symptom-related nouns and adjectives
        [...nouns, ...adjectives].forEach(word => {
          if (this.isLikelySymptom(word)) {
            symptoms.push(word.toLowerCase());
          }
        });
      });

      // Remove duplicates and filter
      return [...new Set(symptoms)]
        .filter(symptom => symptom.length > 2)
        .slice(0, 20); // Limit to top 20
    } catch (error) {
      logger.error('Error extracting symptoms:', error);
      return [];
    }
  }

  /**
   * Extract medical conditions from text
   * @param {string} text - Medical text content
   * @returns {Array} Array of extracted conditions
   */
  async extractConditions(text) {
    try {
      const conditions = [];
      const doc = compromise(text);

      // Common condition patterns
      const conditionPatterns = [
        /\b(diabetes|hypertension|asthma|pneumonia|bronchitis)\b/gi,
        /\b(arthritis|osteoporosis|fibromyalgia|lupus)\b/gi,
        /\b(depression|anxiety|bipolar|schizophrenia)\b/gi,
        /\b(cancer|tumor|carcinoma|lymphoma|leukemia)\b/gi,
        /\b(infection|virus|bacterial|fungal)\b/gi,
        /\b(syndrome|disease|disorder|condition)\b/gi
      ];

      conditionPatterns.forEach(pattern => {
        const matches = text.match(pattern);
        if (matches) {
          conditions.push(...matches.map(match => match.toLowerCase().trim()));
        }
      });

      // Extract proper nouns that might be conditions
      const properNouns = doc.match('#ProperNoun').out('array');
      properNouns.forEach(noun => {
        if (this.isLikelyCondition(noun)) {
          conditions.push(noun.toLowerCase());
        }
      });

      return [...new Set(conditions)].slice(0, 15);
    } catch (error) {
      logger.error('Error extracting conditions:', error);
      return [];
    }
  }

  /**
   * Extract treatments from text
   * @param {string} text - Medical text content
   * @returns {Array} Array of extracted treatments
   */
  async extractTreatments(text) {
    try {
      const treatments = [];
      
      // Treatment patterns
      const treatmentPatterns = [
        /\b(medication|medicine|drug|prescription|pill|tablet)\b/gi,
        /\b(surgery|operation|procedure|therapy|treatment)\b/gi,
        /\b(antibiotic|antiviral|painkiller|analgesic)\b/gi,
        /\b(rest|exercise|diet|lifestyle|rehabilitation)\b/gi
      ];

      treatmentPatterns.forEach(pattern => {
        const matches = text.match(pattern);
        if (matches) {
          treatments.push(...matches.map(match => match.toLowerCase().trim()));
        }
      });

      return [...new Set(treatments)].slice(0, 10);
    } catch (error) {
      logger.error('Error extracting treatments:', error);
      return [];
    }
  }

  /**
   * Extract key phrases from text
   * @param {string} text - Text content
   * @returns {Array} Array of key phrases
   */
  async extractKeyPhrases(text) {
    try {
      const doc = compromise(text);
      const phrases = [];

      // Extract noun phrases
      const nounPhrases = doc.match('#Noun+ #Noun').out('array');
      phrases.push(...nounPhrases);

      // Extract verb phrases
      const verbPhrases = doc.match('#Verb #Adverb? #Noun').out('array');
      phrases.push(...verbPhrases);

      return [...new Set(phrases)]
        .filter(phrase => phrase.length > 5)
        .slice(0, 15);
    } catch (error) {
      logger.error('Error extracting key phrases:', error);
      return [];
    }
  }

  /**
   * Analyze sentiment of text
   * @param {string} text - Text content
   * @returns {Object} Sentiment analysis result
   */
  async analyzeSentiment(text) {
    try {
      const tokens = this.tokenizer.tokenize(text.toLowerCase());
      const stemmedTokens = tokens.map(token => this.stemmer.stem(token));
      const score = this.sentiment.getSentiment(stemmedTokens);

      return {
        score,
        label: score > 0 ? 'positive' : score < 0 ? 'negative' : 'neutral',
        confidence: Math.abs(score)
      };
    } catch (error) {
      logger.error('Error analyzing sentiment:', error);
      return { score: 0, label: 'neutral', confidence: 0 };
    }
  }

  /**
   * Calculate readability score
   * @param {string} text - Text content
   * @returns {number} Readability score
   */
  calculateReadability(text) {
    try {
      const sentences = text.split(/[.!?]+/).length;
      const words = text.split(/\s+/).length;
      const syllables = this.countSyllables(text);

      // Flesch Reading Ease Score
      const score = 206.835 - (1.015 * (words / sentences)) - (84.6 * (syllables / words));
      return Math.max(0, Math.min(100, score));
    } catch (error) {
      logger.error('Error calculating readability:', error);
      return 50; // Default moderate readability
    }
  }

  /**
   * Extract medical terms from text
   * @param {string} text - Text content
   * @returns {Array} Array of medical terms
   */
  async extractMedicalTerms(text) {
    try {
      const medicalTerms = [];
      const doc = compromise(text);

      // Medical prefixes and suffixes
      const medicalPatterns = [
        /\b\w*ology\b/gi,  // -ology (cardiology, neurology)
        /\b\w*itis\b/gi,   // -itis (arthritis, hepatitis)
        /\b\w*osis\b/gi,   // -osis (diagnosis, prognosis)
        /\b\w*pathy\b/gi,  // -pathy (neuropathy, myopathy)
        /\b\w*therapy\b/gi, // -therapy (chemotherapy, physiotherapy)
        /\b\w*gram\b/gi,   // -gram (electrocardiogram, mammogram)
        /\b\w*scopy\b/gi   // -scopy (endoscopy, colonoscopy)
      ];

      medicalPatterns.forEach(pattern => {
        const matches = text.match(pattern);
        if (matches) {
          medicalTerms.push(...matches.map(match => match.toLowerCase().trim()));
        }
      });

      return [...new Set(medicalTerms)].slice(0, 20);
    } catch (error) {
      logger.error('Error extracting medical terms:', error);
      return [];
    }
  }

  /**
   * Check if a word is likely a symptom
   * @param {string} word - Word to check
   * @returns {boolean} True if likely a symptom
   */
  isLikelySymptom(word) {
    const symptomKeywords = [
      'pain', 'ache', 'fever', 'nausea', 'cough', 'fatigue', 'weakness',
      'dizziness', 'headache', 'rash', 'swelling', 'bleeding', 'numbness'
    ];
    return symptomKeywords.some(keyword => word.toLowerCase().includes(keyword));
  }

  /**
   * Check if a word is likely a medical condition
   * @param {string} word - Word to check
   * @returns {boolean} True if likely a condition
   */
  isLikelyCondition(word) {
    const conditionSuffixes = ['itis', 'osis', 'pathy', 'syndrome', 'disease'];
    return conditionSuffixes.some(suffix => word.toLowerCase().endsWith(suffix));
  }

  /**
   * Count syllables in text (approximation)
   * @param {string} text - Text content
   * @returns {number} Estimated syllable count
   */
  countSyllables(text) {
    const words = text.toLowerCase().split(/\s+/);
    let syllableCount = 0;

    words.forEach(word => {
      const vowels = word.match(/[aeiouy]+/g);
      syllableCount += vowels ? vowels.length : 1;
    });

    return syllableCount;
  }

  /**
   * Enrich scraped data with ML analysis
   * @param {string} scrapedDataId - ID of scraped data
   * @returns {Object} Enriched data with ML analysis
   */
  async enrichWithMLAnalysis(scrapedDataId) {
    try {
      const scrapedData = await ScrapedData.findById(scrapedDataId);
      if (!scrapedData) {
        throw new Error('Scraped data not found');
      }

      // Perform ML analysis on extracted symptoms
      const symptoms = scrapedData.processed.extractedSymptoms;
      if (symptoms.length > 0) {
        const mlAnalysis = await mlService.analyzeSymptoms(symptoms);
        
        // Save ML analysis
        const analysisDoc = new MLAnalysis({
          type: 'scraped_data_analysis',
          inputData: { symptoms, scrapedDataId },
          results: mlAnalysis,
          confidence: mlAnalysis.confidence || 0.5,
          createdAt: new Date()
        });
        
        await analysisDoc.save();
        
        // Update scraped data with ML analysis reference
        scrapedData.mlAnalysisId = analysisDoc._id;
        await scrapedData.save();
        
        logger.info('Enriched scraped data with ML analysis', {
          scrapedDataId,
          mlAnalysisId: analysisDoc._id
        });
        
        return {
          scrapedData,
          mlAnalysis: analysisDoc
        };
      }
      
      return { scrapedData };
    } catch (error) {
      logger.error('Error enriching with ML analysis:', error);
      throw error;
    }
  }
}

module.exports = new DataProcessor();