<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEDORA - Application Administration and Enhancement Manual</title>
    <link href="https://fonts.googleapis.com/css2?family=Bruno+Ace+SC:wght@400&family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
</head>
<body>
    <!-- Header -->
    <header class="header" data-aos="fade-down">
        <div class="container">
            <h1 class="logo">MEDORA</h1>
            <p class="subtitle">Application Administration and Enhancement Manual</p>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav" id="mainNav">
        <div class="container">
            <ul class="nav-list">
                <li class="nav-item"><a href="#overview" data-section="overview">Overview</a></li>
                <li class="nav-item"><a href="#installation" data-section="installation">Installation</a></li>
                <li class="nav-item"><a href="#cors" data-section="cors">CORS Setup</a></li>
                <li class="nav-item"><a href="#backend" data-section="backend">Backend</a></li>
                <li class="nav-item"><a href="#frontend" data-section="frontend">Frontend</a></li>
                <li class="nav-item"><a href="#database" data-section="database">Database</a></li>
                <li class="nav-item"><a href="#deployment" data-section="deployment">Deployment</a></li>
                <li class="nav-item"><a href="#security" data-section="security">Security</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container">
        <!-- System Overview Section -->
        <section id="overview" class="section" data-aos="fade-up">
            <div class="section-header">
                <h2 class="section-title">System Overview</h2>
                <p class="section-subtitle">Understanding MEDORA's architecture and components</p>
            </div>

            <div class="card" data-aos="fade-up" data-aos-delay="100">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <h3 class="card-title">Architecture Overview</h3>
                </div>
                <p>MEDORA is a comprehensive AI-powered medical assistant platform built with modern web technologies.</p>
                
                <div class="flow-diagram">
                    <div class="flow-item" data-aos="zoom-in" data-aos-delay="200">
                        <div class="flow-icon">
                            <i class="fab fa-react"></i>
                        </div>
                        <div>Frontend<br><small>React + TypeScript</small></div>
                    </div>
                    <div class="flow-item" data-aos="zoom-in" data-aos-delay="300">
                        <div class="flow-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div>Backend<br><small>Node.js + Express</small></div>
                    </div>
                    <div class="flow-item" data-aos="zoom-in" data-aos-delay="400">
                        <div class="flow-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div>Database<br><small>MongoDB</small></div>
                    </div>
                </div>

                <div class="alert alert-info" data-aos="fade-up" data-aos-delay="500">
                    <strong>💡 Key Features:</strong> AI-powered diagnosis, real-time chat, patient management, payment integration, and HIPAA compliance.
                </div>
            </div>
        </section>

        <!-- Installation Section -->
        <section id="installation" class="section" data-aos="fade-up">
            <div class="section-header">
                <h2 class="section-title">Installation Guide</h2>
                <p class="section-subtitle">Step-by-step installation process with visual guides</p>
            </div>

            <div class="card" data-aos="fade-up" data-aos-delay="100">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <h3 class="card-title">Prerequisites</h3>
                </div>
                
                <div class="flow-diagram">
                    <div class="flow-item" data-aos="zoom-in" data-aos-delay="200">
                        <div class="flow-icon">
                            <i class="fab fa-node-js"></i>
                        </div>
                        <div>Node.js 18+<br><small>JavaScript Runtime</small></div>
                    </div>
                    <div class="flow-item" data-aos="zoom-in" data-aos-delay="300">
                        <div class="flow-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div>MongoDB 6+<br><small>Database</small></div>
                    </div>
                    <div class="flow-item" data-aos="zoom-in" data-aos-delay="400">
                        <div class="flow-icon">
                            <i class="fab fa-git-alt"></i>
                        </div>
                        <div>Git<br><small>Version Control</small></div>
                    </div>
                </div>

                <div class="alert alert-warning" data-aos="fade-up" data-aos-delay="500">
                    <strong>⚠️ Important:</strong> Ensure all prerequisites are installed before proceeding with the installation.
                </div>
            </div>

            <div class="card" data-aos="fade-up" data-aos-delay="200">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3 class="card-title">Installation Steps</h3>
                </div>

                <div class="step-guide">
                    <div class="step" data-aos="fade-right" data-aos-delay="100">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4 class="step-title">Purchase Licensne</h4>
                            <p class="step-description">Download the MEDORA source code</p>
                            <div class="code-block">
                                <pre>Purchase &lt;repository-url&gt;
cd medora-main</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step" data-aos="fade-right" data-aos-delay="200">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4 class="step-title">Install Frontend Dependencies</h4>
                            <p class="step-description">Install all required packages for the React frontend</p>
                            <div class="code-block">
                                <pre>npm install</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step" data-aos="fade-right" data-aos-delay="300">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4 class="step-title">Install Backend Dependencies</h4>
                            <p class="step-description">Navigate to backend folder and install server dependencies</p>
                            <div class="code-block">
                                <pre>cd backend
npm install
cd ..</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step" data-aos="fade-right" data-aos-delay="400">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4 class="step-title">Configure Environment</h4>
                            <p class="step-description">Set up environment variables for your installation</p>
                            <div class="code-block">
                                <pre>cp backend/.env.example backend/.env
# Edit backend/.env with your configurations</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step" data-aos="fade-right" data-aos-delay="500">
                        <div class="step-number">5</div>
                        <div class="step-content">
                            <h4 class="step-title">Start MongoDB</h4>
                            <p class="step-description">Ensure MongoDB service is running on your system</p>
                            <div class="code-block">
                                <pre># Windows
net start MongoDB

# macOS
brew services start mongodb-community

# Linux
sudo systemctl start mongod</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step" data-aos="fade-right" data-aos-delay="600">
                        <div class="step-number">6</div>
                        <div class="step-content">
                            <h4 class="step-title">Launch Application</h4>
                            <p class="step-description">Start both frontend and backend servers</p>
                            <div class="code-block">
                                <pre>npm start</pre>
                            </div>
                            <div class="alert alert-success">
                                <strong>✅ Success!</strong> Your application should now be running at:
                                <br>• Frontend: http://localhost:1200
                                <br>• Backend: http://localhost:5000
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CORS Configuration Section -->
        <section id="cors" class="section">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16" data-aos="fade-up">
                    <h2 class="section-title">CORS Configuration</h2>
                    <p class="section-subtitle">Cross-Origin Resource Sharing Setup</p>
                </div>

                <div class="grid md:grid-cols-2 gap-8">
                    <div class="neumorphic-card" data-aos="fade-right">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800">Backend CORS Setup</h3>
                        <div class="code-block">
                            <pre><code>// server.js
const cors = require('cors');

const corsOptions = {
  origin: [
    'http://localhost:3000',
    'http://localhost:5173',
    'https://yourdomain.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
};

app.use(cors(corsOptions));</code></pre>
                        </div>
                    </div>

                    <div class="neumorphic-card" data-aos="fade-left">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800">Environment Variables</h3>
                        <div class="code-block">
                            <pre><code># .env
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:5000
CORS_ORIGIN=http://localhost:3000,https://yourdomain.com
NODE_ENV=development</code></pre>
                        </div>
                    </div>
                </div>

                <div class="step-guide mt-12" data-aos="fade-up">
                    <h3 class="text-xl font-semibold mb-6 text-gray-800">CORS Configuration Steps</h3>
                    <div class="step-container">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4>Install CORS Package</h4>
                                <p>Run <code>npm install cors</code> in your backend directory</p>
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4>Configure CORS Options</h4>
                                <p>Set up allowed origins, methods, and headers</p>
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4>Apply Middleware</h4>
                                <p>Use app.use(cors(corsOptions)) in your Express app</p>
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h4>Test Configuration</h4>
                                <p>Verify frontend can communicate with backend</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Backend Setup Section -->
        <section id="backend" class="section bg-gray-50">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16" data-aos="fade-up">
                    <h2 class="section-title">Backend Setup</h2>
                    <p class="section-subtitle">Node.js & Express Server Configuration</p>
                </div>

                <div class="grid lg:grid-cols-3 gap-8">
                    <div class="neumorphic-card" data-aos="fade-up" data-aos-delay="100">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800">Dependencies</h3>
                        <div class="code-block">
                            <pre><code>npm install express mongoose cors
npm install dotenv bcryptjs jsonwebtoken
npm install helmet express-rate-limit
npm install socket.io multer
npm install nodemailer stripe</code></pre>
                        </div>
                    </div>

                    <div class="neumorphic-card" data-aos="fade-up" data-aos-delay="200">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800">Server Structure</h3>
                        <div class="code-block">
                            <pre><code>backend/
├── server.js
├── config/
│   ├── database.js
│   └── cors.js
├── routes/
│   ├── auth.js
│   ├── users.js
│   └── api.js
├── middleware/
│   ├── auth.js
│   └── validation.js
└── models/
    ├── User.js
    └── Patient.js</code></pre>
                        </div>
                    </div>

                    <div class="neumorphic-card" data-aos="fade-up" data-aos-delay="300">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800">Environment Setup</h3>
                        <div class="code-block">
                            <pre><code># .env
PORT=5000
MONGODB_URI=mongodb://localhost:27017/medora
JWT_SECRET=your_jwt_secret_here
OPENAI_API_KEY=your_openai_key
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password</code></pre>
                        </div>
                    </div>
                </div>

                <div class="flow-diagram mt-12" data-aos="fade-up">
                    <h3 class="text-xl font-semibold mb-6 text-gray-800 text-center">Backend Architecture Flow</h3>
                    <div class="flow-container">
                        <div class="flow-item">
                            <div class="flow-box">Client Request</div>
                            <div class="flow-arrow">→</div>
                        </div>
                        <div class="flow-item">
                            <div class="flow-box">CORS Middleware</div>
                            <div class="flow-arrow">→</div>
                        </div>
                        <div class="flow-item">
                            <div class="flow-box">Authentication</div>
                            <div class="flow-arrow">→</div>
                        </div>
                        <div class="flow-item">
                            <div class="flow-box">Route Handler</div>
                            <div class="flow-arrow">→</div>
                        </div>
                        <div class="flow-item">
                            <div class="flow-box">Database</div>
                            <div class="flow-arrow">→</div>
                        </div>
                        <div class="flow-item">
                            <div class="flow-box">Response</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Database Configuration Section -->
        <section id="database" class="section">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16" data-aos="fade-up">
                    <h2 class="section-title">Database Configuration</h2>
                    <p class="section-subtitle">MongoDB Setup & Schema Design</p>
                </div>

                <div class="grid md:grid-cols-2 gap-8">
                    <div class="neumorphic-card" data-aos="fade-right">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800">MongoDB Connection</h3>
                        <div class="code-block">
                            <pre><code>// config/database.js
const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error(error);
    process.exit(1);
  }
};

module.exports = connectDB;</code></pre>
                        </div>
                    </div>

                    <div class="neumorphic-card" data-aos="fade-left">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800">User Schema</h3>
                        <div class="code-block">
                            <pre><code>// models/User.js
const userSchema = new mongoose.Schema({
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: {
    type: String,
    enum: ['patient', 'doctor', 'nurse', 'admin'],
    default: 'patient'
  },
  isVerified: { type: Boolean, default: false },
  profile: {
    phone: String,
    dateOfBirth: Date,
    gender: String,
    address: String
  }
}, { timestamps: true });</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Deployment Section -->
        <section id="deployment" class="section bg-gray-50">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16" data-aos="fade-up">
                    <h2 class="section-title">Deployment Guide</h2>
                    <p class="section-subtitle">Production Deployment Strategies</p>
                </div>

                <div class="grid lg:grid-cols-3 gap-8">
                    <div class="neumorphic-card" data-aos="fade-up" data-aos-delay="100">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800">Docker Deployment</h3>
                        <div class="code-block">
                            <pre><code># Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 5000
CMD ["npm", "start"]

# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"</code></pre>
                        </div>
                    </div>

                    <div class="neumorphic-card" data-aos="fade-up" data-aos-delay="200">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800">VPS Deployment</h3>
                        <div class="code-block">
                            <pre><code># Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2
npm install -g pm2

# Deploy application
git clone your-repo
cd medora
npm install
npm run build

# Start with PM2
pm2 start ecosystem.config.js
pm2 startup
pm2 save</code></pre>
                        </div>
                    </div>

                    <div class="neumorphic-card" data-aos="fade-up" data-aos-delay="300">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800">Cloud Platforms</h3>
                        <div class="code-block">
                            <pre><code># Heroku
heroku create medora-app
heroku addons:create mongolab:sandbox
git push heroku main

# Vercel (Frontend)
npm install -g vercel
vercel --prod

# Railway
railway login
railway new
railway add
railway deploy</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Security Section -->
        <section id="security" class="section">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16" data-aos="fade-up">
                    <h2 class="section-title">Security & Compliance</h2>
                    <p class="section-subtitle">HIPAA-Compliant Security Measures</p>
                </div>

                <div class="grid md:grid-cols-2 gap-8">
                    <div class="neumorphic-card" data-aos="fade-right">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800">Security Headers</h3>
                        <div class="code-block">
                            <pre><code>// Security middleware
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"]
    }
  }
}));

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});

app.use('/api/', limiter);</code></pre>
                        </div>
                    </div>

                    <div class="neumorphic-card" data-aos="fade-left">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800">Data Encryption</h3>
                        <div class="code-block">
                            <pre><code>// Password hashing
const bcrypt = require('bcryptjs');

const hashPassword = async (password) => {
  const salt = await bcrypt.genSalt(12);
  return bcrypt.hash(password, salt);
};

// JWT tokens
const jwt = require('jsonwebtoken');

const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: '24h'
  });
};

// Data encryption at rest
const crypto = require('crypto');
const algorithm = 'aes-256-gcm';</code></pre>
                        </div>
                    </div>
                </div>

                <div class="security-checklist mt-12" data-aos="fade-up">
                    <h3 class="text-xl font-semibold mb-6 text-gray-800">HIPAA Compliance Checklist</h3>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="checklist-column">
                            <div class="checklist-item">
                                <span class="checkmark">✓</span>
                                <span>End-to-end encryption for all data transmission</span>
                            </div>
                            <div class="checklist-item">
                                <span class="checkmark">✓</span>
                                <span>Secure user authentication and authorization</span>
                            </div>
                            <div class="checklist-item">
                                <span class="checkmark">✓</span>
                                <span>Audit logging for all data access</span>
                            </div>
                            <div class="checklist-item">
                                <span class="checkmark">✓</span>
                                <span>Regular security assessments and updates</span>
                            </div>
                        </div>
                        <div class="checklist-column">
                            <div class="checklist-item">
                                <span class="checkmark">✓</span>
                                <span>Data backup and disaster recovery plans</span>
                            </div>
                            <div class="checklist-item">
                                <span class="checkmark">✓</span>
                                <span>Access controls and role-based permissions</span>
                            </div>
                            <div class="checklist-item">
                                <span class="checkmark">✓</span>
                                <span>Secure data storage and handling procedures</span>
                            </div>
                            <div class="checklist-item">
                                <span class="checkmark">✓</span>
                                <span>Employee training and compliance monitoring</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer" data-aos="fade-up">
        <div class="container">
            <h3 class="footer-logo">MEDORA</h3>
            <p>Application Administration and Enhancement Manual</p>
            <p class="footer-version">Version 1.0.0 | Last Updated: September 2025</p>
            <div class="footer-links">
                <a href="#overview">Overview</a>
                <a href="#installation">Installation</a>
                <a href="#cors">CORS</a>
                <a href="#backend">Backend</a>
                <a href="#database">Database</a>
                <a href="#deployment">Deployment</a>
                <a href="#security">Security</a>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="js/main.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/components.js"></script>
</body>
</html>
