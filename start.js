#!/usr/bin/env node

/**
 * MEDORA System Startup Script
 * Starts both frontend and backend servers
 */

import { spawn } from 'child_process';
import path from 'path';

console.log('🚀 Starting MEDORA Medical AI System...\n');

// Start backend server
console.log('📡 Starting backend server...');
const backend = spawn('node', ['backend/simple-server.js'], {
  stdio: 'inherit',
  cwd: process.cwd()
});

// Start frontend dev server
console.log('🎨 Starting frontend dev server...');
const frontend = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  cwd: process.cwd(),
  shell: true
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down MEDORA system...');
  backend.kill('SIGINT');
  frontend.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down MEDORA system...');
  backend.kill('SIGTERM');
  frontend.kill('SIGTERM');
  process.exit(0);
});

backend.on('exit', (code) => {
  if (code !== 0) {
    console.error(`❌ Backend server exited with code ${code}`);
  }
});

frontend.on('exit', (code) => {
  if (code !== 0) {
    console.error(`❌ Frontend server exited with code ${code}`);
  }
});

console.log('\n✅ MEDORA system started successfully!');
console.log('📱 Frontend: http://localhost:1200');
console.log('🔧 Backend: http://localhost:5000');
console.log('📚 API Docs: http://localhost:5000/api');
console.log('\nPress Ctrl+C to stop all servers\n');
