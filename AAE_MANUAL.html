<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEDORA - Application Administration and Enhancement Manual</title>
    <link href="https://fonts.googleapis.com/css2?family=Bruno+Ace+SC:wght@400&family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #4ade80;
            --warning-color: #fbbf24;
            --error-color: #ef4444;
            --info-color: #3b82f6;
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --shadow-neumorphic: 20px 20px 60px #d1d9e6, -20px -20px 60px #ffffff;
            --shadow-inset: inset 20px 20px 60px #d1d9e6, inset -20px -20px 60px #ffffff;
            --shadow-3d: 0 10px 30px rgba(0,0,0,0.3), 0 1px 8px rgba(0,0,0,0.2);
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--primary-color));
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Styles */
        .header {
            background: var(--gradient-primary);
            padding: 60px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .logo {
            font-family: 'Bruno Ace SC', cursive;
            font-size: 4rem;
            color: white;
            text-shadow: var(--shadow-3d);
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }

        .subtitle {
            font-size: 1.5rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
            position: relative;
            z-index: 2;
        }

        /* Navigation */
        .nav {
            background: var(--bg-secondary);
            padding: 20px 0;
            box-shadow: var(--shadow-neumorphic);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-list {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 30px;
            list-style: none;
        }

        .nav-item a {
            text-decoration: none;
            color: var(--text-primary);
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            background: var(--bg-primary);
            box-shadow: 5px 5px 15px #d1d9e6, -5px -5px 15px #ffffff;
        }

        .nav-item a:hover {
            box-shadow: var(--shadow-inset);
            color: var(--primary-color);
        }

        /* Section Headers */
        .section {
            margin: 80px 0;
        }

        .section-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title {
            font-family: 'Bruno Ace SC', cursive;
            font-size: 2.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: var(--shadow-3d);
            margin-bottom: 20px;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Neumorphic Cards */
        .card {
            background: var(--bg-secondary);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: var(--shadow-neumorphic);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 25px 25px 70px #d1d9e6, -25px -25px 70px #ffffff;
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            background: var(--gradient-accent);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            box-shadow: var(--shadow-3d);
        }

        .card-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Step Guide Styles */
        .step-guide {
            display: grid;
            gap: 30px;
            margin: 40px 0;
        }

        .step {
            display: flex;
            align-items: flex-start;
            background: var(--bg-secondary);
            border-radius: 15px;
            padding: 25px;
            box-shadow: var(--shadow-neumorphic);
            position: relative;
        }

        .step::before {
            content: '';
            position: absolute;
            left: 35px;
            top: 80px;
            width: 2px;
            height: calc(100% + 30px);
            background: var(--gradient-primary);
            z-index: 1;
        }

        .step:last-child::before {
            display: none;
        }

        .step-number {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--gradient-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
            margin-right: 20px;
            box-shadow: var(--shadow-3d);
            position: relative;
            z-index: 2;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--text-primary);
        }

        .step-description {
            color: var(--text-secondary);
            margin-bottom: 15px;
        }

        /* Code Blocks */
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Fira Code', monospace;
            box-shadow: var(--shadow-inset);
        }

        .code-block pre {
            margin: 0;
        }

        /* Visual Flow Diagrams */
        .flow-diagram {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .flow-item {
            background: var(--bg-secondary);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: var(--shadow-neumorphic);
            min-width: 150px;
            position: relative;
        }

        .flow-item::after {
            content: '→';
            position: absolute;
            right: -30px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5rem;
            color: var(--primary-color);
            font-weight: bold;
        }

        .flow-item:last-child::after {
            display: none;
        }

        .flow-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: var(--gradient-accent);
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        /* Alert Boxes */
        .alert {
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid;
            box-shadow: var(--shadow-neumorphic);
        }

        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            border-color: var(--info-color);
            color: var(--info-color);
        }

        .alert-warning {
            background: rgba(251, 191, 36, 0.1);
            border-color: var(--warning-color);
            color: #d97706;
        }

        .alert-success {
            background: rgba(74, 222, 128, 0.1);
            border-color: var(--success-color);
            color: #059669;
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border-color: var(--error-color);
            color: var(--error-color);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .logo {
                font-size: 2.5rem;
            }
            
            .nav-list {
                flex-direction: column;
                align-items: center;
            }
            
            .flow-diagram {
                flex-direction: column;
            }
            
            .flow-item::after {
                content: '↓';
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }
            
            .step {
                flex-direction: column;
                text-align: center;
            }
            
            .step::before {
                display: none;
            }
        }

        /* Print Styles */
        @media print {
            .nav, .header::before {
                display: none;
            }
            
            .card, .step {
                break-inside: avoid;
                box-shadow: none;
                border: 1px solid #ddd;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1 class="logo">MEDORA AI</h1>
            <p class="subtitle">Application Manual</p>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="container">
            <ul class="nav-list">
                <li class="nav-item"><a href="#overview">Overview</a></li>
                <li class="nav-item"><a href="#installation">Installation</a></li>
                <li class="nav-item"><a href="#cors">CORS Setup</a></li>
                <li class="nav-item"><a href="#backend">Backend</a></li>
                <li class="nav-item"><a href="#frontend">Frontend</a></li>
                <li class="nav-item"><a href="#database">Database</a></li>
                <li class="nav-item"><a href="#deployment">Deployment</a></li>
                <li class="nav-item"><a href="#security">Security</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container">
        <!-- System Overview Section -->
        <section id="overview" class="section">
            <div class="section-header">
                <h2 class="section-title">System Overview</h2>
                <p class="section-subtitle">Understanding MEDORA AI architecture and components</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <h3 class="card-title">Architecture Overview</h3>
                </div>
                <p>MEDORA AI is a comprehensive an Machine Learning ML AI-powered medical assistant platform built with modern web technologies.</p>
                
                <div class="flow-diagram">
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fab fa-react"></i>
                        </div>
                        <div>Frontend<br><small>React + TypeScript</small></div>
                    </div>
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div>Backend<br><small>Node.js + Express</small></div>
                    </div>
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div>Database<br><small>MongoDB</small></div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <strong>💡 Key Features:</strong> AI-powered diagnosis, real-time chat, patient management, payment integration, and HIPAA compliance.
                </div>
            </div>
        </section>

        <!-- Installation Section -->
        <section id="installation" class="section">
            <div class="section-header">
                <h2 class="section-title">Installation Guide</h2>
                <p class="section-subtitle">Step-by-step installation process with visual guides</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <h3 class="card-title">Prerequisites</h3>
                </div>

                <div class="flow-diagram">
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fab fa-node-js"></i>
                        </div>
                        <div>Node.js 18+<br><small>JavaScript Runtime</small></div>
                    </div>
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div>MongoDB 6+<br><small>Database</small></div>
                    </div>
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fab fa-git-alt"></i>
                        </div>
                        <div>Git<br><small>Version Control</small></div>
                    </div>
                </div>

                <div class="alert alert-warning">
                    <strong>⚠️ Important:</strong> Ensure all prerequisites are installed before proceeding with the installation.
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3 class="card-title">Installation Steps</h3>
                </div>

                <div class="step-guide">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4 class="step-title">Clone Repository</h4>
                            <p class="step-description">Download the MEDORA source code from the repository</p>
                            <div class="code-block">
                                <pre>git clone &lt;repository-url&gt;
cd medora-main</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4 class="step-title">Install Frontend Dependencies</h4>
                            <p class="step-description">Install all required packages for the React frontend</p>
                            <div class="code-block">
                                <pre>npm install</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4 class="step-title">Install Backend Dependencies</h4>
                            <p class="step-description">Navigate to backend folder and install server dependencies</p>
                            <div class="code-block">
                                <pre>cd backend
npm install
cd ..</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4 class="step-title">Configure Environment</h4>
                            <p class="step-description">Set up environment variables for your installation</p>
                            <div class="code-block">
                                <pre>cp backend/.env.example backend/.env
# Edit backend/.env with your configurations</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">5</div>
                        <div class="step-content">
                            <h4 class="step-title">Start MongoDB</h4>
                            <p class="step-description">Ensure MongoDB service is running on your system</p>
                            <div class="code-block">
                                <pre># Windows
net start MongoDB

# macOS
brew services start mongodb-community

# Linux
sudo systemctl start mongod</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">6</div>
                        <div class="step-content">
                            <h4 class="step-title">Launch Application</h4>
                            <p class="step-description">Start both frontend and backend servers</p>
                            <div class="code-block">
                                <pre>npm start</pre>
                            </div>
                            <div class="alert alert-success">
                                <strong>✅ Success!</strong> Your application should now be running at:
                                <br>• Frontend: http://localhost:1200
                                <br>• Backend: http://localhost:5000
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CORS Configuration Section -->
        <section id="cors" class="section">
            <div class="section-header">
                <h2 class="section-title">CORS Configuration</h2>
                <p class="section-subtitle">Cross-Origin Resource Sharing setup for secure communication</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="card-title">Understanding CORS</h3>
                </div>
                <p>CORS (Cross-Origin Resource Sharing) allows your frontend to communicate securely with your backend API.</p>

                <div class="flow-diagram">
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fas fa-desktop"></i>
                        </div>
                        <div>Frontend<br><small>localhost:1200</small></div>
                    </div>
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div>CORS<br><small>Security Layer</small></div>
                    </div>
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div>Backend<br><small>localhost:5000</small></div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3 class="card-title">Backend CORS Setup</h3>
                </div>

                <div class="step-guide">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4 class="step-title">Development Configuration</h4>
                            <p class="step-description">Configure CORS for local development environment</p>
                            <div class="code-block">
                                <pre>const corsOptions = {
  origin: [
    'http://localhost:1200',  // Frontend URL
    'http://localhost:3000',  // Alternative port
    'http://localhost:5173'   // Vite default
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
};</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4 class="step-title">Production Configuration</h4>
                            <p class="step-description">Set up CORS for production deployment</p>
                            <div class="code-block">
                                <pre>const corsOptions = {
  origin: [
    'https://yourdomain.com',
    'https://www.yourdomain.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
};</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4 class="step-title">Environment Variables</h4>
                            <p class="step-description">Use environment variables for flexible CORS configuration</p>
                            <div class="code-block">
                                <pre># .env file
FRONTEND_URL=http://localhost:1200
ALLOWED_ORIGINS=http://localhost:1200,http://localhost:3000</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <strong>💡 Pro Tip:</strong> Always test CORS configuration in both development and production environments to ensure proper functionality.
                </div>
            </div>
        </section>

        <!-- Backend Configuration Section -->
        <section id="backend" class="section">
            <div class="section-header">
                <h2 class="section-title">Backend Configuration</h2>
                <p class="section-subtitle">Setting up the Node.js Express server with all required services</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <h3 class="card-title">Server Architecture</h3>
                </div>

                <div class="flow-diagram">
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fas fa-plug"></i>
                        </div>
                        <div>Middleware<br><small>Security & Auth</small></div>
                    </div>
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fas fa-route"></i>
                        </div>
                        <div>API Routes<br><small>Endpoints</small></div>
                    </div>
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div>Database<br><small>MongoDB</small></div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <h3 class="card-title">Environment Variables Setup</h3>
                </div>

                <div class="step-guide">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4 class="step-title">Required Variables</h4>
                            <p class="step-description">Essential environment variables for basic functionality</p>
                            <div class="code-block">
                                <pre>NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb://localhost:27017/medora
JWT_SECRET=your-super-secure-jwt-secret-key
FRONTEND_URL=http://localhost:1200</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4 class="step-title">AI Services Configuration</h4>
                            <p class="step-description">Configure OpenAI and other AI service integrations</p>
                            <div class="code-block">
                                <pre>OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.7</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4 class="step-title">Payment Gateway Setup</h4>
                            <p class="step-description">Configure Paystack and Flutterwave for payment processing</p>
                            <div class="code-block">
                                <pre># Paystack Configuration
PAYSTACK_SECRET_KEY=sk_test_your-paystack-secret-key
PAYSTACK_PUBLIC_KEY=pk_test_your-paystack-public-key

# Flutterwave Configuration
FLW_SECRET_KEY=FLWSECK_TEST-your-flutterwave-secret-key
FLW_PUBLIC_KEY=FLWPUBK_TEST-your-flutterwave-public-key</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4 class="step-title">OAuth Configuration</h4>
                            <p class="step-description">Set up social authentication providers</p>
                            <div class="code-block">
                                <pre>GOOGLE_CLIENT_ID=your-google-client-id.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-warning">
                    <strong>🔒 Security Note:</strong> Never commit .env files to version control. Keep your API keys secure and rotate them regularly.
                </div>
            </div>
        </section>

        <!-- Database Configuration Section -->
        <section id="database" class="section">
            <div class="section-header">
                <h2 class="section-title">Database Setup</h2>
                <p class="section-subtitle">MongoDB installation and configuration guide</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <h3 class="card-title">MongoDB Installation</h3>
                </div>

                <div class="step-guide">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4 class="step-title">Windows Installation</h4>
                            <p class="step-description">Install MongoDB on Windows systems</p>
                            <div class="code-block">
                                <pre>1. Download MongoDB Community Server from mongodb.com
2. Run the installer (.msi file)
3. Choose "Complete" installation
4. Install as Windows Service
5. Install MongoDB Compass (GUI tool)</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4 class="step-title">macOS Installation</h4>
                            <p class="step-description">Install MongoDB using Homebrew</p>
                            <div class="code-block">
                                <pre># Install using Homebrew
brew tap mongodb/brew
brew install mongodb-community

# Start MongoDB service
brew services start mongodb-community</pre>
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4 class="step-title">Linux Installation</h4>
                            <p class="step-description">Install MongoDB on Ubuntu/Debian systems</p>
                            <div class="code-block">
                                <pre># Import MongoDB public GPG key
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# Create list file for MongoDB
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Update package database and install
sudo apt-get update
sudo apt-get install -y mongodb-org

# Start MongoDB service
sudo systemctl start mongod
sudo systemctl enable mongod</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3 class="card-title">Database Schema</h3>
                </div>

                <div class="flow-diagram">
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>Users<br><small>Authentication</small></div>
                    </div>
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fas fa-user-injured"></i>
                        </div>
                        <div>Patients<br><small>Medical Records</small></div>
                    </div>
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fas fa-stethoscope"></i>
                        </div>
                        <div>Diagnoses<br><small>AI Analysis</small></div>
                    </div>
                    <div class="flow-item">
                        <div class="flow-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div>Payments<br><small>Transactions</small></div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <strong>📊 Collections:</strong> The database includes users, patients, diagnoses, payments, subscriptions, settings, and ML analysis collections.
                </div>
            </div>
        </section>
