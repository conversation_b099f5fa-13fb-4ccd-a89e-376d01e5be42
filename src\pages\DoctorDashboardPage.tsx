import React from 'react';
import { motion } from 'framer-motion';
import DoctorDash<PERSON> from '@/components/dashboards/DoctorDashboard';
import { useAuth } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';

const DoctorDashboardPage: React.FC = () => {
  const { user, isAuthenticated, loading } = useAuth();

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-emerald-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/doctor-login" replace />;
  }

  // Redirect if wrong role
  if (user?.role !== 'doctor') {
    const redirectPath = user?.role === 'patient' ? '/patient-dashboard' : 
                        user?.role === 'nurse' ? '/nurse-dashboard' : 
                        user?.role === 'admin' ? '/admin-dashboard' : '/doctor-login';
    return <Navigate to={redirectPath} replace />;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.5 }}
    >
      <DoctorDashboard />
    </motion.div>
  );
};

export default DoctorDashboardPage;
