const express = require('express');
const { body, query, validationResult } = require('express-validator');
const scraperService = require('../services/scraperService');
const { protect, restrictTo } = require('../middleware/auth');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, errors.array()));
  }
  next();
};

// Rate limiting for scraping endpoints
const scrapingRateLimit = {};
const checkScrapingRateLimit = (req, res, next) => {
  const userId = req.user.id;
  const now = Date.now();
  const userLimit = scrapingRateLimit[userId];
  
  // Allow 10 requests per hour per user
  if (userLimit && userLimit.count >= 10 && now - userLimit.resetTime < 3600000) {
    return next(new AppError('Scraping rate limit exceeded. Please try again later.', 429));
  }
  
  if (!userLimit || now - userLimit.resetTime >= 3600000) {
    scrapingRateLimit[userId] = { count: 1, resetTime: now };
  } else {
    scrapingRateLimit[userId].count++;
  }
  
  next();
};

// Apply authentication and rate limiting to all routes
router.use(protect);
router.use(restrictTo('doctor', 'nurse', 'admin', 'medical_staff'));
router.use(checkScrapingRateLimit);

// @route   GET /api/scraping/pubmed
// @desc    Scrape medical research from PubMed
// @access  Private (Medical Staff)
router.get('/pubmed', [
  query('query')
    .notEmpty()
    .withMessage('Search query is required')
    .isLength({ min: 2, max: 200 })
    .withMessage('Query must be between 2 and 200 characters'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('Limit must be between 1 and 20'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { query: searchQuery, limit = 10 } = req.query;
    
    logger.info(`User ${req.user.id} scraping PubMed for: ${searchQuery}`);
    
    const articles = await scraperService.scrapePubMed(searchQuery, parseInt(limit));
    
    res.status(200).json({
      status: 'success',
      results: articles.length,
      data: {
        query: searchQuery,
        articles,
        scrapedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   GET /api/scraping/drug-info
// @desc    Scrape drug information
// @access  Private (Medical Staff)
router.get('/drug-info', [
  query('drug')
    .notEmpty()
    .withMessage('Drug name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Drug name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z0-9\s\-\.]+$/)
    .withMessage('Drug name contains invalid characters'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { drug } = req.query;
    
    logger.info(`User ${req.user.id} scraping drug info for: ${drug}`);
    
    const drugInfo = await scraperService.scrapeDrugInfo(drug);
    
    res.status(200).json({
      status: 'success',
      data: {
        drug: drugInfo,
        scrapedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   GET /api/scraping/medical-news
// @desc    Scrape medical news
// @access  Private (Medical Staff)
router.get('/medical-news', [
  query('topic')
    .notEmpty()
    .withMessage('Topic is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Topic must be between 2 and 100 characters'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Limit must be between 1 and 10'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { topic, limit = 5 } = req.query;
    
    logger.info(`User ${req.user.id} scraping medical news for: ${topic}`);
    
    const news = await scraperService.scrapeMedicalNews(topic, parseInt(limit));
    
    res.status(200).json({
      status: 'success',
      results: news.length,
      data: {
        topic,
        news,
        scrapedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   GET /api/scraping/disease-info
// @desc    Scrape disease information
// @access  Private (Medical Staff)
router.get('/disease-info', [
  query('disease')
    .notEmpty()
    .withMessage('Disease name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Disease name must be between 2 and 100 characters'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { disease } = req.query;
    
    logger.info(`User ${req.user.id} scraping disease info for: ${disease}`);
    
    const diseaseInfo = await scraperService.scrapeDiseasInfo(disease);
    
    res.status(200).json({
      status: 'success',
      data: {
        disease: diseaseInfo,
        scrapedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   GET /api/scraping/clinical-trials
// @desc    Scrape clinical trials information
// @access  Private (Medical Staff)
router.get('/clinical-trials', [
  query('condition')
    .notEmpty()
    .withMessage('Medical condition is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Condition must be between 2 and 100 characters'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Limit must be between 1 and 10'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { condition, limit = 5 } = req.query;
    
    logger.info(`User ${req.user.id} scraping clinical trials for: ${condition}`);
    
    const trials = await scraperService.scrapeClinicalTrials(condition, parseInt(limit));
    
    res.status(200).json({
      status: 'success',
      results: trials.length,
      data: {
        condition,
        trials,
        scrapedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   GET /api/scraping/guidelines
// @desc    Scrape medical guidelines
// @access  Private (Medical Staff)
router.get('/guidelines', [
  query('topic')
    .notEmpty()
    .withMessage('Topic is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Topic must be between 2 and 100 characters'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { topic } = req.query;
    
    logger.info(`User ${req.user.id} scraping medical guidelines for: ${topic}`);
    
    const guidelines = await scraperService.scrapeMedicalGuidelines(topic);
    
    res.status(200).json({
      status: 'success',
      results: guidelines.length,
      data: {
        topic,
        guidelines,
        scrapedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   GET /api/scraping/comprehensive
// @desc    Scrape comprehensive medical data from multiple sources
// @access  Private (Medical Staff)
router.get('/comprehensive', [
  query('query')
    .notEmpty()
    .withMessage('Search query is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Query must be between 2 and 100 characters'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { query: searchQuery } = req.query;
    
    logger.info(`User ${req.user.id} performing comprehensive scraping for: ${searchQuery}`);
    
    const comprehensiveData = await scraperService.scrapeComprehensiveMedicalData(searchQuery);
    
    res.status(200).json({
      status: 'success',
      data: comprehensiveData,
    });
  } catch (error) {
    next(error);
  }
});

// @route   POST /api/scraping/batch
// @desc    Batch scraping for multiple queries
// @access  Private (Doctors and Admin only)
router.post('/batch', [
  restrictTo('doctor', 'admin'),
  body('queries')
    .isArray({ min: 1, max: 5 })
    .withMessage('Queries must be an array with 1-5 items'),
  body('queries.*')
    .isLength({ min: 2, max: 100 })
    .withMessage('Each query must be between 2 and 100 characters'),
  body('sources')
    .optional()
    .isArray()
    .withMessage('Sources must be an array'),
  body('sources.*')
    .optional()
    .isIn(['pubmed', 'news', 'disease', 'trials', 'guidelines'])
    .withMessage('Invalid source specified'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { queries, sources = ['pubmed', 'news', 'disease'] } = req.body;
    
    logger.info(`User ${req.user.id} performing batch scraping for ${queries.length} queries`);
    
    const batchResults = [];
    
    for (const query of queries) {
      try {
        const results = {};
        
        if (sources.includes('pubmed')) {
          results.pubmed = await scraperService.scrapePubMed(query, 3);
        }
        
        if (sources.includes('news')) {
          results.news = await scraperService.scrapeMedicalNews(query, 2);
        }
        
        if (sources.includes('disease')) {
          try {
            results.disease = await scraperService.scrapeDiseasInfo(query);
          } catch (error) {
            results.disease = null;
          }
        }
        
        if (sources.includes('trials')) {
          results.trials = await scraperService.scrapeClinicalTrials(query, 2);
        }
        
        if (sources.includes('guidelines')) {
          results.guidelines = await scraperService.scrapeMedicalGuidelines(query);
        }
        
        batchResults.push({
          query,
          results,
          scrapedAt: new Date().toISOString(),
        });
        
        // Add delay between queries to be respectful
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        logger.error(`Batch scraping error for query "${query}":`, error);
        batchResults.push({
          query,
          error: error.message,
          scrapedAt: new Date().toISOString(),
        });
      }
    }
    
    res.status(200).json({
      status: 'success',
      results: batchResults.length,
      data: {
        batchResults,
        completedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   GET /api/scraping/status
// @desc    Get scraping service status and rate limit info
// @access  Private (Medical Staff)
router.get('/status', async (req, res, next) => {
  try {
    const userId = req.user.id;
    const userLimit = scrapingRateLimit[userId];
    const now = Date.now();
    
    let remainingRequests = 10;
    let resetTime = null;
    
    if (userLimit) {
      const timeSinceReset = now - userLimit.resetTime;
      if (timeSinceReset < 3600000) {
        remainingRequests = Math.max(0, 10 - userLimit.count);
        resetTime = new Date(userLimit.resetTime + 3600000).toISOString();
      }
    }
    
    res.status(200).json({
      status: 'success',
      data: {
        serviceStatus: 'operational',
        rateLimit: {
          remainingRequests,
          resetTime,
          maxRequestsPerHour: 10,
        },
        availableEndpoints: [
          'pubmed',
          'drug-info',
          'medical-news',
          'disease-info',
          'clinical-trials',
          'guidelines',
          'comprehensive',
          'batch',
        ],
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   POST /api/scraping/comprehensive-enhanced
// @desc    Enhanced comprehensive medical data scraping with database storage
// @access  Private
router.post('/comprehensive-enhanced', protect, async (req, res, next) => {
  try {
    const { query, useCache = true } = req.body;

    if (!query) {
      return res.status(400).json({
        status: 'error',
        message: 'Search query is required'
      });
    }

    logger.info(`Enhanced comprehensive scraping request from user ${req.user.id} for query: ${query}`);

    // Use enhanced scraping with storage
    const result = await scraperService.scrapeComprehensiveMedicalDataWithStorage(query, req.user.id);

    res.status(200).json({
      status: 'success',
      data: {
        id: result._id,
        query: result.query,
        dataType: result.dataType,
        source: result.source,
        data: result.comprehensiveData,
        qualityScore: result.qualityScore,
        relevanceScore: result.relevanceScore,
        scrapedAt: result.scrapedAt,
        processingTime: result.processingTime,
        totalItems: result.totalItems,
        freshness: result.freshness,
      },
      message: 'Enhanced comprehensive medical data scraped and stored successfully'
    });

  } catch (error) {
    next(error);
  }
});

// @route   GET /api/scraping/cached/:query
// @desc    Get cached scraped data for a query
// @access  Private
router.get('/cached/:query', protect, async (req, res, next) => {
  try {
    const { query } = req.params;
    const { dataType = 'comprehensive', maxAge = 7 } = req.query;

    const ScrapedData = require('../models/ScrapedData');
    const cachedData = await ScrapedData.findFreshData(query, dataType, parseInt(maxAge));

    if (!cachedData) {
      return res.status(404).json({
        status: 'error',
        message: 'No cached data found for this query'
      });
    }

    await cachedData.incrementAccess();

    res.status(200).json({
      status: 'success',
      data: {
        id: cachedData._id,
        query: cachedData.query,
        dataType: cachedData.dataType,
        data: cachedData.comprehensiveData || cachedData.pubmedArticles || cachedData.medicalNews,
        qualityScore: cachedData.qualityScore,
        relevanceScore: cachedData.relevanceScore,
        scrapedAt: cachedData.scrapedAt,
        accessCount: cachedData.accessCount,
        freshness: cachedData.freshness,
      },
      message: 'Cached data retrieved successfully'
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;