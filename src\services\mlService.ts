import { apiClient, ApiResponse } from './api';

export interface MLAnalysisRequest {
  patientId?: string;
  analysisType: 'symptom_analysis' | 'risk_assessment' | 'treatment_recommendation' | 'drug_interaction' | 'prognosis';
  inputData: {
    symptoms?: string[];
    medicalHistory?: string[];
    currentMedications?: string[];
    labResults?: {
      parameter: string;
      value: number;
      unit: string;
    }[];
    vitalSigns?: {
      temperature?: number;
      bloodPressure?: {
        systolic: number;
        diastolic: number;
      };
      heartRate?: number;
      respiratoryRate?: number;
      oxygenSaturation?: number;
    };
    demographics?: {
      age: number;
      gender: 'male' | 'female' | 'other';
      weight?: number;
      height?: number;
    };
    lifestyle?: {
      smoking: boolean;
      alcohol: 'none' | 'occasional' | 'moderate' | 'heavy';
      exercise: 'none' | 'light' | 'moderate' | 'intense';
      diet: 'poor' | 'average' | 'good' | 'excellent';
    };
  };
  options?: {
    includeExplanation: boolean;
    confidenceThreshold: number;
    maxResults: number;
  };
}

export interface MLAnalysisResult {
  _id: string;
  patientId?: string;
  analysisType: string;
  results: {
    predictions: {
      condition: string;
      probability: number;
      confidence: number;
      severity: 'low' | 'medium' | 'high';
      explanation?: string;
    }[];
    riskFactors: {
      factor: string;
      impact: number;
      description: string;
    }[];
    recommendations: {
      category: 'immediate' | 'short_term' | 'long_term';
      action: string;
      priority: 'low' | 'medium' | 'high' | 'critical';
      reasoning: string;
    }[];
    drugInteractions?: {
      drug1: string;
      drug2: string;
      severity: 'minor' | 'moderate' | 'major';
      description: string;
      recommendation: string;
    }[];
    prognosis?: {
      outlook: 'excellent' | 'good' | 'fair' | 'poor';
      timeframe: string;
      factors: string[];
      recommendations: string[];
    };
  };
  modelInfo: {
    name: string;
    version: string;
    accuracy: number;
    lastTrained: string;
  };
  processingTime: number;
  confidence: number;
  status: 'completed' | 'processing' | 'failed';
  createdAt: string;
  updatedAt: string;
}

export interface MLModel {
  _id: string;
  name: string;
  type: 'classification' | 'regression' | 'clustering' | 'recommendation';
  version: string;
  accuracy: number;
  description: string;
  inputFeatures: string[];
  outputClasses: string[];
  trainingData: {
    samples: number;
    lastUpdated: string;
  };
  performance: {
    precision: number;
    recall: number;
    f1Score: number;
    auc?: number;
  };
  status: 'active' | 'training' | 'deprecated';
  createdAt: string;
  updatedAt: string;
}

export interface MLAnalysisHistory {
  analyses: MLAnalysisResult[];
  total: number;
  page: number;
  limit: number;
}

class MLService {
  async createAnalysis(analysisData: MLAnalysisRequest): Promise<ApiResponse<MLAnalysisResult>> {
    return apiClient.post<MLAnalysisResult>('/ml/analyze', analysisData);
  }

  async getAnalysis(analysisId: string): Promise<ApiResponse<MLAnalysisResult>> {
    return apiClient.get<MLAnalysisResult>(`/ml/analysis/${analysisId}`);
  }

  async getPatientAnalyses(
    patientId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<ApiResponse<MLAnalysisHistory>> {
    return apiClient.get<MLAnalysisHistory>(
      `/ml/analysis/patient/${patientId}?page=${page}&limit=${limit}`
    );
  }

  async getAllAnalyses(
    page: number = 1,
    limit: number = 10,
    analysisType?: string
  ): Promise<ApiResponse<MLAnalysisHistory>> {
    const typeQuery = analysisType ? `&type=${analysisType}` : '';
    return apiClient.get<MLAnalysisHistory>(
      `/ml/analysis?page=${page}&limit=${limit}${typeQuery}`
    );
  }

  async deleteAnalysis(analysisId: string): Promise<ApiResponse<{ message: string }>> {
    return apiClient.delete<{ message: string }>(`/ml/analysis/${analysisId}`);
  }

  async getAvailableModels(): Promise<ApiResponse<MLModel[]>> {
    return apiClient.get<MLModel[]>('/ml/models');
  }

  async getModelDetails(modelId: string): Promise<ApiResponse<MLModel>> {
    return apiClient.get<MLModel>(`/ml/models/${modelId}`);
  }

  async trainModel(
    modelType: string,
    trainingData: unknown[],
    options?: {
      validationSplit: number;
      epochs: number;
      batchSize: number;
    }
  ): Promise<ApiResponse<{ jobId: string; message: string }>> {
    return apiClient.post<{ jobId: string; message: string }>('/ml/train', {
      modelType,
      trainingData,
      options
    });
  }

  async getTrainingStatus(jobId: string): Promise<ApiResponse<{
    status: 'pending' | 'running' | 'completed' | 'failed';
    progress: number;
    message: string;
    results?: {
      accuracy: number;
      loss: number;
      validationAccuracy: number;
      validationLoss: number;
    };
  }>> {
    return apiClient.get(`/ml/training/${jobId}`);
  }

  async predictSymptoms(symptoms: string[]): Promise<ApiResponse<{
    possibleConditions: {
      condition: string;
      probability: number;
      confidence: number;
    }[];
    recommendations: string[];
  }>> {
    return apiClient.post('/ml/predict/symptoms', { symptoms });
  }

  async assessRisk(
    patientData: {
      age: number;
      gender: string;
      medicalHistory: string[];
      lifestyle: {
        smoking: boolean;
        alcohol: string;
        exercise: string;
      };
      familyHistory: string[];
    }
  ): Promise<ApiResponse<{
    riskScore: number;
    riskLevel: 'low' | 'medium' | 'high';
    factors: {
      factor: string;
      impact: number;
      modifiable: boolean;
    }[];
    recommendations: string[];
  }>> {
    return apiClient.post('/ml/assess-risk', patientData);
  }

  async checkDrugInteractions(medications: string[]): Promise<ApiResponse<{
    interactions: {
      drug1: string;
      drug2: string;
      severity: 'minor' | 'moderate' | 'major';
      description: string;
      recommendation: string;
    }[];
    safetyScore: number;
    warnings: string[];
  }>> {
    return apiClient.post('/ml/drug-interactions', { medications });
  }

  async getMLStatistics(): Promise<ApiResponse<{
    totalAnalyses: number;
    analysesToday: number;
    averageAccuracy: number;
    modelPerformance: {
      modelName: string;
      accuracy: number;
      usage: number;
    }[];
    popularAnalysisTypes: {
      type: string;
      count: number;
    }[];
  }>> {
    return apiClient.get('/ml/statistics');
  }
}

export const mlService = new MLService();