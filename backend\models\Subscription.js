const mongoose = require('mongoose');

const SubscriptionSchema = new mongoose.Schema(
  {
    userEmail: { type: String, index: true },
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    planName: String,
    cycle: String,
    status: { type: String, default: 'active' },
    provider: String,
    reference: String,
  },
  { timestamps: true }
);

module.exports = mongoose.model('Subscription', SubscriptionSchema);

