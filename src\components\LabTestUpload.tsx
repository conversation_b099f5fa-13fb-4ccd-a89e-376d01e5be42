import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Upload, FileText, AlertCircle, CheckCircle, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiClient } from '@/services/api';

interface LabTest {
  testName: string;
  testCode: string;
  category: string;
  results: Array<{
    parameter: string;
    value: string;
    unit: string;
    referenceRange: string;
    status: 'normal' | 'abnormal' | 'critical' | 'pending';
  }>;
  orderedBy: string;
  performedAt: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  notes: string;
}

interface LabTestUploadProps {
  patientId?: string;
  onTestUploaded?: (test: LabTest) => void;
}

const LabTestUpload: React.FC<LabTestUploadProps> = ({ patientId, onTestUploaded }) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [manualEntry, setManualEntry] = useState(false);
  const [testData, setTestData] = useState<Partial<LabTest>>({
    testName: '',
    category: '',
    results: [],
    notes: '',
    status: 'completed'
  });
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const testCategories = [
    'blood_chemistry',
    'hematology',
    'immunology',
    'microbiology',
    'pathology',
    'radiology',
    'cardiology',
    'other'
  ];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setUploadedFiles(prev => [...prev, ...files]);
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const addTestResult = () => {
    setTestData(prev => ({
      ...prev,
      results: [
        ...(prev.results || []),
        {
          parameter: '',
          value: '',
          unit: '',
          referenceRange: '',
          status: 'normal'
        }
      ]
    }));
  };

  const updateTestResult = (index: number, field: string, value: string) => {
    setTestData(prev => ({
      ...prev,
      results: prev.results?.map((result, i) => 
        i === index ? { ...result, [field]: value } : result
      ) || []
    }));
  };

  const removeTestResult = (index: number) => {
    setTestData(prev => ({
      ...prev,
      results: prev.results?.filter((_, i) => i !== index) || []
    }));
  };

  const processLabFiles = async () => {
    if (uploadedFiles.length === 0) {
      toast({
        title: "No files selected",
        description: "Please select lab test files to upload.",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);
    try {
      const formData = new FormData();
      uploadedFiles.forEach(file => {
        formData.append('labFiles', file);
      });
      
      if (patientId) {
        formData.append('patientId', patientId);
      }

      const response = await apiClient.post('/api/patients/lab-tests/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      toast({
        title: "Lab tests uploaded successfully",
        description: `${uploadedFiles.length} file(s) processed and analyzed.`
      });

      if (onTestUploaded && (response as unknown).data.data.labTests) {
        (response as unknown).data.data.labTests.forEach((test: LabTest) => {
          onTestUploaded(test);
        });
      }

      // Reset form
      setUploadedFiles([]);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: unknown) {
      toast({
        title: "Upload failed",
        description: error.response?.data?.message || "Failed to upload lab tests.",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const submitManualEntry = async () => {
    if (!testData.testName || !testData.category) {
      toast({
        title: "Missing required fields",
        description: "Please fill in test name and category.",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);
    try {
      const payload = {
        ...testData,
        patientId,
        performedAt: new Date().toISOString()
      };

      const response = await apiClient.post('/api/patients/lab-tests/manual', payload);

      toast({
        title: "Lab test added successfully",
        description: "Manual lab test entry has been saved."
      });

      if (onTestUploaded) {
        onTestUploaded((response as unknown).data.data.labTest);
      }

      // Reset form
      setTestData({
        testName: '',
        category: '',
        results: [],
        notes: '',
        status: 'completed'
      });
      setManualEntry(false);
    } catch (error: unknown) {
      toast({
        title: "Failed to save lab test",
        description: error.response?.data?.message || "Failed to save manual lab test entry.",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Lab Test Upload & Analysis
          </CardTitle>
          <CardDescription>
            Upload lab test files or manually enter test results for AI-powered analysis
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Upload Method Toggle */}
          <div className="flex gap-2">
            <Button
              variant={!manualEntry ? "default" : "outline"}
              onClick={() => setManualEntry(false)}
              className="flex-1"
            >
              <Upload className="h-4 w-4 mr-2" />
              File Upload
            </Button>
            <Button
              variant={manualEntry ? "default" : "outline"}
              onClick={() => setManualEntry(true)}
              className="flex-1"
            >
              <FileText className="h-4 w-4 mr-2" />
              Manual Entry
            </Button>
          </div>

          {!manualEntry ? (
            /* File Upload Section */
            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">
                    Drop lab test files here or click to browse
                  </p>
                  <p className="text-xs text-gray-500">
                    Supports PDF, JPG, PNG, DICOM files
                  </p>
                </div>
                <Input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept=".pdf,.jpg,.jpeg,.png,.dcm"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-4"
                >
                  Select Files
                </Button>
              </div>

              {/* Uploaded Files List */}
              {uploadedFiles.length > 0 && (
                <div className="space-y-2">
                  <Label>Selected Files ({uploadedFiles.length})</Label>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {uploadedFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          <span className="text-sm">{file.name}</span>
                          <span className="text-xs text-gray-500">
                            ({(file.size / 1024 / 1024).toFixed(2)} MB)
                          </span>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <Button
                onClick={processLabFiles}
                disabled={isUploading || uploadedFiles.length === 0}
                className="w-full"
              >
                {isUploading ? "Processing..." : "Upload & Analyze Lab Tests"}
              </Button>
            </div>
          ) : (
            /* Manual Entry Section */
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="testName">Test Name *</Label>
                  <Input
                    id="testName"
                    value={testData.testName || ''}
                    onChange={(e) => setTestData(prev => ({ ...prev, testName: e.target.value }))}
                    placeholder="e.g., Complete Blood Count"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select
                    value={testData.category || ''}
                    onValueChange={(value) => setTestData(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select test category" />
                    </SelectTrigger>
                    <SelectContent>
                      {testCategories.map(category => (
                        <SelectItem key={category} value={category}>
                          {category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Test Results */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Test Results</Label>
                  <Button type="button" variant="outline" size="sm" onClick={addTestResult}>
                    Add Result
                  </Button>
                </div>
                
                {testData.results?.map((result, index) => (
                  <Card key={index} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                      <div className="space-y-1">
                        <Label className="text-xs">Parameter</Label>
                        <Input
                          value={result.parameter}
                          onChange={(e) => updateTestResult(index, 'parameter', e.target.value)}
                          placeholder="e.g., Hemoglobin"
                          className="text-sm"
                        />
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Value</Label>
                        <Input
                          value={result.value}
                          onChange={(e) => updateTestResult(index, 'value', e.target.value)}
                          placeholder="e.g., 12.5"
                          className="text-sm"
                        />
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Unit</Label>
                        <Input
                          value={result.unit}
                          onChange={(e) => updateTestResult(index, 'unit', e.target.value)}
                          placeholder="e.g., g/dL"
                          className="text-sm"
                        />
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Reference Range</Label>
                        <div className="flex gap-1">
                          <Input
                            value={result.referenceRange}
                            onChange={(e) => updateTestResult(index, 'referenceRange', e.target.value)}
                            placeholder="e.g., 12-15"
                            className="text-sm"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeTestResult(index)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                    <div className="mt-3">
                      <Label className="text-xs">Status</Label>
                      <Select
                        value={result.status}
                        onValueChange={(value) => updateTestResult(index, 'status', value as unknown)}
                      >
                        <SelectTrigger className="text-sm">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="normal">Normal</SelectItem>
                          <SelectItem value="abnormal">Abnormal</SelectItem>
                          <SelectItem value="critical">Critical</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </Card>
                ))}
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={testData.notes || ''}
                  onChange={(e) => setTestData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Additional notes about the test..."
                  rows={3}
                />
              </div>

              <Button
                onClick={submitManualEntry}
                disabled={isUploading || !testData.testName || !testData.category}
                className="w-full"
              >
                {isUploading ? "Saving..." : "Save Lab Test"}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default LabTestUpload;
