import { apiClient } from './api';
import config from '../config';

export interface SocialProvider {
  name: string;
  displayName: string;
  icon: string;
  color: string;
  enabled: boolean;
}

export interface SocialAuthResponse {
  success: boolean;
  providers?: string[];
  baseUrl?: string;
  authUrl?: string;
  message?: string;
  error?: string;
}

export interface SocialAuthConfig {
  providers: string[];
  baseUrl: string;
}

class SocialAuthService {
  private config: SocialAuthConfig | null = null;
  private readonly API_BASE_URL = config.api.baseUrl;

  /**
   * Get available OAuth providers from the backend
   */
  async getAvailableProviders(): Promise<SocialAuthResponse> {
    try {
      const response = await fetch(`${this.API_BASE_URL}/auth/providers`);
      const data = await response.json();
      
      if (data.success) {
        this.config = {
          providers: data.providers || [],
          baseUrl: data.baseUrl || `${this.API_BASE_URL}/auth`
        };
        
        return {
          success: true,
          providers: data.providers,
          baseUrl: data.baseUrl
        };
      }
      
      return {
        success: false,
        error: data.message || 'Failed to fetch providers'
      };
    } catch (error) {
      console.error('Failed to fetch OAuth providers:', error);
      return {
        success: false,
        error: 'Network error while fetching providers'
      };
    }
  }

  /**
   * Initiate OAuth flow for a specific provider
   */
  async initiateOAuth(provider: string, mode: 'login' | 'register' = 'login'): Promise<void> {
    try {
      if (!this.config) {
        await this.getAvailableProviders();
      }

      if (!this.config?.providers.includes(provider)) {
        throw new Error(`Provider ${provider} is not available`);
      }

      // Store the current mode in sessionStorage for callback handling
      sessionStorage.setItem('oauth_mode', mode);
      sessionStorage.setItem('oauth_provider', provider);
      sessionStorage.setItem('oauth_timestamp', Date.now().toString());

      // Redirect to OAuth provider
      const authUrl = `${this.config.baseUrl}/${provider}`;
      window.location.href = authUrl;
      
    } catch (error) {
      console.error(`Failed to initiate ${provider} OAuth:`, error);
      throw error;
    }
  }

  /**
   * Handle OAuth callback data
   */
  handleOAuthCallback(token: string, userData: any): {
    success: boolean;
    token?: string;
    user?: any;
    error?: string;
  } {
    try {
      // Validate token
      if (!token || typeof token !== 'string') {
        return {
          success: false,
          error: 'Invalid authentication token'
        };
      }

      // Validate user data
      if (!userData || !userData.id || !userData.email) {
        return {
          success: false,
          error: 'Invalid user data received'
        };
      }

      // Clear OAuth session data
      sessionStorage.removeItem('oauth_mode');
      sessionStorage.removeItem('oauth_provider');
      sessionStorage.removeItem('oauth_timestamp');

      return {
        success: true,
        token,
        user: userData
      };
    } catch (error) {
      console.error('OAuth callback handling error:', error);
      return {
        success: false,
        error: 'Failed to process authentication data'
      };
    }
  }

  /**
   * Link a social account to an existing user
   */
  async linkSocialAccount(provider: string): Promise<SocialAuthResponse> {
    try {
      const response = await apiClient.post(`/auth/link/${provider}`, {});
      
      if (response.success && response.data?.authUrl) {
        // Store linking mode
        sessionStorage.setItem('oauth_mode', 'link');
        sessionStorage.setItem('oauth_provider', provider);
        
        return {
          success: true,
          authUrl: response.data.authUrl,
          message: response.data.message
        };
      }
      
      return {
        success: false,
        error: response.error || 'Failed to initiate account linking'
      };
    } catch (error) {
      console.error(`Failed to link ${provider} account:`, error);
      return {
        success: false,
        error: 'Network error while linking account'
      };
    }
  }

  /**
   * Unlink a social account from the current user
   */
  async unlinkSocialAccount(provider: string): Promise<SocialAuthResponse> {
    try {
      const response = await apiClient.delete(`/auth/unlink/${provider}`);
      
      return {
        success: response.success,
        message: response.data?.message || response.message,
        error: response.error
      };
    } catch (error) {
      console.error(`Failed to unlink ${provider} account:`, error);
      return {
        success: false,
        error: 'Network error while unlinking account'
      };
    }
  }

  /**
   * Get OAuth session data
   */
  getOAuthSession(): {
    mode?: string;
    provider?: string;
    timestamp?: number;
  } {
    return {
      mode: sessionStorage.getItem('oauth_mode') || undefined,
      provider: sessionStorage.getItem('oauth_provider') || undefined,
      timestamp: sessionStorage.getItem('oauth_timestamp') 
        ? parseInt(sessionStorage.getItem('oauth_timestamp')!) 
        : undefined
    };
  }

  /**
   * Clear OAuth session data
   */
  clearOAuthSession(): void {
    sessionStorage.removeItem('oauth_mode');
    sessionStorage.removeItem('oauth_provider');
    sessionStorage.removeItem('oauth_timestamp');
  }

  /**
   * Check if OAuth session is valid (not expired)
   */
  isOAuthSessionValid(): boolean {
    const session = this.getOAuthSession();
    if (!session.timestamp) return false;
    
    // Session expires after 10 minutes
    const expirationTime = 10 * 60 * 1000;
    return (Date.now() - session.timestamp) < expirationTime;
  }

  /**
   * Get provider configuration
   */
  getProviderConfig(): SocialAuthConfig | null {
    return this.config;
  }

  /**
   * Check if a specific provider is available
   */
  isProviderAvailable(provider: string): boolean {
    return this.config?.providers.includes(provider) || false;
  }
}

// Export singleton instance
export const socialAuthService = new SocialAuthService();
export default socialAuthService;
