import React from 'react';
import { motion } from 'framer-motion';
import { Button, ButtonProps } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

interface EnhancedButtonProps extends ButtonProps {
  loading?: boolean;
  glow?: boolean;
  pulse?: boolean;
  gradient?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  ripple?: boolean;
}

const EnhancedButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ 
    className, 
    variant = "default", 
    size = "default", 
    loading = false,
    glow = false,
    pulse = false,
    gradient = false,
    icon,
    iconPosition = 'left',
    ripple = true,
    children,
    disabled,
    ...props 
  }, ref) => {
    const [ripples, setRipples] = React.useState<Array<{ id: number; x: number; y: number }>>([]);

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (ripple && !disabled && !loading) {
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        const newRipple = { id: Date.now(), x, y };
        
        setRipples(prev => [...prev, newRipple]);
        
        setTimeout(() => {
          setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
        }, 600);
      }
      
      if (props.onClick && !disabled && !loading) {
        props.onClick(e);
      }
    };

    const buttonVariants = {
      initial: { scale: 1 },
      hover: { 
        scale: 1.02,
        transition: { duration: 0.2, ease: "easeOut" }
      },
      tap: { 
        scale: 0.98,
        transition: { duration: 0.1, ease: "easeOut" }
      }
    };

    const glowVariants = {
      initial: { boxShadow: "0 0 0 rgba(59, 130, 246, 0)" },
      hover: { 
        boxShadow: "0 0 20px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2)",
        transition: { duration: 0.3 }
      }
    };

    const pulseVariants = {
      pulse: {
        scale: [1, 1.05, 1],
        transition: {
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }
      }
    };

    return (
      <motion.div
        className="relative inline-block"
        variants={buttonVariants}
        initial="initial"
        whileHover="hover"
        whileTap="tap"
        animate={pulse ? "pulse" : "initial"}
        {...(pulse && { variants: pulseVariants })}
      >
        <motion.div
          variants={glow ? glowVariants : {}}
          className="rounded-md"
        >
          <Button
            ref={ref}
            variant={variant}
            size={size}
            className={cn(
              "relative overflow-hidden transition-all duration-300 font-medium",
              gradient && "bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 text-primary-foreground border-0",
              glow && "shadow-lg hover:shadow-xl",
              loading && "cursor-not-allowed",
              className
            )}
            disabled={disabled || loading}
            onClick={handleClick}
            {...props}
          >
            {/* Ripple Effect */}
            {ripples.map((ripple) => (
              <motion.span
                key={ripple.id}
                className="absolute bg-white/30 rounded-full pointer-events-none"
                style={{
                  left: ripple.x - 10,
                  top: ripple.y - 10,
                  width: 20,
                  height: 20,
                }}
                initial={{ scale: 0, opacity: 1 }}
                animate={{ scale: 4, opacity: 0 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
              />
            ))}

            {/* Content */}
            <span className="relative flex items-center justify-center space-x-2">
              {loading && (
                <Loader2 className="h-4 w-4 animate-spin" />
              )}
              {!loading && icon && iconPosition === 'left' && (
                <motion.span
                  initial={{ x: 0 }}
                  whileHover={{ x: -2 }}
                  transition={{ duration: 0.2 }}
                >
                  {icon}
                </motion.span>
              )}
              {children && (
                <span className={loading ? "opacity-70" : ""}>
                  {children}
                </span>
              )}
              {!loading && icon && iconPosition === 'right' && (
                <motion.span
                  initial={{ x: 0 }}
                  whileHover={{ x: 2 }}
                  transition={{ duration: 0.2 }}
                >
                  {icon}
                </motion.span>
              )}
            </span>

            {/* Shine Effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 opacity-0"
              whileHover={{
                opacity: 1,
                x: ["0%", "100%"],
                transition: { duration: 0.6, ease: "easeOut" }
              }}
            />
          </Button>
        </motion.div>
      </motion.div>
    );
  }
);

EnhancedButton.displayName = "EnhancedButton";

export { EnhancedButton };
