const axios = require('axios');
const logger = require('../utils/logger');
const { OpenAI } = require('openai');
const { ChromaClient } = require('chromadb');
const cheerio = require('cheerio');

/**
 * Enhanced AI Medical Assistant with 25 years of experience simulation
 * Implements multimodal RAG and agentic reasoning capabilities
 */
class EnhancedAIService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    
    this.chromaClient = new ChromaClient({
      path: process.env.CHROMA_DB_PATH || 'http://localhost:8000'
    });
    
    this.medicalKnowledgeGraph = this.initializeMedicalKnowledgeGraph();
    this.experienceBase = this.initializeExperienceBase();
    this.isInitialized = false;
    
    this.initialize();
  }

  async initialize() {
    try {
      // Initialize vector database collections
      await this.initializeVectorCollections();
      
      // Load medical knowledge bases
      await this.loadMedicalKnowledgeBases();
      
      this.isInitialized = true;
      logger.info('Enhanced AI Medical Assistant initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Enhanced AI Service:', error);
    }
  }

  /**
   * Initialize medical knowledge graph with ICD-10, SNOMED CT integration
   */
  initializeMedicalKnowledgeGraph() {
    return {
      // ICD-10 Disease Classifications
      icd10: {
        'A00-B99': 'Infectious and parasitic diseases',
        'C00-D49': 'Neoplasms',
        'D50-D89': 'Diseases of blood and immune system',
        'E00-E89': 'Endocrine, nutritional and metabolic diseases',
        'F01-F99': 'Mental and behavioral disorders',
        'G00-G99': 'Diseases of the nervous system',
        'H00-H59': 'Diseases of the eye and adnexa',
        'H60-H95': 'Diseases of the ear and mastoid process',
        'I00-I99': 'Diseases of the circulatory system',
        'J00-J99': 'Diseases of the respiratory system',
        'K00-K95': 'Diseases of the digestive system',
        'L00-L99': 'Diseases of the skin and subcutaneous tissue',
        'M00-M99': 'Diseases of the musculoskeletal system',
        'N00-N99': 'Diseases of the genitourinary system',
        'O00-O9A': 'Pregnancy, childbirth and the puerperium',
        'P00-P96': 'Perinatal conditions',
        'Q00-Q99': 'Congenital malformations',
        'R00-R99': 'Symptoms, signs and abnormal findings',
        'S00-T88': 'Injury, poisoning and external causes',
        'V00-Y99': 'External causes of morbidity',
        'Z00-Z99': 'Factors influencing health status'
      },
      
      // SNOMED CT Concepts (simplified)
      snomedCT: {
        concepts: new Map(),
        relationships: new Map(),
        descriptions: new Map()
      },
      
      // Drug interactions and contraindications
      pharmacology: {
        drugClasses: new Map(),
        interactions: new Map(),
        contraindications: new Map(),
        allergies: new Map()
      }
    };
  }

  /**
   * Initialize 25-year experience base with clinical reasoning patterns
   */
  initializeExperienceBase() {
    return {
      clinicalPatterns: {
        // Common presentation patterns learned over 25 years
        chestPain: {
          redFlags: ['crushing pain', 'radiation to arm', 'diaphoresis', 'nausea'],
          differentials: [
            {
              condition: 'Acute MI',
              probability: 0.15,
              reasoning: 'Classic presentation with risk factors',
              urgency: 'immediate',
              workup: ['ECG', 'Troponins', 'CXR']
            },
            {
              condition: 'Unstable Angina',
              probability: 0.20,
              reasoning: 'Exertional symptoms with known CAD',
              urgency: 'urgent',
              workup: ['ECG', 'Stress test', 'Echo']
            }
          ]
        },
        
        headache: {
          redFlags: ['sudden onset', 'worst headache ever', 'fever', 'neck stiffness'],
          differentials: [
            {
              condition: 'Subarachnoid hemorrhage',
              probability: 0.05,
              reasoning: 'Thunderclap headache pattern',
              urgency: 'immediate',
              workup: ['CT head', 'LP if CT negative']
            }
          ]
        }
      },
      
      // Clinical decision trees based on experience
      decisionTrees: {
        chestPain: this.buildChestPainDecisionTree(),
        dyspnea: this.buildDyspneaDecisionTree(),
        abdominalPain: this.buildAbdominalPainDecisionTree()
      },
      
      // Treatment protocols
      treatmentProtocols: new Map(),
      
      // Drug prescribing patterns
      prescribingPatterns: new Map()
    };
  }

  /**
   * Build chest pain decision tree based on clinical experience
   */
  buildChestPainDecisionTree() {
    return {
      root: {
        question: 'Is the pain crushing/squeezing in nature?',
        yes: {
          question: 'Does it radiate to arm, jaw, or back?',
          yes: {
            question: 'Associated with diaphoresis or nausea?',
            yes: {
              diagnosis: 'High suspicion for ACS',
              action: 'Immediate ECG, IV access, aspirin, nitro',
              urgency: 'immediate'
            },
            no: {
              diagnosis: 'Moderate suspicion for ACS',
              action: 'ECG, troponins, monitor',
              urgency: 'urgent'
            }
          },
          no: {
            question: 'Worse with exertion?',
            yes: {
              diagnosis: 'Possible stable angina',
              action: 'Stress testing, cardiology consult',
              urgency: 'semi-urgent'
            }
          }
        },
        no: {
          question: 'Sharp, pleuritic pain?',
          yes: {
            diagnosis: 'Consider PE, pneumothorax, pericarditis',
            action: 'CXR, D-dimer, ECG',
            urgency: 'urgent'
          }
        }
      }
    };
  }

  buildDyspneaDecisionTree() {
    return {
      root: {
        question: 'Acute onset (minutes to hours)?',
        yes: {
          question: 'Chest pain present?',
          yes: {
            diagnosis: 'Consider PE, pneumothorax, MI',
            action: 'CXR, ECG, D-dimer, ABG',
            urgency: 'immediate'
          },
          no: {
            question: 'Wheezing present?',
            yes: {
              diagnosis: 'Acute asthma/COPD exacerbation',
              action: 'Bronchodilators, steroids, ABG',
              urgency: 'urgent'
            }
          }
        },
        no: {
          question: 'Progressive over days to weeks?',
          yes: {
            diagnosis: 'Consider CHF, anemia, pulmonary disease',
            action: 'Echo, CBC, BNP, CXR',
            urgency: 'semi-urgent'
          }
        }
      }
    };
  }

  buildAbdominalPainDecisionTree() {
    return {
      root: {
        question: 'Right lower quadrant pain?',
        yes: {
          question: 'McBurney\'s point tenderness?',
          yes: {
            diagnosis: 'Appendicitis',
            action: 'Surgery consult, CT abdomen, NPO',
            urgency: 'urgent'
          }
        },
        no: {
          question: 'Epigastric pain radiating to back?',
          yes: {
            diagnosis: 'Consider pancreatitis, peptic ulcer',
            action: 'Lipase, amylase, CT abdomen',
            urgency: 'urgent'
          }
        }
      }
    };
  }

  /**
   * Initialize vector database collections for RAG
   */
  async initializeVectorCollections() {
    try {
      // Medical literature collection
      this.medicalLiterature = await this.chromaClient.getOrCreateCollection({
        name: 'medical_literature',
        metadata: { description: 'PubMed abstracts and medical papers' }
      });

      // Clinical guidelines collection
      this.clinicalGuidelines = await this.chromaClient.getOrCreateCollection({
        name: 'clinical_guidelines',
        metadata: { description: 'WHO, CDC, and medical society guidelines' }
      });

      // Drug information collection
      this.drugInformation = await this.chromaClient.getOrCreateCollection({
        name: 'drug_information',
        metadata: { description: 'FDA drug information and interactions' }
      });

      // Case studies collection
      this.caseStudies = await this.chromaClient.getOrCreateCollection({
        name: 'case_studies',
        metadata: { description: 'Clinical case studies and outcomes' }
      });

      logger.info('Vector collections initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize vector collections:', error);
      throw error;
    }
  }

  /**
   * Load medical knowledge bases from external sources
   */
  async loadMedicalKnowledgeBases() {
    try {
      // Load ICD-10 codes
      await this.loadICD10Codes();
      
      // Load SNOMED CT concepts
      await this.loadSNOMEDConcepts();
      
      // Load drug interaction database
      await this.loadDrugInteractions();
      
      // Load clinical guidelines
      await this.loadClinicalGuidelines();
      
      logger.info('Medical knowledge bases loaded successfully');
    } catch (error) {
      logger.error('Failed to load medical knowledge bases:', error);
    }
  }

  async loadICD10Codes() {
    // Implementation for loading ICD-10 codes
    // This would typically load from a medical database or API
    logger.info('ICD-10 codes loaded');
  }

  async loadSNOMEDConcepts() {
    // Implementation for loading SNOMED CT concepts
    logger.info('SNOMED CT concepts loaded');
  }

  async loadDrugInteractions() {
    // Implementation for loading drug interaction database
    logger.info('Drug interactions database loaded');
  }

  async loadClinicalGuidelines() {
    // Implementation for loading clinical guidelines
    logger.info('Clinical guidelines loaded');
  }

  /**
   * Main consultation method - simulates 25 years of clinical experience
   */
  async consultAsExperiencedDoctor(patientData) {
    try {
      logger.info('Starting experienced doctor consultation');

      const consultation = {
        timestamp: new Date(),
        patientId: patientData.patientId,
        chiefComplaint: patientData.chiefComplaint,
        reasoning: [],
        assessment: null,
        plan: null,
        confidence: 0
      };

      // Step 1: Initial clinical impression (like a doctor's first thoughts)
      const initialImpression = await this.formInitialImpression(patientData);
      consultation.reasoning.push({
        step: 'Initial Impression',
        thought: initialImpression.thought,
        differentials: initialImpression.differentials
      });

      // Step 2: Systematic review and red flag assessment
      const redFlagAssessment = await this.assessRedFlags(patientData);
      consultation.reasoning.push({
        step: 'Red Flag Assessment',
        thought: redFlagAssessment.thought,
        urgency: redFlagAssessment.urgency,
        immediateActions: redFlagAssessment.actions
      });

      // Step 3: Differential diagnosis with clinical reasoning
      const differentialDx = await this.generateDifferentialDiagnosis(patientData);
      consultation.reasoning.push({
        step: 'Differential Diagnosis',
        thought: differentialDx.reasoning,
        differentials: differentialDx.diagnoses
      });

      // Step 4: Evidence-based workup recommendations
      const workupPlan = await this.recommendWorkup(differentialDx.diagnoses, patientData);
      consultation.reasoning.push({
        step: 'Diagnostic Workup',
        thought: workupPlan.reasoning,
        recommendations: workupPlan.tests
      });

      // Step 5: Treatment recommendations
      const treatmentPlan = await this.recommendTreatment(differentialDx.diagnoses, patientData);
      consultation.reasoning.push({
        step: 'Treatment Plan',
        thought: treatmentPlan.reasoning,
        medications: treatmentPlan.medications,
        nonPharmacologic: treatmentPlan.nonPharmacologic
      });

      // Step 6: Follow-up and monitoring
      const followUpPlan = await this.planFollowUp(differentialDx.diagnoses, patientData);
      consultation.reasoning.push({
        step: 'Follow-up Plan',
        thought: followUpPlan.reasoning,
        timeline: followUpPlan.timeline,
        monitoring: followUpPlan.monitoring
      });

      consultation.assessment = this.synthesizeAssessment(consultation.reasoning);
      consultation.plan = this.synthesizePlan(consultation.reasoning);
      consultation.confidence = this.calculateConfidence(consultation.reasoning);

      return consultation;
    } catch (error) {
      logger.error('Error in experienced doctor consultation:', error);
      throw error;
    }
  }

  /**
   * Form initial clinical impression based on chief complaint and presentation
   */
  async formInitialImpression(patientData) {
    const { chiefComplaint, symptoms, age, gender, vitalSigns } = patientData;

    // Simulate experienced doctor's immediate thoughts
    let thought = `A ${age}-year-old ${gender} presents with ${chiefComplaint}. `;

    // Access clinical patterns from experience base
    const relevantPatterns = this.findRelevantClinicalPatterns(chiefComplaint, symptoms);

    if (relevantPatterns.length > 0) {
      thought += `Based on my 25 years of experience, this presentation pattern suggests several possibilities. `;

      // Consider age and gender factors
      if (age > 50 && chiefComplaint.includes('chest pain')) {
        thought += `Given the patient's age, cardiac causes must be strongly considered. `;
      }

      if (gender === 'female' && age < 50 && chiefComplaint.includes('chest pain')) {
        thought += `In younger women, atypical presentations of cardiac disease are more common, but other causes like anxiety or musculoskeletal pain are also frequent. `;
      }
    }

    // Generate initial differential based on pattern recognition
    const differentials = relevantPatterns.map(pattern => ({
      condition: pattern.condition,
      probability: this.adjustProbabilityForDemographics(pattern.probability, age, gender),
      reasoning: pattern.reasoning
    }));

    return {
      thought,
      differentials: differentials.slice(0, 5) // Top 5 initial considerations
    };
  }

  /**
   * Assess for red flags that require immediate attention
   */
  async assessRedFlags(patientData) {
    const { symptoms, vitalSigns, chiefComplaint } = patientData;

    let urgency = 'routine';
    let actions = [];
    let thought = 'Assessing for red flags that would require immediate intervention. ';

    // Vital signs red flags
    if (vitalSigns) {
      if (vitalSigns.systolicBP > 180 || vitalSigns.diastolicBP > 110) {
        urgency = 'urgent';
        actions.push('Blood pressure management');
        thought += 'Hypertensive crisis noted - requires immediate BP control. ';
      }

      if (vitalSigns.heartRate > 120 || vitalSigns.heartRate < 50) {
        urgency = 'urgent';
        actions.push('Cardiac monitoring');
        thought += 'Significant tachycardia/bradycardia - needs cardiac evaluation. ';
      }

      if (vitalSigns.oxygenSaturation < 92) {
        urgency = 'urgent';
        actions.push('Oxygen supplementation');
        thought += 'Hypoxemia present - requires oxygen support. ';
      }
    }

    // Symptom-specific red flags
    if (chiefComplaint.includes('chest pain')) {
      const chestPainRedFlags = ['crushing', 'radiating', 'diaphoresis', 'nausea'];
      const hasRedFlags = chestPainRedFlags.some(flag =>
        symptoms.some(symptom => symptom.toLowerCase().includes(flag))
      );

      if (hasRedFlags) {
        urgency = 'immediate';
        actions.push('ECG', 'IV access', 'Cardiac enzymes');
        thought += 'Chest pain with concerning features - rule out acute coronary syndrome. ';
      }
    }

    if (chiefComplaint.includes('headache')) {
      const headacheRedFlags = ['sudden onset', 'worst ever', 'fever', 'neck stiffness'];
      const hasRedFlags = headacheRedFlags.some(flag =>
        symptoms.some(symptom => symptom.toLowerCase().includes(flag))
      );

      if (hasRedFlags) {
        urgency = 'immediate';
        actions.push('CT head', 'Lumbar puncture consideration');
        thought += 'Headache with red flags - concern for intracranial pathology. ';
      }
    }

    return { thought, urgency, actions };
  }

  /**
   * Generate differential diagnosis using clinical reasoning
   */
  async generateDifferentialDiagnosis(patientData) {
    const { chiefComplaint, symptoms, age, gender, medicalHistory } = patientData;

    let reasoning = 'Generating differential diagnosis based on clinical presentation and patient factors. ';

    // Use decision trees from experience base
    const decisionTree = this.getRelevantDecisionTree(chiefComplaint);
    let diagnoses = [];

    if (decisionTree) {
      diagnoses = this.traverseDecisionTree(decisionTree, symptoms, patientData);
      reasoning += `Using clinical decision tree for ${chiefComplaint}. `;
    }

    // Enhance with RAG retrieval from medical literature
    const ragResults = await this.retrieveRelevantMedicalLiterature(chiefComplaint, symptoms);
    if (ragResults.length > 0) {
      reasoning += 'Incorporating latest medical literature findings. ';
      diagnoses = this.enhanceDifferentialWithRAG(diagnoses, ragResults);
    }

    // Consider patient-specific factors
    if (medicalHistory && medicalHistory.length > 0) {
      reasoning += 'Adjusting probabilities based on patient medical history. ';
      diagnoses = this.adjustForMedicalHistory(diagnoses, medicalHistory);
    }

    // Sort by probability and clinical significance
    diagnoses.sort((a, b) => b.probability - a.probability);

    return {
      reasoning,
      diagnoses: diagnoses.slice(0, 8) // Top 8 differential diagnoses
    };
  }

  /**
   * Recommend diagnostic workup based on differential diagnosis
   */
  async recommendWorkup(differentials, patientData) {
    let reasoning = 'Planning diagnostic workup to differentiate between likely diagnoses. ';
    let tests = [];

    // Basic workup for most presentations
    tests.push({
      category: 'Basic Labs',
      tests: ['CBC', 'BMP', 'LFTs'],
      reasoning: 'Baseline laboratory assessment'
    });

    // Specific tests based on differentials
    differentials.forEach(differential => {
      const specificTests = this.getTestsForCondition(differential.condition);
      if (specificTests.length > 0) {
        tests.push({
          category: `For ${differential.condition}`,
          tests: specificTests,
          reasoning: `To evaluate for ${differential.condition}`
        });
      }
    });

    // Risk stratification
    const riskLevel = this.assessOverallRisk(differentials);
    if (riskLevel === 'high') {
      reasoning += 'High-risk presentation requires comprehensive workup. ';
      tests.push({
        category: 'High-Risk Protocol',
        tests: ['Troponins', 'D-dimer', 'Lactate'],
        reasoning: 'High-risk presentation protocol'
      });
    }

    return { reasoning, tests };
  }
}

module.exports = EnhancedAIService;
