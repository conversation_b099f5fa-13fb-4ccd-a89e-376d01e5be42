import { apiClient } from '@/services/api';

export const paymentService = {
  initiatePaystack: async (email: string, amount: number, planName: string, cycle: 'monthly'|'yearly') => {
    return apiClient.post<{ authorization_url: string; reference: string }>(
      '/payments/paystack/initiate',
      { email, amount, planName, cycle }
    );
  },
  verifyPaystack: async (reference: string) => {
    return apiClient.get(`/payments/paystack/verify?reference=${encodeURIComponent(reference)}`);
  },
  initiateFlutterwave: async (email: string, amount: number, planName: string, cycle: 'monthly'|'yearly') => {
    return apiClient.post<{ link: string; tx_ref: string }>(
      '/payments/flutterwave/initiate',
      { email, amount, planName, cycle }
    );
  },
  verifyFlutterwave: async (transaction_id: string) => {
    return apiClient.get(`/payments/flutterwave/verify?transaction_id=${encodeURIComponent(transaction_id)}`);
  },
};

