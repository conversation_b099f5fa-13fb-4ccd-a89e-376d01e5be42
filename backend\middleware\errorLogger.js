const logger = require('../utils/logger');
const AppError = require('../utils/appError');

/**
 * Enhanced error logging middleware
 * Logs errors with appropriate context and categorization
 */
const errorLogger = (err, req, res, next) => {
  // Add request context to error
  const errorContext = {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    body: req.method !== 'GET' ? sanitizeBody(req.body) : undefined,
    params: req.params,
    query: req.query,
    headers: sanitizeHeaders(req.headers)
  };

  // Log the error with context
  logger.logError(err, errorContext);

  // Log security events for specific error types
  if (isSecurityRelatedError(err)) {
    logger.logSecurityEvent(
      getSecurityEventType(err),
      {
        error: err.message,
        statusCode: err.statusCode,
        path: req.originalUrl
      },
      req
    );
  }

  // Continue to next error handler
  next(err);
};

/**
 * Sanitize request body to remove sensitive information
 * @param {Object} body - Request body
 * @returns {Object} Sanitized body
 */
function sanitizeBody(body) {
  if (!body || typeof body !== 'object') return body;

  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth', 'authorization'];
  const sanitized = { ...body };

  Object.keys(sanitized).forEach(key => {
    if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
      sanitized[key] = '[REDACTED]';
    }
  });

  return sanitized;
}

/**
 * Sanitize request headers to remove sensitive information
 * @param {Object} headers - Request headers
 * @returns {Object} Sanitized headers
 */
function sanitizeHeaders(headers) {
  if (!headers || typeof headers !== 'object') return headers;

  const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];
  const sanitized = { ...headers };

  Object.keys(sanitized).forEach(key => {
    if (sensitiveHeaders.includes(key.toLowerCase())) {
      sanitized[key] = '[REDACTED]';
    }
  });

  return sanitized;
}

/**
 * Check if error is security-related
 * @param {Error} err - Error object
 * @returns {boolean} True if security-related
 */
function isSecurityRelatedError(err) {
  const securityStatusCodes = [401, 403, 429];
  const securityMessages = [
    'unauthorized',
    'forbidden',
    'access denied',
    'invalid token',
    'rate limit',
    'too many requests',
    'authentication failed',
    'permission denied'
  ];

  return (
    securityStatusCodes.includes(err.statusCode) ||
    securityMessages.some(msg => 
      err.message.toLowerCase().includes(msg)
    )
  );
}

/**
 * Get security event type based on error
 * @param {Error} err - Error object
 * @returns {string} Security event type
 */
function getSecurityEventType(err) {
  if (err.statusCode === 401) return 'AUTHENTICATION_FAILURE';
  if (err.statusCode === 403) return 'AUTHORIZATION_FAILURE';
  if (err.statusCode === 429) return 'RATE_LIMIT_EXCEEDED';
  if (err.message.toLowerCase().includes('token')) return 'TOKEN_VALIDATION_FAILURE';
  return 'SECURITY_VIOLATION';
}

/**
 * Request logging middleware
 * Logs all incoming requests with timing
 */
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  
  // Log request start
  logger.debug('Request started', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id
  });

  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(...args) {
    const responseTime = Date.now() - startTime;
    
    // Log HTTP request with timing
    logger.logHTTPRequest(req, res, responseTime);
    
    // Call original end method
    originalEnd.apply(this, args);
  };

  next();
};

/**
 * Database operation logger
 * Wraps database operations with logging
 */
const dbLogger = {
  /**
   * Log database query
   * @param {string} operation - Database operation type
   * @param {string} collection - Collection/table name
   * @param {Object} query - Query object
   * @param {Function} callback - Operation callback
   */
  logQuery: async (operation, collection, query, callback) => {
    const timer = logger.startTimer(`DB ${operation} on ${collection}`);
    
    try {
      const result = await callback();
      const duration = timer.end();
      
      logger.debug('Database Operation', {
        operation,
        collection,
        query: JSON.stringify(query),
        resultCount: Array.isArray(result) ? result.length : result ? 1 : 0,
        duration: `${duration.toFixed(2)}ms`,
        success: true
      });
      
      return result;
    } catch (error) {
      const duration = timer.end();
      
      logger.error('Database Operation Failed', {
        operation,
        collection,
        query: JSON.stringify(query),
        duration: `${duration.toFixed(2)}ms`,
        error: error.message,
        success: false
      });
      
      throw error;
    }
  }
};

/**
 * Performance monitoring middleware
 * Tracks slow requests and resource usage
 */
const performanceMonitor = (req, res, next) => {
  const startTime = process.hrtime.bigint();
  const startMemory = process.memoryUsage();
  
  // Override res.end to capture performance metrics
  const originalEnd = res.end;
  res.end = function(...args) {
    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    
    // Log slow requests (> 1 second)
    if (duration > 1000) {
      logger.warn('Slow Request Detected', {
        method: req.method,
        url: req.originalUrl,
        duration: `${duration.toFixed(2)}ms`,
        memoryUsed: `${(endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024}MB`,
        userId: req.user?.id
      });
    }
    
    // Log performance metrics for monitoring
    const config = require('../config');
    if (config.server.isDevelopment) {
      logger.debug('Request Performance', {
        method: req.method,
        url: req.originalUrl,
        duration: `${duration.toFixed(2)}ms`,
        memoryDelta: `${(endMemory.heapUsed - startMemory.heapUsed) / 1024}KB`,
        statusCode: res.statusCode
      });
    }
    
    originalEnd.apply(this, args);
  };
  
  next();
};

/**
 * Health check logger
 * Logs system health metrics
 */
const healthLogger = {
  /**
   * Log system health metrics
   */
  logSystemHealth: () => {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    logger.info('System Health Check', {
      memory: {
        rss: `${(memUsage.rss / 1024 / 1024).toFixed(2)}MB`,
        heapTotal: `${(memUsage.heapTotal / 1024 / 1024).toFixed(2)}MB`,
        heapUsed: `${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`,
        external: `${(memUsage.external / 1024 / 1024).toFixed(2)}MB`
      },
      cpu: {
        user: `${(cpuUsage.user / 1000).toFixed(2)}ms`,
        system: `${(cpuUsage.system / 1000).toFixed(2)}ms`
      },
      uptime: `${(process.uptime() / 60).toFixed(2)} minutes`,
      timestamp: new Date().toISOString()
    });
  },
  
  /**
   * Start periodic health logging
   * @param {number} interval - Interval in milliseconds (default: 5 minutes)
   */
  startPeriodicLogging: (interval = 5 * 60 * 1000) => {
    setInterval(() => {
      healthLogger.logSystemHealth();
    }, interval);
    
    logger.info('Periodic health logging started', { interval: `${interval / 1000}s` });
  }
};

module.exports = {
  errorLogger,
  requestLogger,
  performanceMonitor,
  dbLogger,
  healthLogger
};