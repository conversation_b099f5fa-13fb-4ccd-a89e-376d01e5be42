const mongoose = require('mongoose');

// Sub-schema for symptom analysis results
const symptomAnalysisSchema = new mongoose.Schema({
  inputSymptoms: [{
    symptom: {
      type: String,
      required: true,
      trim: true,
    },
    severity: {
      type: String,
      enum: ['mild', 'moderate', 'severe', 'critical'],
      default: 'moderate',
    },
    duration: {
      type: String,
      trim: true,
    },
    frequency: {
      type: String,
      enum: ['constant', 'intermittent', 'occasional', 'rare'],
    },
  }],
  possibleConditions: [{
    condition: {
      type: String,
      required: true,
      trim: true,
    },
    probability: {
      type: Number,
      min: 0,
      max: 1,
      required: true,
    },
    confidence: {
      type: Number,
      min: 0,
      max: 1,
      required: true,
    },
    reasoning: {
      type: String,
      trim: true,
    },
    urgency: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium',
    },
    icdCode: {
      type: String,
      trim: true,
    },
  }],
  recommendedTests: [{
    test: {
      type: String,
      required: true,
      trim: true,
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium',
    },
    reasoning: {
      type: String,
      trim: true,
    },
    estimatedCost: {
      type: Number,
      min: 0,
    },
  }],
  redFlags: [{
    flag: {
      type: String,
      required: true,
      trim: true,
    },
    severity: {
      type: String,
      enum: ['warning', 'critical', 'emergency'],
      required: true,
    },
    action: {
      type: String,
      required: true,
      trim: true,
    },
  }],
}, { _id: false });

// Sub-schema for risk assessment results
const riskAssessmentSchema = new mongoose.Schema({
  condition: {
    type: String,
    required: true,
    trim: true,
  },
  overallRisk: {
    type: String,
    enum: ['very_low', 'low', 'moderate', 'high', 'very_high'],
    required: true,
  },
  riskScore: {
    type: Number,
    min: 0,
    max: 100,
    required: true,
  },
  riskFactors: [{
    factor: {
      type: String,
      required: true,
      trim: true,
    },
    impact: {
      type: String,
      enum: ['minimal', 'low', 'moderate', 'high', 'severe'],
      required: true,
    },
    modifiable: {
      type: Boolean,
      default: true,
    },
    recommendation: {
      type: String,
      trim: true,
    },
  }],
  protectiveFactors: [{
    factor: {
      type: String,
      required: true,
      trim: true,
    },
    benefit: {
      type: String,
      enum: ['minimal', 'low', 'moderate', 'high', 'significant'],
      required: true,
    },
  }],
  timeframe: {
    type: String,
    enum: ['1_month', '3_months', '6_months', '1_year', '5_years', '10_years'],
    default: '1_year',
  },
  preventionStrategies: [{
    strategy: {
      type: String,
      required: true,
      trim: true,
    },
    effectiveness: {
      type: Number,
      min: 0,
      max: 100,
    },
    difficulty: {
      type: String,
      enum: ['easy', 'moderate', 'difficult', 'very_difficult'],
    },
  }],
}, { _id: false });

// Sub-schema for drug interaction analysis
const drugInteractionSchema = new mongoose.Schema({
  medications: [{
    name: {
      type: String,
      required: true,
      trim: true,
    },
    dosage: {
      type: String,
      trim: true,
    },
    frequency: {
      type: String,
      trim: true,
    },
  }],
  interactions: [{
    drug1: {
      type: String,
      required: true,
      trim: true,
    },
    drug2: {
      type: String,
      required: true,
      trim: true,
    },
    severity: {
      type: String,
      enum: ['minor', 'moderate', 'major', 'contraindicated'],
      required: true,
    },
    mechanism: {
      type: String,
      trim: true,
    },
    clinicalEffect: {
      type: String,
      trim: true,
    },
    management: {
      type: String,
      trim: true,
    },
    evidence: {
      type: String,
      enum: ['theoretical', 'case_report', 'study', 'established'],
      default: 'study',
    },
  }],
  contraindications: [{
    medication: {
      type: String,
      required: true,
      trim: true,
    },
    condition: {
      type: String,
      required: true,
      trim: true,
    },
    severity: {
      type: String,
      enum: ['relative', 'absolute'],
      required: true,
    },
    reasoning: {
      type: String,
      trim: true,
    },
  }],
  dosageAdjustments: [{
    medication: {
      type: String,
      required: true,
      trim: true,
    },
    reason: {
      type: String,
      required: true,
      trim: true,
    },
    adjustment: {
      type: String,
      required: true,
      trim: true,
    },
  }],
}, { _id: false });

// Sub-schema for treatment recommendations
const treatmentRecommendationSchema = new mongoose.Schema({
  primaryTreatments: [{
    treatment: {
      type: String,
      required: true,
      trim: true,
    },
    type: {
      type: String,
      enum: ['medication', 'procedure', 'therapy', 'lifestyle', 'surgery'],
      required: true,
    },
    priority: {
      type: Number,
      min: 1,
      max: 10,
      required: true,
    },
    evidence: {
      type: String,
      enum: ['grade_a', 'grade_b', 'grade_c', 'expert_opinion'],
      required: true,
    },
    expectedOutcome: {
      type: String,
      trim: true,
    },
    duration: {
      type: String,
      trim: true,
    },
    sideEffects: [{
      type: String,
      trim: true,
    }],
    cost: {
      type: String,
      enum: ['low', 'moderate', 'high', 'very_high'],
    },
  }],
  alternativeTreatments: [{
    treatment: {
      type: String,
      required: true,
      trim: true,
    },
    type: {
      type: String,
      enum: ['medication', 'procedure', 'therapy', 'lifestyle', 'surgery'],
      required: true,
    },
    reasoning: {
      type: String,
      trim: true,
    },
    whenToConsider: {
      type: String,
      trim: true,
    },
  }],
  monitoringPlan: [{
    parameter: {
      type: String,
      required: true,
      trim: true,
    },
    frequency: {
      type: String,
      required: true,
      trim: true,
    },
    targetRange: {
      type: String,
      trim: true,
    },
  }],
  followUpSchedule: [{
    timepoint: {
      type: String,
      required: true,
      trim: true,
    },
    purpose: {
      type: String,
      required: true,
      trim: true,
    },
    assessments: [{
      type: String,
      trim: true,
    }],
  }],
}, { _id: false });

// Sub-schema for outcome predictions
const outcomePredictionSchema = new mongoose.Schema({
  timeframe: {
    type: String,
    enum: ['1_week', '1_month', '3_months', '6_months', '1_year', '5_years'],
    required: true,
  },
  predictions: [{
    outcome: {
      type: String,
      required: true,
      trim: true,
    },
    probability: {
      type: Number,
      min: 0,
      max: 1,
      required: true,
    },
    confidence: {
      type: Number,
      min: 0,
      max: 1,
      required: true,
    },
    factors: [{
      factor: {
        type: String,
        required: true,
        trim: true,
      },
      influence: {
        type: String,
        enum: ['strongly_negative', 'negative', 'neutral', 'positive', 'strongly_positive'],
        required: true,
      },
    }],
  }],
  qualityOfLife: {
    predicted: {
      type: Number,
      min: 0,
      max: 100,
    },
    confidence: {
      type: Number,
      min: 0,
      max: 1,
    },
  },
  functionalStatus: {
    predicted: {
      type: String,
      enum: ['independent', 'minimal_assistance', 'moderate_assistance', 'dependent'],
    },
    confidence: {
      type: Number,
      min: 0,
      max: 1,
    },
  },
  riskFactorsEvolution: [{
    factor: {
      type: String,
      required: true,
      trim: true,
    },
    currentLevel: {
      type: String,
      enum: ['low', 'moderate', 'high'],
      required: true,
    },
    predictedLevel: {
      type: String,
      enum: ['low', 'moderate', 'high'],
      required: true,
    },
  }],
}, { _id: false });

// Main ML Analysis schema
const mlAnalysisSchema = new mongoose.Schema({
  analysisType: {
    type: String,
    required: true,
    enum: ['symptom_analysis', 'risk_assessment', 'drug_interactions', 'treatment_recommendations', 'outcome_prediction', 'diagnosis_validation'],
    index: true,
  },
  
  // Related entities
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    index: true,
  },
  diagnosis: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Diagnosis',
    index: true,
  },
  
  // Analysis results based on type
  symptomAnalysis: symptomAnalysisSchema,
  riskAssessment: riskAssessmentSchema,
  drugInteractions: drugInteractionSchema,
  treatmentRecommendations: treatmentRecommendationSchema,
  outcomePredictions: outcomePredictionSchema,
  
  // ML Model information
  modelInfo: {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    version: {
      type: String,
      required: true,
      trim: true,
    },
    algorithm: {
      type: String,
      trim: true,
    },
    trainingDate: {
      type: Date,
    },
    accuracy: {
      type: Number,
      min: 0,
      max: 1,
    },
    precision: {
      type: Number,
      min: 0,
      max: 1,
    },
    recall: {
      type: Number,
      min: 0,
      max: 1,
    },
    f1Score: {
      type: Number,
      min: 0,
      max: 1,
    },
  },
  
  // Input data
  inputData: {
    type: mongoose.Schema.Types.Mixed,
    required: true,
  },
  
  // Analysis metadata
  analyzedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  analyzedAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
  processingTime: {
    type: Number, // in milliseconds
    min: 0,
  },
  
  // Quality and validation
  confidence: {
    type: Number,
    min: 0,
    max: 1,
    required: true,
  },
  reliability: {
    type: String,
    enum: ['very_low', 'low', 'moderate', 'high', 'very_high'],
    default: 'moderate',
  },
  
  // Validation and review
  status: {
    type: String,
    enum: ['pending', 'completed', 'reviewed', 'approved', 'rejected'],
    default: 'completed',
    index: true,
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  reviewedAt: {
    type: Date,
  },
  reviewNotes: {
    type: String,
    trim: true,
  },
  
  // Clinical decision support
  clinicalRelevance: {
    type: String,
    enum: ['low', 'moderate', 'high', 'critical'],
    default: 'moderate',
  },
  actionRequired: {
    type: Boolean,
    default: false,
  },
  urgency: {
    type: String,
    enum: ['routine', 'urgent', 'emergent'],
    default: 'routine',
  },
  
  // Feedback and learning
  feedback: [{
    providedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    rating: {
      type: Number,
      min: 1,
      max: 5,
      required: true,
    },
    comments: {
      type: String,
      trim: true,
    },
    accuracy: {
      type: String,
      enum: ['very_poor', 'poor', 'fair', 'good', 'excellent'],
    },
    usefulness: {
      type: String,
      enum: ['not_useful', 'somewhat_useful', 'useful', 'very_useful', 'extremely_useful'],
    },
    providedAt: {
      type: Date,
      default: Date.now,
    },
  }],
  
  // Usage tracking
  accessCount: {
    type: Number,
    default: 0,
    min: 0,
  },
  lastAccessed: {
    type: Date,
  },
  
  // Integration with external systems
  externalReferences: [{
    system: {
      type: String,
      required: true,
      trim: true,
    },
    reference: {
      type: String,
      required: true,
      trim: true,
    },
    url: {
      type: String,
      trim: true,
    },
  }],
  
  // Compliance and audit
  auditTrail: [{
    action: {
      type: String,
      required: true,
      enum: ['created', 'viewed', 'modified', 'reviewed', 'approved', 'rejected'],
    },
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
    details: {
      type: String,
      trim: true,
    },
  }],
  
  // Data retention
  retentionPolicy: {
    type: String,
    enum: ['1_year', '5_years', '10_years', 'indefinite'],
    default: '5_years',
  },
  expiresAt: {
    type: Date,
    index: { expireAfterSeconds: 0 },
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for performance
mlAnalysisSchema.index({ analysisType: 1, analyzedAt: -1 });
mlAnalysisSchema.index({ patient: 1, analysisType: 1, analyzedAt: -1 });
mlAnalysisSchema.index({ analyzedBy: 1, analyzedAt: -1 });
mlAnalysisSchema.index({ status: 1, clinicalRelevance: 1 });
mlAnalysisSchema.index({ 'modelInfo.name': 1, 'modelInfo.version': 1 });
mlAnalysisSchema.index({ confidence: -1, reliability: 1 });

// Virtual for analysis age
mlAnalysisSchema.virtual('analysisAge').get(function() {
  return Date.now() - this.analyzedAt.getTime();
});

// Virtual for average feedback rating
mlAnalysisSchema.virtual('averageRating').get(function() {
  if (!this.feedback || this.feedback.length === 0) return null;
  const sum = this.feedback.reduce((acc, fb) => acc + fb.rating, 0);
  return sum / this.feedback.length;
});

// Virtual for analysis summary
mlAnalysisSchema.virtual('summary').get(function() {
  switch (this.analysisType) {
    case 'symptom_analysis':
      return `Analyzed ${this.symptomAnalysis?.inputSymptoms?.length || 0} symptoms, found ${this.symptomAnalysis?.possibleConditions?.length || 0} possible conditions`;
    case 'risk_assessment':
      return `Risk assessment for ${this.riskAssessment?.condition}: ${this.riskAssessment?.overallRisk} risk`;
    case 'drug_interactions':
      return `Checked ${this.drugInteractions?.medications?.length || 0} medications, found ${this.drugInteractions?.interactions?.length || 0} interactions`;
    case 'treatment_recommendations':
      return `Generated ${this.treatmentRecommendations?.primaryTreatments?.length || 0} primary treatment recommendations`;
    case 'outcome_prediction':
      return `Predicted outcomes for ${this.outcomePredictions?.timeframe} timeframe`;
    default:
      return `${this.analysisType} analysis completed`;
  }
});

// Pre-save middleware
mlAnalysisSchema.pre('save', function(next) {
  // Set expiration based on retention policy
  if (!this.expiresAt && this.retentionPolicy !== 'indefinite') {
    const now = new Date();
    switch (this.retentionPolicy) {
      case '1_year':
        this.expiresAt = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
        break;
      case '5_years':
        this.expiresAt = new Date(now.getTime() + 5 * 365 * 24 * 60 * 60 * 1000);
        break;
      case '10_years':
        this.expiresAt = new Date(now.getTime() + 10 * 365 * 24 * 60 * 60 * 1000);
        break;
    }
  }
  
  // Add audit trail entry for creation
  if (this.isNew) {
    this.auditTrail.push({
      action: 'created',
      performedBy: this.analyzedBy,
      details: `${this.analysisType} analysis created`,
    });
  }
  
  next();
});

// Instance methods
mlAnalysisSchema.methods.addFeedback = function(userId, rating, comments, accuracy, usefulness) {
  this.feedback.push({
    providedBy: userId,
    rating,
    comments,
    accuracy,
    usefulness,
  });
  return this.save();
};

mlAnalysisSchema.methods.review = function(userId, status, notes) {
  this.status = status;
  this.reviewedBy = userId;
  this.reviewedAt = new Date();
  this.reviewNotes = notes;
  
  this.auditTrail.push({
    action: 'reviewed',
    performedBy: userId,
    details: `Analysis ${status}: ${notes}`,
  });
  
  return this.save();
};

mlAnalysisSchema.methods.incrementAccess = function() {
  this.accessCount += 1;
  this.lastAccessed = new Date();
  
  this.auditTrail.push({
    action: 'viewed',
    performedBy: this.analyzedBy, // This should be updated with actual user accessing
    details: 'Analysis accessed',
  });
  
  return this.save();
};

// Static methods
mlAnalysisSchema.statics.findByPatient = function(patientId, analysisType = null) {
  const query = { patient: patientId };
  if (analysisType) {
    query.analysisType = analysisType;
  }
  return this.find(query).sort({ analyzedAt: -1 });
};

mlAnalysisSchema.statics.getAnalyticsData = function(startDate, endDate, analysisType = null) {
  const matchCriteria = {
    analyzedAt: {
      $gte: startDate,
      $lte: endDate,
    },
  };
  
  if (analysisType) {
    matchCriteria.analysisType = analysisType;
  }
  
  return this.aggregate([
    { $match: matchCriteria },
    {
      $group: {
        _id: {
          analysisType: '$analysisType',
          status: '$status',
        },
        count: { $sum: 1 },
        avgConfidence: { $avg: '$confidence' },
        avgProcessingTime: { $avg: '$processingTime' },
        totalAccess: { $sum: '$accessCount' },
      },
    },
    { $sort: { '_id.analysisType': 1, '_id.status': 1 } },
  ]);
};

mlAnalysisSchema.statics.getModelPerformance = function(modelName, modelVersion = null) {
  const matchCriteria = { 'modelInfo.name': modelName };
  if (modelVersion) {
    matchCriteria['modelInfo.version'] = modelVersion;
  }
  
  return this.aggregate([
    { $match: matchCriteria },
    {
      $group: {
        _id: {
          name: '$modelInfo.name',
          version: '$modelInfo.version',
        },
        totalAnalyses: { $sum: 1 },
        avgConfidence: { $avg: '$confidence' },
        avgRating: { $avg: { $avg: '$feedback.rating' } },
        statusDistribution: {
          $push: '$status',
        },
      },
    },
  ]);
};

module.exports = mongoose.model('MLAnalysis', mlAnalysisSchema);