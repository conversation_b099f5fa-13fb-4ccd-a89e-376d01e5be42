import React from 'react';
import { motion } from 'framer-motion';
import PatientDashboard from '@/components/dashboards/PatientDashboard';
import { useAuth } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';

const PatientDashboardPage: React.FC = () => {
  const { user, isAuthenticated, loading } = useAuth();

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/patient-login" replace />;
  }

  // Redirect if wrong role
  if (user?.role !== 'patient') {
    const redirectPath = user?.role === 'doctor' ? '/doctor-dashboard' : 
                        user?.role === 'nurse' ? '/nurse-dashboard' : 
                        user?.role === 'admin' ? '/admin-dashboard' : '/patient-login';
    return <Navigate to={redirectPath} replace />;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.5 }}
    >
      <PatientDashboard />
    </motion.div>
  );
};

export default PatientDashboardPage;
