import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Stethoscope, Plus, X, AlertCircle, Target } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiClient } from '@/services/api';

interface DiagnosisInterfaceProps {
  patientId: string;
  onDiagnosisCreated?: (diagnosis: unknown) => void;
}

const DiagnosisInterface: React.FC<DiagnosisInterfaceProps> = ({ patientId, onDiagnosisCreated }) => {
  const [diagnosisData, setDiagnosisData] = useState<unknown>({
    patient: patientId,
    symptoms: [],
    physicalExamination: { general: '', vitals: '', systems: [] },
    priority: 'medium'
  });
  const [aiAnalysis, setAiAnalysis] = useState<unknown>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('symptoms');
  const { toast } = useToast();

  const addSymptom = () => {
    setDiagnosisData((prev: unknown) => ({
      ...prev,
      symptoms: [...(prev.symptoms || []), { name: '', severity: 'mild', duration: '', description: '', onset: '' }]
    }));
  };

  const updateSymptom = (index: number, field: string, value: string) => {
    setDiagnosisData((prev: unknown) => ({
      ...prev,
      symptoms: prev.symptoms?.map((symptom: unknown, i: number) => 
        i === index ? { ...symptom, [field]: value } : symptom
      ) || []
    }));
  };

  const removeSymptom = (index: number) => {
    setDiagnosisData((prev: unknown) => ({
      ...prev,
      symptoms: prev.symptoms?.filter((_: unknown, i: number) => i !== index) || []
    }));
  };

  const runAIAnalysis = async () => {
    if (!diagnosisData.symptoms || diagnosisData.symptoms.length === 0) {
      toast({
        title: "No symptoms to analyze",
        description: "Please add at least one symptom before running AI analysis.",
        variant: "destructive"
      });
      return;
    }

    setIsAnalyzing(true);
    try {
      const response = await apiClient.post('/api/diagnosis/analyze-symptoms', {
        symptoms: diagnosisData.symptoms,
        patientId,
        includeHistory: true
      });

      setAiAnalysis((response as unknown).data.data.analysis);
      
      toast({
        title: "AI Analysis Complete",
        description: "Symptom analysis has been completed successfully."
      });
    } catch (error: unknown) {
      toast({
        title: "Analysis failed",
        description: error.response?.data?.message || "Failed to analyze symptoms.",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const saveDiagnosis = async () => {
    if (!diagnosisData.chiefComplaint || !diagnosisData.symptoms || diagnosisData.symptoms.length === 0) {
      toast({
        title: "Missing required fields",
        description: "Please fill in chief complaint and at least one symptom.",
        variant: "destructive"
      });
      return;
    }

    setIsSaving(true);
    try {
      const response = await apiClient.post('/api/diagnosis', diagnosisData);
      
      toast({
        title: "Diagnosis saved",
        description: "Diagnosis has been created successfully."
      });

      if (onDiagnosisCreated) {
        onDiagnosisCreated((response as unknown).data.data.diagnosis);
      }

      // Reset form
      setDiagnosisData({
        patient: patientId,
        symptoms: [],
        physicalExamination: { general: '', vitals: '', systems: [] },
        priority: 'medium'
      });
      setAiAnalysis(null);
    } catch (error: unknown) {
      toast({
        title: "Save failed",
        description: error.response?.data?.message || "Failed to save diagnosis.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Stethoscope className="h-5 w-5" />
            New Diagnosis
          </CardTitle>
          <CardDescription>
            Create a comprehensive diagnosis with AI-powered analysis
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="symptoms">Symptoms</TabsTrigger>
          <TabsTrigger value="diagnosis">Diagnosis</TabsTrigger>
          <TabsTrigger value="ai-analysis">AI Analysis</TabsTrigger>
        </TabsList>

        {/* Symptoms Tab */}
        <TabsContent value="symptoms" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Chief Complaint & Symptoms</CardTitle>
                <Button variant="outline" size="sm" onClick={addSymptom}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Symptom
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="chiefComplaint">Chief Complaint *</Label>
                <Textarea
                  id="chiefComplaint"
                  value={diagnosisData.chiefComplaint || ''}
                  onChange={(e) => setDiagnosisData((prev: unknown) => ({ ...prev, chiefComplaint: e.target.value }))}
                  placeholder="Patient's primary concern in their own words..."
                  rows={3}
                />
              </div>

              <div className="space-y-4">
                <Label>Symptoms</Label>
                {diagnosisData.symptoms?.map((symptom: unknown, index: number) => (
                  <Card key={index} className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">Symptom {index + 1}</h4>
                      <Button variant="ghost" size="sm" onClick={() => removeSymptom(index)}>
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Symptom Name *</Label>
                        <Input
                          value={symptom.name}
                          onChange={(e) => updateSymptom(index, 'name', e.target.value)}
                          placeholder="e.g., chest pain, headache"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Severity</Label>
                        <Select
                          value={symptom.severity}
                          onValueChange={(value) => updateSymptom(index, 'severity', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="mild">Mild</SelectItem>
                            <SelectItem value="moderate">Moderate</SelectItem>
                            <SelectItem value="severe">Severe</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="mt-4 space-y-2">
                      <Label>Description</Label>
                      <Textarea
                        value={symptom.description}
                        onChange={(e) => updateSymptom(index, 'description', e.target.value)}
                        placeholder="Detailed description of the symptom..."
                        rows={2}
                      />
                    </div>
                  </Card>
                ))}
              </div>

              <Button onClick={runAIAnalysis} disabled={isAnalyzing || !diagnosisData.symptoms?.length}>
                <img
                  src="/lovable-uploads/logo-png.png"
                  alt="MEDORA AI"
                  className="h-4 w-4 mr-2 object-contain"
                />
                {isAnalyzing ? "Analyzing..." : "Run AI Analysis"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Diagnosis Tab */}
        <TabsContent value="diagnosis" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Diagnosis & Assessment</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="primaryCondition">Primary Diagnosis *</Label>
                  <Input
                    id="primaryCondition"
                    value={diagnosisData.primaryDiagnosis?.condition || ''}
                    onChange={(e) => setDiagnosisData((prev: unknown) => ({
                      ...prev,
                      primaryDiagnosis: { ...prev.primaryDiagnosis, condition: e.target.value }
                    }))}
                    placeholder="Primary diagnosis condition"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority Level</Label>
                  <Select
                    value={diagnosisData.priority || 'medium'}
                    onValueChange={(value) => setDiagnosisData((prev: unknown) => ({ ...prev, priority: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="emergency">Emergency</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="clinicalNotes">Clinical Notes</Label>
                <Textarea
                  id="clinicalNotes"
                  value={diagnosisData.clinicalNotes || ''}
                  onChange={(e) => setDiagnosisData((prev: unknown) => ({ ...prev, clinicalNotes: e.target.value }))}
                  placeholder="Additional clinical notes and observations..."
                  rows={4}
                />
              </div>

              <Button onClick={saveDiagnosis} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Diagnosis"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* AI Analysis Tab */}
        <TabsContent value="ai-analysis" className="space-y-4">
          {aiAnalysis ? (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <img
                      src="/lovable-uploads/logo-png.png"
                      alt="MEDORA AI"
                      className="h-5 w-5 object-contain"
                    />
                    AI Differential Diagnosis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {aiAnalysis.predictions?.map((prediction: unknown, index: number) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{prediction.condition}</h4>
                          <Badge variant="default">{prediction.probability}%</Badge>
                        </div>
                        <div className="text-sm space-y-2">
                          <div>
                            <span className="text-gray-600">Supporting Evidence:</span>
                            <p>{prediction.supportingEvidence?.join(', ')}</p>
                          </div>
                          <div>
                            <span className="text-gray-600">Recommended Tests:</span>
                            <p>{prediction.recommendedTests?.join(', ')}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5" />
                    Risk Assessment
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-sm font-medium">Overall Risk:</span>
                      <Badge variant="default">{aiAnalysis.riskAssessment?.overallRisk?.toUpperCase()}</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    AI Recommendations
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {aiAnalysis.recommendations?.immediateActions?.length > 0 && (
                    <div>
                      <h4 className="font-medium text-sm mb-2 text-red-600">Immediate Actions</h4>
                      <ul className="space-y-1">
                        {aiAnalysis.recommendations.immediateActions.map((action: string, index: number) => (
                          <li key={index} className="text-sm flex items-center gap-2">
                            <AlertCircle className="h-3 w-3 text-red-500" />
                            {action}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <img
                  src="/lovable-uploads/logo-png.png"
                  alt="MEDORA AI"
                  className="h-12 w-12 mx-auto opacity-40 mb-4 object-contain"
                />
                <p className="text-gray-600 mb-4">No AI analysis available</p>
                <Button onClick={runAIAnalysis} disabled={isAnalyzing || !diagnosisData.symptoms?.length}>
                  <img
                    src="/lovable-uploads/logo-png.png"
                    alt="MEDORA AI"
                    className="h-4 w-4 mr-2 object-contain"
                  />
                  {isAnalyzing ? "Analyzing..." : "Run AI Analysis"}
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DiagnosisInterface;
