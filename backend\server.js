const express = require('express');
const cors = require('cors');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const session = require('express-session');
const passport = require('./config/passport');
const config = require('./config');

const app = express();

const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const User = require('./models/User');
const Setting = require('./models/Setting');
const Payment = require('./models/Payment');
const Subscription = require('./models/Subscription');
// Data persistence (demo JSON file)
const DATA_DIR = path.join(__dirname, 'data');
const DATA_FILE = path.join(DATA_DIR, 'store.json');

function ensureDataFile() {
  if (!fs.existsSync(DATA_DIR)) fs.mkdirSync(DATA_DIR, { recursive: true });
  if (!fs.existsSync(DATA_FILE)) {
    fs.writeFileSync(DATA_FILE, JSON.stringify({ payments: [], subscriptions: {}, settings: {}, users: [] }, null, 2));
  }
}

function loadStore() {
  try {
    ensureDataFile();
    const raw = fs.readFileSync(DATA_FILE, 'utf8');
    return JSON.parse(raw);
  } catch (e) {
    console.error('Failed to load data store:', e);
    return { payments: [], subscriptions: {}, settings: {}, users: [] };
  }
}

function saveStore() {
  try {
    ensureDataFile();
    fs.writeFileSync(DATA_FILE, JSON.stringify(store, null, 2));
  } catch (e) {
    console.error('Failed to save data store:', e);
  }
}

// In-memory demo store (replace with DB in production)
const store = loadStore();

// Middleware
app.use(cors({
  origin: function (origin, callback) {
    // Get allowed origins from configuration
    const allowedOrigins = config.getAllowedOrigins();

    console.log('🔒 CORS Configuration:', {
      requestOrigin: origin,
      allowedOrigins: allowedOrigins,
      configuredOrigins: config.cors.allowedOrigins.length
    });

    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) {
      console.log('✅ CORS: Allowing request with no origin');
      return callback(null, true);
    }

    // Normalize the request origin
    const normalizedOrigin = origin.replace(/\/$/, '');

    if (allowedOrigins.includes(normalizedOrigin)) {
      console.log('✅ CORS: Allowing origin:', normalizedOrigin);
      callback(null, true);
    } else {
      console.log('❌ CORS: Blocked origin:', normalizedOrigin);
      console.log('📋 Allowed origins:', allowedOrigins);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin', 'Cache-Control'],
  exposedHeaders: ['Content-Range', 'X-Content-Range'],
  optionsSuccessStatus: 200 // Some legacy browsers (IE11, various SmartTVs) choke on 204
}));

// JSON parser, but we'll override for webhooks that need raw body
app.use(express.json({ verify: (req, res, buf) => { req.rawBody = buf } }));
app.use(express.urlencoded({ extended: true }));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`📥 ${req.method} ${req.path}`, {
    body: req.body,
    query: req.query,
    headers: {
      'content-type': req.headers['content-type'],
      'origin': req.headers.origin,
      'authorization': req.headers.authorization ? 'Bearer ***' : 'None'
    }
  });
  next();
});

// Session configuration for OAuth
app.use(session({
  secret: config.auth.sessionSecret,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: config.server.isProduction,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Initialize Passport
app.use(passport.initialize());
app.use(passport.session());

// Connect to MongoDB (RBAC) - Optimized for Vercel serverless
let isMongoConnected = false;
let connectionAttempts = 0;
const MAX_CONNECTION_ATTEMPTS = 3;

async function connectToMongoDB() {
  if (isMongoConnected) {
    return true;
  }

  if (!config.isMongoConfigured()) {
    console.log('⚠️  MongoDB URI not configured, running in demo mode');
    console.log('💡 Set MONGODB_URI environment variable for full functionality');
    return false;
  }

  try {
    connectionAttempts++;
    console.log(`🔄 Attempting MongoDB connection (${connectionAttempts}/${MAX_CONNECTION_ATTEMPTS})...`);

    await mongoose.connect(config.database.mongoUri, {
      dbName: config.database.dbName,
      serverSelectionTimeoutMS: 10000, // 10s timeout for serverless
      socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
      bufferCommands: false, // Disable mongoose buffering
      bufferMaxEntries: 0, // Disable mongoose buffering
      maxPoolSize: 10, // Maintain up to 10 socket connections
      minPoolSize: 1, // Maintain at least 1 socket connection
      maxIdleTimeMS: 30000, // Close connections after 30s of inactivity
      retryWrites: true,
      w: 'majority'
    });

    console.log('✅ Connected to MongoDB Atlas');
    isMongoConnected = true;
    connectionAttempts = 0; // Reset on successful connection
    return true;
  } catch (e) {
    console.error(`❌ MongoDB connection attempt ${connectionAttempts} failed:`, e.message);

    if (connectionAttempts < MAX_CONNECTION_ATTEMPTS) {
      console.log(`🔄 Retrying connection in 2 seconds...`);
      setTimeout(() => connectToMongoDB(), 2000);
    } else {
      console.log('⚠️  All MongoDB connection attempts failed - running in demo mode');
      console.log('💡 Check your MONGODB_URI and network connectivity');
    }

    isMongoConnected = false;
    return false;
  }
}

// Handle MongoDB connection events
mongoose.connection.on('connected', () => {
  console.log('🔗 Mongoose connected to MongoDB');
  isMongoConnected = true;
});

mongoose.connection.on('error', (err) => {
  console.error('❌ Mongoose connection error:', err.message);
  isMongoConnected = false;
});

mongoose.connection.on('disconnected', () => {
  console.log('🔌 Mongoose disconnected from MongoDB');
  isMongoConnected = false;

  // Attempt to reconnect in serverless environment
  if (config.isMongoConfigured() && connectionAttempts < MAX_CONNECTION_ATTEMPTS) {
    setTimeout(() => connectToMongoDB(), 5000);
  }
});

// Initial connection attempt
connectToMongoDB();

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'MEDORA Backend Server is running',
    timestamp: new Date().toISOString(),
    environment: config.server.nodeEnv,
    database: {
      mongodb_configured: config.isMongoConfigured(),
      mongodb_connected: isMongoConnected,
      connection_status: isMongoConnected ? 'connected' : 'disconnected'
    },
    services: {
      openai_configured: config.isOpenAiConfigured(),
      paystack_configured: config.isPaystackConfigured(),
      flutterwave_configured: config.isFlutterwaveConfigured()
    }
  });
});

// API status endpoint
function sanitizeUser(user) {
  const u = user.toObject ? user.toObject() : user;
  delete u.password;
// Seed initial admin if not exists
(async () => {
  try {
    if (!isMongoConnected) {
      console.log('⚠️  Skipping admin seed - MongoDB not connected');
      return;
    }
    const adminEmail = '<EMAIL>';
    const existing = await User.findOne({ email: adminEmail });
    if (!existing) {
      const hash = await bcrypt.hash('Americana123456789@', 12);
      await User.create({ email: adminEmail, password: hash, firstName: 'Dev', lastName: 'Admin', role: 'admin', isVerified: true });
      console.log('✅ Seeded initial admin user:', adminEmail);
    }
  } catch (e) {
    console.warn('Admin seed skipped:', e.message);
  }
})();
  delete u.passwordHash;
  return u;
}

app.get('/api/status', (req, res) => {
  res.json({
    status: 'success',
    message: 'API is working',
    version: '1.0.0',
    database: {
      mongodb_configured: config.isMongoConfigured(),
      mongodb_connected: isMongoConnected,
      connection_status: isMongoConnected ? 'connected' : 'demo_mode'
    },
    features: {
      openai: config.isOpenAiConfigured(),
      mongodb: isMongoConnected,
      paystack: config.isPaystackConfigured(),
      flutterwave: config.isFlutterwaveConfigured()
    },
    environment: config.server.nodeEnv
  });
});

// Database health check endpoint
app.get('/api/health/database', async (req, res) => {
  try {
    const dbStatus = {
      configured: config.isMongoConfigured(),
      connected: isMongoConnected,
      status: isMongoConnected ? 'healthy' : 'disconnected',
      mode: isMongoConnected ? 'production' : 'demo',
      connection_attempts: connectionAttempts,
      max_attempts: MAX_CONNECTION_ATTEMPTS
    };

    if (isMongoConnected) {
      // Test database connection with a simple query
      try {
        const startTime = Date.now();
        await mongoose.connection.db.admin().ping();
        const endTime = Date.now();

        dbStatus.ping = 'success';
        dbStatus.latency = `${endTime - startTime}ms`;
        dbStatus.ready_state = mongoose.connection.readyState;
        dbStatus.database_name = mongoose.connection.name;
      } catch (pingError) {
        dbStatus.ping = 'failed';
        dbStatus.error = pingError.message;
        dbStatus.status = 'unhealthy';
      }
    } else {
      dbStatus.reason = config.isMongoConfigured() ? 'connection_failed' : 'not_configured';
      dbStatus.message = config.isMongoConfigured()
        ? 'MongoDB URI configured but connection failed'
        : 'MongoDB URI not configured - running in demo mode';

      if (config.isMongoConfigured()) {
        dbStatus.uri_format = config.database.mongoUri.replace(/\/\/[^:]+:[^@]+@/, '//***:***@');
      }
    }

    res.json({
      status: 'success',
      data: dbStatus,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Database health check failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// API documentation endpoint
app.get('/api', (req, res) => {
  res.json({
    name: 'MEDORA Backend API',
    version: '1.0.0',
    description: 'AI-Powered Medical Assistant Backend',
    status: 'operational',
    environment: config.server.nodeEnv,
    timestamp: new Date().toISOString(),
    endpoints: {
      health: {
        '/health': 'Basic server health check',
        '/api/status': 'API status and features',
        '/api/health/database': 'Database connectivity status'
      },
      authentication: {
        'POST /api/auth/register': 'User registration',
        'POST /api/auth/login': 'User login',
        'GET /api/auth/me': 'Get current user (requires auth)',
        'POST /api/auth/logout': 'User logout'
      },
      ai: {
        'POST /api/ai/chat': 'AI chat conversation'
      },
      admin: {
        'GET /api/admin/settings': 'Get system settings (admin only)',
        'PUT /api/admin/settings': 'Update system settings (admin only)',
        'GET /api/admin/users': 'Get all users (admin only)'
      }
    },
    services: {
      database: {
        configured: config.isMongoConfigured(),
        connected: isMongoConnected,
        status: isMongoConnected ? 'connected' : 'disconnected'
      },
      ai: {
        openai_configured: config.isOpenAiConfigured()
      },
      payments: {
        paystack_configured: config.isPaystackConfigured(),
        flutterwave_configured: config.isFlutterwaveConfigured()
      }
    }
  });
});

// Middleware to ensure database connection for database-dependent endpoints
function requireDatabase(req, res, next) {
  if (!isMongoConnected) {
    // Try to reconnect if not connected
    if (config.isMongoConfigured() && connectionAttempts < MAX_CONNECTION_ATTEMPTS) {
      connectToMongoDB().then(connected => {
        if (connected) {
          next();
        } else {
          res.status(503).json({
            success: false,
            error: 'Database service unavailable',
            message: 'Database connection failed. Please try again later.',
            mode: 'demo'
          });
        }
      });
    } else {
      res.status(503).json({
        success: false,
        error: 'Database not configured',
        message: 'This endpoint requires database connectivity. Running in demo mode.',
        mode: 'demo'
      });
    }
  } else {
    next();
  }
}

// JWT-protected current user
function requireAuth(req, res, next) {
  try {
    const auth = req.headers.authorization || '';
    const token = auth.startsWith('Bearer ') ? auth.slice(7) : null;
    if (!token) return res.status(401).json({ success: false, error: 'Unauthorized' });
    const payload = jwt.verify(token, config.auth.jwtSecret);
    req.user = payload;
    next();
  } catch (e) {
    return res.status(401).json({ success: false, error: 'Unauthorized' });
  }
}

app.get('/api/auth/me', requireAuth, requireDatabase, async (req, res) => {
  try {
    const user = await User.findById(req.user.sub);
    if (!user) return res.status(404).json({ success: false, error: 'Not found' });
    return res.json({ success: true, data: sanitizeUser(user) });
  } catch (error) {
    console.error('Get user error:', error);
    return res.status(500).json({ success: false, error: 'Database error' });
  }
});

function requireRole(role) {
  return (req, res, next) => {
    if (!req.user) return res.status(401).json({ success: false, error: 'Unauthorized' });
    if (req.user.role !== role) return res.status(403).json({ success: false, error: 'Forbidden' });
    next();
  };
}

// Auth endpoints (MongoDB + JWT)
app.post('/api/auth/register', requireDatabase, async (req, res) => {
  try {
    const { email, password, firstName, lastName, role } = req.body || {};
    if (!email || !password || !firstName || !lastName) {
      return res.status(400).json({ success: false, status: 'error', message: 'Missing required fields' });
    }

    const existing = await User.findOne({ email });
    if (existing) return res.status(409).json({ success: false, status: 'error', message: 'Email already in use' });

    const hash = await bcrypt.hash(password, 12);
    const user = await User.create({
      email,
      password: hash,
      firstName,
      lastName,
      role: role || 'patient',
      isVerified: true,
      isActive: true
    });

    console.log('JWT_SECRET available:', !!config.auth.jwtSecret);
    const token = jwt.sign({ sub: user._id, role: user.role }, config.auth.jwtSecret, { expiresIn: config.auth.jwtExpire });
    res.json({ success: true, status: 'success', token, data: { user: sanitizeUser(user) } });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ success: false, status: 'error', message: 'Registration failed', error: error.message });
  }
});

app.post('/api/auth/login', requireDatabase, async (req, res) => {
  try {
    const { email, password } = req.body || {};
    if (!email || !password) {
      return res.status(400).json({ success: false, status: 'error', message: 'Email and password are required' });
    }

    const user = await User.findOne({ email }).select('+password');
    if (!user) return res.status(401).json({ success: false, status: 'error', message: 'Invalid credentials' });

    // Check if user account is active
    if (!user.isActive) {
      return res.status(401).json({ success: false, status: 'error', message: 'Account is deactivated' });
    }

    const ok = await bcrypt.compare(password, user.password);
    if (!ok) return res.status(401).json({ success: false, status: 'error', message: 'Invalid credentials' });

    user.lastLogin = new Date();
    await user.save();

    const token = jwt.sign({ sub: user._id, role: user.role }, config.auth.jwtSecret, { expiresIn: config.auth.jwtExpire });
    return res.json({ success: true, status: 'success', token, data: { user: sanitizeUser(user) } });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ success: false, status: 'error', message: 'Login failed' });
  }
});

// Seed default settings in MongoDB if not exists
(async () => {
  try {
    if (!isMongoConnected) {
      console.log('⚠️  Skipping settings seed - MongoDB not connected');
      return;
    }
    const existingSettings = await Setting.findOne();
    if (!existingSettings) {
      await Setting.create({
        siteName: 'MEDORA',
        siteDescription: 'AI-Powered Medical Assistant',
        timezone: 'UTC',
        language: 'en',
        maintenanceMode: false,
        sessionTimeout: 30,
        maxLoginAttempts: 5,
        passwordMinLength: 8,
        requireTwoFactor: false,
        enableAuditLog: true,
        smtpHost: 'smtp.gmail.com',
        smtpPort: 587,
        smtpUser: '',
        smtpPassword: '',
        fromEmail: '<EMAIL>',
        fromName: 'MEDORA System',
        aiConfidenceThreshold: 0.7,
        mlModelPath: '/models',
        enableAutoRetrain: true,
        maxConcurrentRequests: 100,
        dbBackupFrequency: 'daily',
        dbRetentionDays: 30,
        enableQueryLogging: false,
        emailNotifications: true,
        pushNotifications: true,
        slackWebhook: '',
        alertThreshold: 80
      });
      console.log('✅ Seeded default settings');
    }
  } catch (e) {
    console.warn('Settings seed skipped:', e.message);
  }
})();

// Admin settings endpoints (DB + RBAC)
app.get('/api/admin/settings', requireAuth, requireRole('admin'), async (req, res) => {
  try {
    const setting = await Setting.findOne().sort({ updatedAt: -1 });
    res.json({ success: true, data: setting || {} });
  } catch (e) {
    res.status(500).json({ success: false, error: 'Failed to fetch settings' });
  }
});

app.put('/api/admin/settings', requireAuth, requireRole('admin'), async (req, res) => {
  try {
    let setting = await Setting.findOne();
    if (!setting) {
      setting = new Setting(req.body);
    } else {
      Object.assign(setting, req.body);
    }
    await setting.save();
    res.json({ success: true, data: setting });
  } catch (e) {
    res.status(400).json({ success: false, error: 'Invalid settings' });
  }
});

// Admin users endpoints (DB + RBAC)
app.get('/api/admin/users', requireAuth, requireRole('admin'), async (req, res) => {
  try {
    const users = await User.find().sort({ createdAt: -1 }).select('-password');
    res.json({ success: true, data: users });
  } catch (e) {
    res.status(500).json({ success: false, error: 'Failed to fetch users' });
  }
});

app.post('/api/admin/users', requireAuth, requireRole('admin'), async (req, res) => {
  try {
    const { firstName, lastName, email, role = 'patient', password } = req.body || {};
    if (!email) return res.status(400).json({ success: false, error: 'Email is required' });

    const exists = await User.findOne({ email });
    if (exists) return res.status(409).json({ success: false, error: 'Email already exists' });

    const hash = await bcrypt.hash(password || 'Password123!', 12);
    const user = await User.create({
      firstName: firstName || '',
      lastName: lastName || '',
      email,
      role,
      password: hash,
      isVerified: true
    });

    res.json({ success: true, data: sanitizeUser(user) });
  } catch (e) {
    res.status(500).json({ success: false, error: 'Failed to create user' });
  }
});

app.put('/api/admin/users/:id/toggle-status', requireAuth, requireRole('admin'), async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findById(id);
    if (!user) return res.status(404).json({ success: false, error: 'User not found' });

    user.isActive = !user.isActive;
    await user.save();
    res.json({ success: true, data: sanitizeUser(user) });
  } catch (e) {
    res.status(500).json({ success: false, error: 'Failed to update user status' });
  }
});

app.delete('/api/admin/users/:id', requireAuth, requireRole('admin'), async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findByIdAndDelete(id);
    if (!user) return res.status(404).json({ success: false, error: 'User not found' });
    res.json({ success: true, message: 'User deleted successfully' });
  } catch (e) {
    res.status(500).json({ success: false, error: 'Failed to delete user' });
  }
});

// Payment logs and subscriptions (DB + RBAC)
app.get('/api/payments/logs', requireAuth, requireRole('admin'), async (req, res) => {
  const logs = await Payment.find().sort({ createdAt: -1 }).limit(50);
  res.json({ success: true, data: logs });
});
app.get('/api/payments/subscriptions', requireAuth, requireRole('admin'), async (req, res) => {
  const subs = await Subscription.find().sort({ updatedAt: -1 });
  const result = {};
  subs.forEach(s => { if (s.userEmail) result[s.userEmail] = s; });
  res.json({ success: true, data: result });
});

// AI Chat endpoint with OpenAI integration
app.post('/api/ai/chat', async (req, res) => {
  try {
    console.log('AI Chat request:', req.body);

    if (!config.isOpenAiConfigured()) {
      return res.status(400).json({
        success: false,
        status: 'error',
        message: 'OpenAI API key not configured'
      });
    }

    const { message, conversationId, isVoiceInput } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        status: 'error',
        message: 'Message is required'
      });
    }

    // For now, return a demo response
    // TODO: Integrate with actual OpenAI API

    // Voice-specific responses (more conversational)
    const voiceResponses = [
      "I'm listening. Please tell me about your symptoms and I'll do my best to help you.",
      "Thank you for sharing that with me. Can you describe when these symptoms first started?",
      "I understand your concern. Let me ask you a few questions to better understand your situation.",
      "That sounds concerning. Have you experienced anything like this before?",
      "I appreciate you taking the time to speak with me. Can you tell me more about how you're feeling?",
      "Based on what you're telling me, I'd like to gather a bit more information. How long have you been experiencing this?",
      "I'm here to help you. Can you describe the intensity of what you're experiencing on a scale of one to ten?",
      "Thank you for that information. Are there any other symptoms you've noticed recently?"
    ];

    // Text-specific responses (more formal)
    const textResponses = [
      "Hello! I'm MEDORA, your AI medical assistant. How can I help you today?",
      "I understand your concern. Can you tell me more about your symptoms?",
      "Based on what you've described, I'd recommend consulting with a healthcare professional for a proper evaluation.",
      "That's a good question. Let me provide you with some general medical information about that topic.",
      "I'm here to help with medical information and guidance. What specific health concern would you like to discuss?"
    ];

    const responses = isVoiceInput ? voiceResponses : textResponses;
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];

    const response = {
      success: true,
      status: 'success',
      data: {
        response: randomResponse,
        conversationId: conversationId || 'demo-conversation-' + Date.now(),
        timestamp: new Date().toISOString(),
        messageId: 'msg-' + Date.now()
      }
    };

    console.log('📤 Sending AI response:', response);
    res.json(response);

  } catch (error) {
    console.error('AI endpoint error:', error);
    res.status(500).json({
      success: false,
      status: 'error',
      message: 'AI service error',
      error: error.message
    });
  }
});

// Logout endpoint
app.post('/api/auth/logout', (req, res) => {
  return res.json({ success: true, message: 'Logged out successfully' });
});

// OAuth routes
const oauthRoutes = require('./routes/oauth');
app.use('/api/auth', oauthRoutes);
// Patient management endpoints
app.get('/api/patients', requireAuth, async (req, res) => {
  try {
    const patients = await User.find({ role: 'patient' }).select('-password').sort({ createdAt: -1 });
    res.json({ success: true, data: patients });
  } catch (e) {
    res.status(500).json({ success: false, error: 'Failed to fetch patients' });
  }
});

app.get('/api/patients/:id', requireAuth, async (req, res) => {
  try {
    const patient = await User.findById(req.params.id).select('-password');
    if (!patient) return res.status(404).json({ success: false, error: 'Patient not found' });
    res.json({ success: true, data: patient });
  } catch (e) {
    res.status(500).json({ success: false, error: 'Failed to fetch patient' });
  }
});

// Diagnosis endpoints
app.post('/api/diagnosis', requireAuth, async (req, res) => {
  try {
    const { symptoms, patientId } = req.body;
    // This would integrate with AI/ML services for actual diagnosis
    const mockDiagnosis = {
      id: Date.now().toString(),
      symptoms,
      patientId,
      suggestions: ['Consult with a healthcare professional', 'Monitor symptoms'],
      confidence: 0.75,
      createdAt: new Date().toISOString()
    };
    res.json({ success: true, data: mockDiagnosis });
  } catch (e) {
    res.status(500).json({ success: false, error: 'Diagnosis failed' });
  }
});

// ML Analysis endpoints
app.post('/api/ml/analyze', requireAuth, async (req, res) => {
  try {
    const { data, analysisType } = req.body;
    // This would integrate with actual ML models
    const mockAnalysis = {
      id: Date.now().toString(),
      analysisType,
      results: { confidence: 0.85, predictions: ['Normal', 'Monitor'] },
      createdAt: new Date().toISOString()
    };
    res.json({ success: true, data: mockAnalysis });
  } catch (e) {
    res.status(500).json({ success: false, error: 'ML analysis failed' });
  }
});

// Payments integration (Paystack and Flutterwave)
const FRONTEND_URL = config.server.frontendUrl;

// INITIATE Paystack transaction
app.post('/api/payments/paystack/initiate', async (req, res) => {
  try {
    const { email, amount, planName, cycle, currency = 'NGN' } = req.body || {};
    if (!config.isPaystackConfigured()) {
      return res.status(400).json({ success: false, error: 'Missing PAYSTACK_SECRET_KEY' });
    }
    if (!email || !amount) {
      return res.status(400).json({ success: false, error: 'email and amount are required' });
    }
    const initPayload = {
      email,
      amount: Math.round(Number(amount) * 100), // kobo
      currency,
      callback_url: `${FRONTEND_URL}/payment/callback?provider=paystack&planName=${encodeURIComponent(planName||'')}&cycle=${encodeURIComponent(cycle||'')}&amount=${encodeURIComponent(amount)}`,
      metadata: { planName, cycle }
    };
    const resp = await fetch('https://api.paystack.co/transaction/initialize', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.payments.paystack.secretKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(initPayload)
    });
    const data = await resp.json();
    if (!data.status) {
      return res.status(400).json({ success: false, error: data.message || 'Paystack init failed' });
    }
    return res.json({ success: true, data: { authorization_url: data.data.authorization_url, reference: data.data.reference } });
  } catch (e) {
    console.error('Paystack init error:', e);
    return res.status(500).json({ success: false, error: 'Paystack init error' });
  }
});

// VERIFY Paystack transaction
app.get('/api/payments/paystack/verify', async (req, res) => {
  try {
    const { reference } = req.query;
    if (!config.isPaystackConfigured()) {
      return res.status(400).json({ success: false, error: 'Missing PAYSTACK_SECRET_KEY' });
    }
    if (!reference) {
      return res.status(400).json({ success: false, error: 'reference is required' });
    }
    const resp = await fetch(`https://api.paystack.co/transaction/verify/${reference}`, {
      headers: { 'Authorization': `Bearer ${config.payments.paystack.secretKey}` }
    });
    const data = await resp.json();
    if (!data.status) {
      return res.status(400).json({ success: false, error: data.message || 'Paystack verify failed' });
    }
    return res.json({ success: true, data: data.data });
  } catch (e) {
    console.error('Paystack verify error:', e);
    return res.status(500).json({ success: false, error: 'Paystack verify error' });
  }
});

// INITIATE Flutterwave payment
app.post('/api/payments/flutterwave/initiate', async (req, res) => {
  try {
    const { email, amount, planName, cycle, currency = 'NGN' } = req.body || {};
    if (!config.isFlutterwaveConfigured()) {
      return res.status(400).json({ success: false, error: 'Missing FLW_SECRET_KEY' });
    }
    if (!email || !amount) {
      return res.status(400).json({ success: false, error: 'email and amount are required' });
    }
    const txRef = `flw_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`;
    const initPayload = {
      tx_ref: txRef,
      amount: Number(amount),
      currency,
      redirect_url: `${FRONTEND_URL}/payment/callback?provider=flutterwave`,
      customer: { email },
      meta: { planName, cycle }
    };
    const resp = await fetch('https://api.flutterwave.com/v3/payments', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.payments.flutterwave.secretKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(initPayload)
    });
    const data = await resp.json();
    if (data.status !== 'success') {
      return res.status(400).json({ success: false, error: data.message || 'Flutterwave init failed' });
    }
    return res.json({ success: true, data: { link: data.data.link, tx_ref: txRef } });
  } catch (e) {
    console.error('Flutterwave init error:', e);
    return res.status(500).json({ success: false, error: 'Flutterwave init error' });
  }
});

// VERIFY Flutterwave transaction by transaction_id
app.get('/api/payments/flutterwave/verify', async (req, res) => {
  try {
    const { transaction_id } = req.query;
    if (!config.isFlutterwaveConfigured()) {
      return res.status(400).json({ success: false, error: 'Missing FLW_SECRET_KEY' });
    }
    if (!transaction_id) {
      return res.status(400).json({ success: false, error: 'transaction_id is required' });
    }
    const resp = await fetch(`https://api.flutterwave.com/v3/transactions/${transaction_id}/verify`, {
      headers: { 'Authorization': `Bearer ${config.payments.flutterwave.secretKey}` }
    });
    const data = await resp.json();
// Webhooks
// Paystack webhook: uses 'x-paystack-signature' HMAC SHA512 with PAYSTACK_SECRET_KEY
app.post('/api/payments/paystack/webhook', async (req, res) => {
  try {
    const secret = config.payments.paystack.secretKey;
    if (!secret) return res.status(400).json({ success: false, error: 'Missing PAYSTACK_SECRET_KEY' });

    const signature = req.headers['x-paystack-signature'];
    const expected = crypto.createHmac('sha512', secret).update(req.rawBody).digest('hex');
    if (signature !== expected) {
      console.warn('Invalid Paystack webhook signature');
      return res.status(401).json({ success: false });
    }

    const event = req.body; // already parsed
    const data = event?.data || {};

    // Store payment event in MongoDB
    await Payment.create({
      provider: 'paystack',
      status: event?.event,
      email: data.customer?.email,
      amount: data.amount,
      currency: data.currency,
      reference: data.reference,
      raw: event
    });

    if (event?.event === 'charge.success') {
      const email = data.customer?.email;
      if (email) {
        // Update or create subscription in MongoDB
        await Subscription.findOneAndUpdate(
          { userEmail: email },
          {
            userEmail: email,
            planName: data.metadata?.planName,
            cycle: data.metadata?.cycle,
            status: 'active',
            provider: 'paystack',
            reference: data.reference
          },
          { upsert: true, new: true }
        );
      }
    }

    return res.json({ success: true });
  } catch (e) {
    console.error('Paystack webhook error:', e);
    return res.status(500).json({ success: false });
  }
});

// Flutterwave webhook: verify via hash in 'verif-hash' header using FLW_SECRET_HASH if set, else FLW_SECRET_KEY
app.post('/api/payments/flutterwave/webhook', async (req, res) => {
  try {
    const secret = config.payments.flutterwave.secretHash || config.payments.flutterwave.secretKey;
    if (!secret) return res.status(400).json({ success: false, error: 'Missing FLW secret' });

    const signature = req.headers['verif-hash'];
    if (!signature || signature !== secret) {
      console.warn('Invalid Flutterwave webhook signature');
      return res.status(401).json({ success: false });
    }

    const event = req.body;
    const data = event?.data || {};

    // Store payment event in MongoDB
    await Payment.create({
      provider: 'flutterwave',
      status: event?.event || data.status,
      email: data.customer?.email,
      amount: data.amount,
      currency: data.currency,
      tx_ref: data.tx_ref,
      raw: event
    });

    if (data.status === 'successful' || data.status === 'success') {
      const email = data.customer?.email;
      if (email) {
        // Update or create subscription in MongoDB
        await Subscription.findOneAndUpdate(
          { userEmail: email },
          {
            userEmail: email,
            planName: data.meta?.planName,
            cycle: data.meta?.cycle,
            status: 'active',
            provider: 'flutterwave',
            reference: data.tx_ref
          },
          { upsert: true, new: true }
        );
      }
    }

    return res.json({ success: true });
  } catch (e) {
    console.error('Flutterwave webhook error:', e);
    return res.status(500).json({ success: false });
  }
});

// Remove duplicate endpoints - already handled above with RBAC

    if (data.status !== 'success') {
      return res.status(400).json({ success: false, error: data.message || 'Flutterwave verify failed' });
    }
    return res.json({ success: true, data: data.data });
  } catch (e) {
    console.error('Flutterwave verify error:', e);
    return res.status(500).json({ success: false, error: 'Flutterwave verify error' });
  }
});

// Catch all for undefined routes
app.use('*', (req, res) => {
  res.status(404).json({
    status: 'error',
    message: `Route ${req.originalUrl} not found`,
    note: 'This is a simplified server. Full functionality requires MongoDB.'
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({
    status: 'error',
    message: 'Internal server error',
    error: config.server.isDevelopment ? error.message : 'Something went wrong'
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully');
  process.exit(0);
});

// Start server
const PORT = config.server.port;
app.listen(PORT, () => {
  console.log(` MEDORA Backend Server running on port ${PORT}`);
  console.log(` Environment: ${config.server.nodeEnv}`);
  console.log(` Frontend URL: ${FRONTEND_URL}`);
  console.log(` OpenAI configured: ${config.isOpenAiConfigured()}`);
  console.log(` Paystack configured: ${config.isPaystackConfigured()}`);
  console.log(` Flutterwave configured: ${config.isFlutterwaveConfigured()}`);
  console.log(` Admin seeded: <EMAIL>`);
  console.log(` System ready for production use!`);
});