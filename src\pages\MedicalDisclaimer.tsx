import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/card';
import { ArrowLeft, AlertTriangle, Phone, Clock, Shield, Brain, Stethoscope } from 'lucide-react';

const MedicalDisclaimer = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/')}
              className="text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
            <div className="flex items-center space-x-3">
              <img 
                src="/lovable-uploads/logo-png.png" 
                alt="MEDORA Logo" 
                className="h-8 w-8 object-contain"
              />
              <span className="text-xl font-bold" style={{color: '#9ACD32'}}>MEDORA</span>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12 max-w-4xl">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-4xl font-bold mb-4">Medical Disclaimer</h1>
          <p className="text-xl text-muted-foreground">
            Important information about MEDORA's AI medical assistance
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Please read this disclaimer carefully before using our services
          </p>
        </div>

        {/* Emergency Notice */}
        <div className="mb-8">
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-6">
              <div className="flex items-start space-x-4">
                <Phone className="h-8 w-8 text-red-600 mt-1" />
                <div>
                  <h3 className="text-xl font-bold text-red-800 mb-2">Medical Emergency</h3>
                  <p className="text-red-700 mb-3">
                    If you are experiencing a medical emergency, do not use MEDORA. Call emergency services immediately:
                  </p>
                  <div className="space-y-1 text-red-700 font-semibold">
                    <p>🚨 Emergency: 911 (US) | 999 (UK) | 112 (EU)</p>
                    <p>☎️ Poison Control: ************** (US)</p>
                    <p>🏥 Go to your nearest emergency room</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-8">
          {/* Primary Disclaimer */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
                Primary Medical Disclaimer
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="font-bold text-yellow-800 mb-2">IMPORTANT: NOT A SUBSTITUTE FOR PROFESSIONAL MEDICAL CARE</h4>
                <p className="text-yellow-700">
                  MEDORA is an artificial intelligence-powered medical assistance platform designed to provide general health information and support. It is NOT a substitute for professional medical advice, diagnosis, or treatment from qualified healthcare providers.
                </p>
              </div>
              <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                <li>Always seek the advice of your physician or other qualified health provider with any questions you may have regarding a medical condition</li>
                <li>Never disregard professional medical advice or delay in seeking it because of something you have read or received from MEDORA</li>
                <li>If you think you may have a medical emergency, call your doctor or emergency services immediately</li>
                <li>MEDORA does not recommend or endorse any specific tests, physicians, products, procedures, opinions, or other information</li>
              </ul>
            </CardContent>
          </Card>

          {/* AI Limitations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Brain className="h-5 w-5 mr-2" />
                AI Technology Limitations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                While MEDORA uses advanced artificial intelligence algorithms, it has inherent limitations:
              </p>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2 text-red-600">Limitations</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground text-sm">
                    <li>Cannot perform physical examinations</li>
                    <li>Cannot order or interpret diagnostic tests</li>
                    <li>May not account for all medical factors</li>
                    <li>Cannot replace clinical judgment</li>
                    <li>May have knowledge gaps or errors</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2 text-green-600">Capabilities</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground text-sm">
                    <li>Provides general health information</li>
                    <li>Offers symptom analysis guidance</li>
                    <li>Suggests when to seek medical care</li>
                    <li>Helps organize health information</li>
                    <li>Facilitates healthcare communication</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Accuracy and Reliability */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Accuracy and Reliability
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                While MEDORA strives for accuracy, we cannot guarantee that all information provided is complete, accurate, or up-to-date:
              </p>
              <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                <li>Medical knowledge is constantly evolving</li>
                <li>Individual medical conditions vary significantly</li>
                <li>AI algorithms may have biases or limitations</li>
                <li>Information may not reflect the latest medical research</li>
                <li>System errors or technical issues may occur</li>
              </ul>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                <p className="text-blue-700 text-sm">
                  <strong>Accuracy Rate:</strong> While our AI has demonstrated high accuracy in testing environments, real-world performance may vary. Always verify important medical information with healthcare professionals.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Scope of Service */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Stethoscope className="h-5 w-5 mr-2" />
                Scope of Service
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">What MEDORA Can Help With:</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>General health education and information</li>
                  <li>Symptom tracking and organization</li>
                  <li>Medication reminders and information</li>
                  <li>Health goal setting and monitoring</li>
                  <li>Communication with healthcare providers</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">What MEDORA Cannot Do:</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>Provide medical diagnoses</li>
                  <li>Prescribe medications</li>
                  <li>Replace emergency medical services</li>
                  <li>Perform medical procedures</li>
                  <li>Guarantee treatment outcomes</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* User Responsibilities */}
          <Card>
            <CardHeader>
              <CardTitle>Your Responsibilities</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                As a user of MEDORA, you are responsible for:
              </p>
              <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                <li>Using the service appropriately and not for emergency situations</li>
                <li>Providing accurate information to the best of your ability</li>
                <li>Consulting with healthcare professionals for medical decisions</li>
                <li>Understanding the limitations of AI technology</li>
                <li>Seeking immediate medical attention when necessary</li>
                <li>Not sharing your account or medical information inappropriately</li>
              </ul>
            </CardContent>
          </Card>

          {/* Liability Limitation */}
          <Card>
            <CardHeader>
              <CardTitle>Limitation of Liability</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <p className="text-gray-700 text-sm">
                  <strong>Legal Notice:</strong> To the maximum extent permitted by law, MEDORA and its affiliates disclaim all liability for any damages arising from the use of this service, including but not limited to medical decisions made based on AI recommendations.
                </p>
              </div>
              <p>
                Users assume full responsibility for any actions taken based on information provided by MEDORA. We strongly recommend consulting with qualified healthcare professionals before making any medical decisions.
              </p>
            </CardContent>
          </Card>

          {/* When to Seek Medical Care */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                When to Seek Immediate Medical Care
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="font-semibold">Seek immediate medical attention if you experience:</p>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2 text-red-600">Emergency Symptoms</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground text-sm">
                    <li>Chest pain or pressure</li>
                    <li>Difficulty breathing</li>
                    <li>Severe bleeding</li>
                    <li>Loss of consciousness</li>
                    <li>Severe allergic reactions</li>
                    <li>Signs of stroke</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2 text-orange-600">Urgent Symptoms</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground text-sm">
                    <li>High fever (&gt;103°F/39.4°C)</li>
                    <li>Severe pain</li>
                    <li>Persistent vomiting</li>
                    <li>Signs of infection</li>
                    <li>Mental health crises</li>
                    <li>Medication reactions</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Questions About This Disclaimer</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                If you have questions about this medical disclaimer or MEDORA's services, please contact us:
              </p>
              <div className="space-y-2 text-muted-foreground">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Phone:</strong> +****************</p>
                <p><strong>Address:</strong> MEDORA Medical Affairs, 123 Healthcare Blvd, Medical City, MC 12345</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default MedicalDisclaimer;
