# MongoDB Connection Troubleshooting Guide

## 🚨 Current Issue
**Authentication Failed**: The MongoDB connection is failing with "bad auth : authentication failed"

## 📋 Your Current Configuration
```
URI: mongodb+srv://medora:<EMAIL>/medora
Database: medora
Cluster: medora.rauhtwy.mongodb.net
```

## 🔧 Troubleshooting Steps

### 1. Verify MongoDB Atlas Setup
1. **Login to MongoDB Atlas**: https://cloud.mongodb.com/
2. **Check Database User**:
   - Go to "Database Access" in your project
   - Verify user "medora" exists
   - Confirm password is exactly: `Americana123456789`
   - Ensure user has proper permissions (readWrite or Atlas admin)

### 2. Check Network Access
1. **Go to "Network Access"** in MongoDB Atlas
2. **Verify IP Whitelist**:
   - Add `0.0.0.0/0` for testing (allows all IPs)
   - Or add your specific IP address
   - For production, use specific IP ranges

### 3. Verify Cluster Information
1. **Go to "Clusters"** in MongoDB Atlas
2. **Click "Connect"** on your cluster
3. **Choose "Connect your application"**
4. **Copy the connection string** and compare with yours

### 4. Test Connection String Format
The correct format should be:
```
mongodb+srv://<username>:<password>@<cluster-url>/<database>?retryWrites=true&w=majority
```

### 5. Common Issues & Solutions

#### Issue: Special Characters in Password
- If password contains special characters, they need URL encoding
- `@` becomes `%40`
- `#` becomes `%23`
- `$` becomes `%24`

#### Issue: Wrong Database Name
- Ensure the database name in the URI matches your actual database
- Default is usually the cluster name or "test"

#### Issue: User Permissions
- User must have at least "readWrite" role for the specific database
- Or "Atlas admin" for full access

### 6. Alternative Connection Methods

#### Method 1: Test with MongoDB Compass
1. Download MongoDB Compass
2. Use the same connection string
3. See if it connects successfully

#### Method 2: Test with mongo shell
```bash
mongosh "mongodb+srv://medora:<EMAIL>/medora"
```

### 7. Updated Test Script
Create a more detailed test with better error handling:

```javascript
const mongoose = require('mongoose');

const MONGODB_URI = 'mongodb+srv://medora:<EMAIL>/medora?retryWrites=true&w=majority';

async function testConnection() {
  try {
    console.log('🔗 Testing MongoDB Atlas connection...');
    console.log('Cluster:', 'medora.rauhtwy.mongodb.net');
    console.log('Database:', 'medora');
    console.log('User:', 'medora');
    
    await mongoose.connect(MONGODB_URI, {
      serverSelectionTimeoutMS: 10000, // 10 seconds
      socketTimeoutMS: 45000, // 45 seconds
    });
    
    console.log('✅ Successfully connected to MongoDB Atlas!');
    
    // Test database operations
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    console.log('📁 Available collections:', collections.map(c => c.name));
    
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('Error code:', error.code);
    
    if (error.code === 8000) {
      console.log('\n🔍 Authentication Error Troubleshooting:');
      console.log('1. Check username and password in MongoDB Atlas');
      console.log('2. Verify user has proper database permissions');
      console.log('3. Ensure IP address is whitelisted');
    }
  }
}

testConnection();
```

## 🎯 Next Steps

1. **Verify Atlas Setup**: Check user credentials and permissions
2. **Test Network Access**: Ensure IP is whitelisted
3. **Get Correct URI**: Copy connection string directly from Atlas
4. **Update Configuration**: Use the verified connection string

## 📞 Need Help?
If issues persist:
1. Share screenshot of MongoDB Atlas Database Access page
2. Share screenshot of Network Access page
3. Verify the exact cluster connection string from Atlas
