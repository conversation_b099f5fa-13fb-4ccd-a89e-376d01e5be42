import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Send, Mic, MicOff, Volume2, VolumeX } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { aiService } from '@/services/aiService';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/useAuth';
import { creditsService } from '@/services/creditsService';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

interface ChatInterfaceProps {
  className?: string;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({ className = '' }) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hello! I\'m <PERSON><PERSON><PERSON><PERSON>, your AI medical assistant. How can I help you today?',
      sender: 'ai',
      timestamp: new Date()
    }
  ]);
  const [currentMessage, setCurrentMessage] = useState('');
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [conversationId] = useState(`chat_${Date.now()}`);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const stopListeningRef = useRef<(() => void) | null>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = async () => {
    if (!currentMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: currentMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const messageText = currentMessage;
    setCurrentMessage('');
    setIsLoading(true);

    try {
      // Spend 1 credit per message (demo). If insufficient credits, block send.
      if (user?._id) {
        const spend = creditsService.spend(user._id, 1);
        if (!spend.success) {
          toast.error('You have run out of AI credits. Please upgrade your plan.');
          setIsLoading(false);
          return;
        }
      }

      const response = await aiService.sendChatMessage(messageText, conversationId);
      
      if (response.success && response.data) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: response.data.response,
          sender: 'ai',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, aiMessage]);

        // Speak response if not muted
        if (!isMuted) {
          try {
            await aiService.speak(response.data.response);
          } catch (error) {
            console.error('Speech synthesis error:', error);
          }
        }
      } else {
        throw new Error(response.error || 'Failed to get AI response');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message. Please try again.');
      
      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        text: "I'm sorry, I'm having trouble connecting right now. Please try again.",
        sender: 'ai',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleVoiceInput = () => {
    if (isListening) {
      stopListeningRef.current?.();
      setIsListening(false);
    } else {
      try {
        const stopListening = aiService.startListening(
          (transcript, isFinal) => {
            if (isFinal && transcript.trim()) {
              setCurrentMessage(transcript);
              stopListeningRef.current?.();
              setIsListening(false);
              // Auto-send voice message
              setTimeout(() => sendMessage(), 100);
            }
          },
          (error) => {
            console.error('Speech recognition error:', error);
            toast.error('Voice recognition failed. Please try again.');
            setIsListening(false);
          }
        );
        
        stopListeningRef.current = stopListening;
        setIsListening(true);
      } catch (error) {
        console.error('Failed to start voice recognition:', error);
        toast.error('Voice recognition not supported or failed to start.');
      }
    }
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    if (!isMuted) {
      // Stop any ongoing speech
      window.speechSynthesis?.cancel();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <Card className={`flex flex-col h-full neu-inset ${className}`}>
      <CardHeader className="border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <img 
              src="/lovable-uploads/047b7b2a-050d-44d4-8fe0-3bc138ac9ed7.png" 
              alt="MEDORA AI" 
              className="w-8 h-8 rounded-full"
            />
            <div>
              <h3 className="text-lg font-semibold text-foreground">MEDORA AI Assistant</h3>
              <p className="text-xs text-muted-foreground">Medical consultation chat</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMute}
              className={isMuted ? 'text-red-500' : 'text-muted-foreground'}
            >
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] px-4 py-2 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-green-500 text-white neu-raised'
                      : 'bg-card border border-border neu-inset'
                  }`}
                >
                  <p className="text-sm">{message.text}</p>
                  <p className="text-xs opacity-70 mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-card border border-border rounded-lg px-4 py-2 neu-inset">
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce delay-100"></div>
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce delay-200"></div>
                    </div>
                    <span className="text-xs text-muted-foreground">MEDORA is thinking...</span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        <div className="border-t border-border p-4">
          <div className="flex space-x-2">
            <Input
              value={currentMessage}
              onChange={(e) => setCurrentMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={isListening ? "Listening..." : "Type your medical question..."}
              className="flex-1 neu-inset"
              disabled={isLoading || isListening}
            />
            
            <Button
              onClick={toggleVoiceInput}
              variant="outline"
              size="icon"
              className={`neu-raised ${
                isListening 
                  ? 'bg-red-500 text-white border-red-500 animate-pulse' 
                  : 'hover:bg-green-500/20 hover:border-green-500'
              }`}
              disabled={isLoading}
            >
              {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
            </Button>
            
            <Button
              onClick={sendMessage}
              disabled={!currentMessage.trim() || isLoading || isListening}
              className="neu-raised bg-green-500 hover:bg-green-600 text-white"
              size="icon"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          
          {isListening && (
            <div className="mt-2 text-center">
              <p className="text-xs text-muted-foreground animate-pulse">
                🎤 Listening for your voice input...
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ChatInterface;
