const mongoose = require('mongoose');

const symptomSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  severity: {
    type: String,
    enum: ['mild', 'moderate', 'severe'],
    required: true,
  },
  duration: {
    value: Number,
    unit: {
      type: String,
      enum: ['minutes', 'hours', 'days', 'weeks', 'months', 'years'],
    },
  },
  frequency: {
    type: String,
    enum: ['constant', 'intermittent', 'occasional', 'rare'],
  },
  location: String,
  description: String,
  triggers: [String],
  relievingFactors: [String],
  associatedSymptoms: [String],
});

const differentialDiagnosisSchema = new mongoose.Schema({
  condition: {
    type: String,
    required: true,
  },
  icdCode: String,
  probability: {
    type: Number,
    min: 0,
    max: 100,
    required: true,
  },
  confidence: {
    type: String,
    enum: ['low', 'medium', 'high'],
    required: true,
  },
  supportingEvidence: [String],
  contradictingEvidence: [String],
  recommendedTests: [String],
  urgency: {
    type: String,
    enum: ['low', 'medium', 'high', 'emergency'],
    default: 'medium',
  },
});

const aiAnalysisSchema = new mongoose.Schema({
  modelUsed: {
    type: String,
    required: true,
  },
  modelVersion: String,
  analysisType: {
    type: String,
    enum: ['symptom_analysis', 'image_analysis', 'lab_analysis', 'comprehensive'],
    required: true,
  },
  inputData: {
    symptoms: [symptomSchema],
    vitalSigns: mongoose.Schema.Types.Mixed,
    labResults: mongoose.Schema.Types.Mixed,
    imageData: [{
      type: String,
      url: String,
      analysisResults: mongoose.Schema.Types.Mixed,
    }],
    patientHistory: mongoose.Schema.Types.Mixed,
  },
  predictions: [differentialDiagnosisSchema],
  riskAssessment: {
    overallRisk: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
    },
    riskFactors: [{
      factor: String,
      impact: {
        type: String,
        enum: ['low', 'medium', 'high'],
      },
      description: String,
    }],
  },
  recommendations: {
    immediateActions: [String],
    diagnosticTests: [{
      test: String,
      priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
      },
      reasoning: String,
    }],
    referrals: [{
      specialty: String,
      urgency: {
        type: String,
        enum: ['routine', 'urgent', 'emergency'],
      },
      reason: String,
    }],
    followUp: {
      timeframe: String,
      instructions: String,
    },
  },
  confidence: {
    type: Number,
    min: 0,
    max: 100,
  },
  processingTime: Number, // milliseconds
  dataQuality: {
    completeness: Number, // percentage
    reliability: Number, // percentage
    issues: [String],
  },
});

const treatmentPlanSchema = new mongoose.Schema({
  medications: [{
    name: String,
    dosage: String,
    frequency: String,
    duration: String,
    instructions: String,
    sideEffects: [String],
    contraindications: [String],
  }],
  procedures: [{
    name: String,
    description: String,
    scheduledDate: Date,
    urgency: {
      type: String,
      enum: ['routine', 'urgent', 'emergency'],
    },
    prerequisites: [String],
  }],
  lifestyle: {
    dietRecommendations: [String],
    exerciseRecommendations: [String],
    restrictions: [String],
    modifications: [String],
  },
  monitoring: {
    parameters: [String],
    frequency: String,
    duration: String,
    alerts: [String],
  },
});

const diagnosisSchema = new mongoose.Schema({
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
  },
  diagnosingPhysician: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  
  // Chief Complaint
  chiefComplaint: {
    type: String,
    required: true,
  },
  
  // History of Present Illness
  historyOfPresentIllness: String,
  
  // Symptoms
  symptoms: [symptomSchema],
  
  // Physical Examination
  physicalExamination: {
    general: String,
    vitalSigns: {
      temperature: String,
      bloodPressure: String,
      heartRate: String,
      respiratoryRate: String,
      oxygenSaturation: String,
    },
    systemicExamination: {
      cardiovascular: String,
      respiratory: String,
      gastrointestinal: String,
      neurological: String,
      musculoskeletal: String,
      dermatological: String,
      other: String,
    },
  },
  
  // Diagnostic Tests
  diagnosticTests: [{
    testName: String,
    testDate: Date,
    results: String,
    interpretation: String,
    attachments: [{
      filename: String,
      url: String,
      type: String,
    }],
  }],
  
  // AI Analysis
  aiAnalysis: aiAnalysisSchema,
  
  // Differential Diagnoses
  differentialDiagnoses: [differentialDiagnosisSchema],
  
  // Primary Diagnosis
  primaryDiagnosis: {
    condition: {
      type: String,
      required: true,
    },
    icdCode: String,
    confidence: {
      type: String,
      enum: ['low', 'medium', 'high'],
      required: true,
    },
    severity: {
      type: String,
      enum: ['mild', 'moderate', 'severe', 'critical'],
    },
    stage: String,
    prognosis: String,
  },
  
  // Secondary Diagnoses
  secondaryDiagnoses: [{
    condition: String,
    icdCode: String,
    relationship: String, // how it relates to primary diagnosis
  }],
  
  // Treatment Plan
  treatmentPlan: treatmentPlanSchema,
  
  // Follow-up
  followUp: {
    required: {
      type: Boolean,
      default: true,
    },
    timeframe: String,
    instructions: String,
    scheduledDate: Date,
    assignedTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  
  // Status
  status: {
    type: String,
    enum: ['preliminary', 'confirmed', 'revised', 'resolved'],
    default: 'preliminary',
  },
  
  // Priority
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'emergency'],
    default: 'medium',
  },
  
  // Notes
  clinicalNotes: String,
  
  // Quality Metrics
  qualityMetrics: {
    completeness: Number, // percentage
    accuracy: Number, // percentage
    timeliness: Number, // hours from symptom onset to diagnosis
    reviewRequired: Boolean,
    reviewedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    reviewedAt: Date,
    reviewNotes: String,
  },
  
  // Outcome
  outcome: {
    status: {
      type: String,
      enum: ['ongoing', 'improved', 'resolved', 'worsened', 'stable'],
    },
    followUpCompleted: Boolean,
    patientSatisfaction: {
      type: Number,
      min: 1,
      max: 5,
    },
    treatmentEffectiveness: {
      type: String,
      enum: ['poor', 'fair', 'good', 'excellent'],
    },
    complications: [String],
    notes: String,
  },
  
  // Metadata
  diagnosisDate: {
    type: Date,
    default: Date.now,
  },
  lastModified: {
    type: Date,
    default: Date.now,
  },
  modifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  
  // Flags
  flags: [{
    type: {
      type: String,
      enum: ['urgent', 'complex', 'rare_disease', 'second_opinion', 'research'],
    },
    description: String,
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    addedAt: {
      type: Date,
      default: Date.now,
    },
  }],
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
diagnosisSchema.index({ patient: 1 });
diagnosisSchema.index({ diagnosingPhysician: 1 });
diagnosisSchema.index({ diagnosisDate: -1 });
diagnosisSchema.index({ status: 1 });
diagnosisSchema.index({ priority: 1 });
diagnosisSchema.index({ 'primaryDiagnosis.condition': 1 });
diagnosisSchema.index({ 'primaryDiagnosis.icdCode': 1 });
diagnosisSchema.index({ 'followUp.scheduledDate': 1 });

// Virtual for diagnosis age
diagnosisSchema.virtual('diagnosisAge').get(function() {
  const now = new Date();
  const diagnosisDate = new Date(this.diagnosisDate);
  const diffTime = Math.abs(now - diagnosisDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Virtual for urgency score
diagnosisSchema.virtual('urgencyScore').get(function() {
  const priorityScores = { low: 1, medium: 2, high: 3, emergency: 4 };
  const severityScores = { mild: 1, moderate: 2, severe: 3, critical: 4 };
  
  const priorityScore = priorityScores[this.priority] || 2;
  const severityScore = severityScores[this.primaryDiagnosis?.severity] || 2;
  
  return (priorityScore + severityScore) / 2;
});

// Pre-save middleware
diagnosisSchema.pre('save', function(next) {
  this.lastModified = new Date();
  next();
});

// Methods
diagnosisSchema.methods.addFollowUpNote = function(note, userId) {
  if (!this.followUp.notes) {
    this.followUp.notes = [];
  }
  this.followUp.notes.push({
    note,
    addedBy: userId,
    addedAt: new Date(),
  });
  return this.save();
};

diagnosisSchema.methods.updateStatus = function(newStatus, userId) {
  this.status = newStatus;
  this.modifiedBy = userId;
  this.lastModified = new Date();
  return this.save();
};

module.exports = mongoose.model('Diagnosis', diagnosisSchema);