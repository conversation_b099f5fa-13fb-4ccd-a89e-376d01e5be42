import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { socialAuthService } from '@/services/socialAuthService';
import { toast } from 'sonner';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';

const AuthCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { setUser, setToken } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing authentication...');

  useEffect(() => {
    const handleOAuthCallback = async () => {
      try {
        const token = searchParams.get('token');
        const userParam = searchParams.get('user');
        const error = searchParams.get('message');

        // Get OAuth session info
        const oauthSession = socialAuthService.getOAuthSession();
        const mode = oauthSession.mode || 'login';
        const provider = oauthSession.provider || 'unknown';

        if (error) {
          setStatus('error');
          setMessage(error);
          toast.error(`${provider} authentication failed: ${error}`);
          socialAuthService.clearOAuthSession();
          setTimeout(() => navigate('/login'), 3000);
          return;
        }

        if (!token || !userParam) {
          setStatus('error');
          setMessage('Missing authentication data');
          toast.error('Authentication failed: Missing data');
          socialAuthService.clearOAuthSession();
          setTimeout(() => navigate('/login'), 3000);
          return;
        }

        // Parse user data
        const userData = JSON.parse(decodeURIComponent(userParam));

        // Use social auth service to handle the callback
        const result = socialAuthService.handleOAuthCallback(token, userData);

        if (!result.success) {
          setStatus('error');
          setMessage(result.error || 'Authentication failed');
          toast.error(result.error || 'Authentication failed');
          setTimeout(() => navigate('/login'), 3000);
          return;
        }

        // Store authentication data
        setToken(result.token!);
        setUser(result.user!);

        setStatus('success');
        const successMessage = mode === 'register'
          ? `Account created successfully with ${provider}! Redirecting...`
          : `Successfully signed in with ${provider}! Redirecting...`;
        setMessage(successMessage);

        toast.success(mode === 'register'
          ? `Welcome to MEDORA! Account created with ${provider}.`
          : `Welcome back! Signed in with ${provider}.`
        );

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          navigate('/dashboard');
        }, 2000);

      } catch (error) {
        console.error('OAuth callback error:', error);
        setStatus('error');
        setMessage('Failed to process authentication');
        toast.error('Authentication failed');
        socialAuthService.clearOAuthSession();
        setTimeout(() => navigate('/login'), 3000);
      }
    };

    handleOAuthCallback();
  }, [searchParams, navigate, setUser, setToken]);

  const getIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-8 w-8 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-8 w-8 text-green-500" />;
      case 'error':
        return <XCircle className="h-8 w-8 text-red-500" />;
      default:
        return <Loader2 className="h-8 w-8 animate-spin text-blue-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-blue-600';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <Card className="w-full max-w-md card-gradient neu-raised">
        <CardContent className="flex flex-col items-center justify-center p-8 space-y-4">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <img 
              src="/lovable-uploads/logo-png.png" 
              alt="MEDORA Logo" 
              className="h-10 w-10 object-contain"
            />
            <span className="text-2xl font-bold font-unica" style={{color: '#9ACD32'}}>
              MEDORA
            </span>
          </div>
          
          <div className="flex flex-col items-center space-y-4">
            {getIcon()}
            <h2 className={`text-xl font-semibold ${getStatusColor()}`}>
              {status === 'loading' && 'Authenticating...'}
              {status === 'success' && 'Success!'}
              {status === 'error' && 'Authentication Failed'}
            </h2>
            <p className="text-center text-muted-foreground">
              {message}
            </p>
            
            {status === 'loading' && (
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full animate-pulse" style={{width: '60%'}}></div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthCallback;
