const express = require('express');
const { body, validationResult, param } = require('express-validator');
const Patient = require('../models/Patient');
const User = require('../models/User');
const Diagnosis = require('../models/Diagnosis');
const { protect, restrictTo, canAccessPatient } = require('../middleware/auth');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation middleware
const validatePatientCreation = [
  body('userId').isMongoId().withMessage('Valid user ID is required'),
  body('insurance.provider').optional().trim(),
  body('insurance.policyNumber').optional().trim(),
];

const validateVitalSigns = [
  body('temperature.value').optional().isFloat({ min: 30, max: 50 }),
  body('bloodPressure.systolic').optional().isInt({ min: 50, max: 300 }),
  body('bloodPressure.diastolic').optional().isInt({ min: 30, max: 200 }),
  body('heartRate.value').optional().isInt({ min: 30, max: 250 }),
  body('weight.value').optional().isFloat({ min: 0.5, max: 1000 }),
  body('height.value').optional().isFloat({ min: 30, max: 300 }),
];

// @route   GET /api/patients
// @desc    Get all patients (admin/medical staff only)
// @access  Private
router.get('/', restrictTo('admin', 'doctor', 'nurse'), async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      status,
      primaryCareProvider,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = req.query;

    // Build query
    const query = {};
    
    if (status) {
      query.status = status;
    }
    
    if (primaryCareProvider) {
      query.primaryCareProvider = primaryCareProvider;
    }

    // Search functionality
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { mrn: searchRegex },
        { 'userId.firstName': searchRegex },
        { 'userId.lastName': searchRegex },
        { 'userId.email': searchRegex },
      ];
    }

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const patients = await Patient.find(query)
      .populate('userId', 'firstName lastName email dateOfBirth gender phoneNumber')
      .populate('primaryCareProvider', 'firstName lastName specialty')
      .populate('careTeam.provider', 'firstName lastName specialty role')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Patient.countDocuments(query);

    res.json({
      status: 'success',
      data: {
        patients,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total,
        },
      },
    });
  } catch (error) {
    logger.error('Get patients error:', error);
    next(error);
  }
});

// @route   POST /api/patients
// @desc    Create a new patient record
// @access  Private (admin/medical staff)
router.post('/', restrictTo('admin', 'doctor', 'nurse'), validatePatientCreation, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400));
    }

    const { userId, insurance, medicalHistory, emergencyContacts } = req.body;

    // Check if user exists and has patient role
    const user = await User.findById(userId);
    if (!user) {
      return next(new AppError('User not found', 404));
    }
    
    if (user.role !== 'patient') {
      return next(new AppError('User must have patient role', 400));
    }

    // Check if patient record already exists
    const existingPatient = await Patient.findOne({ userId });
    if (existingPatient) {
      return next(new AppError('Patient record already exists for this user', 400));
    }

    // Create patient record
    const patient = new Patient({
      userId,
      insurance,
      medicalHistory,
      emergencyContacts,
    });

    await patient.save();
    await patient.populate('userId', 'firstName lastName email dateOfBirth gender phoneNumber');

    logger.info(`New patient record created: ${patient.mrn} for user: ${user.email}`);

    res.status(201).json({
      status: 'success',
      data: {
        patient,
      },
    });
  } catch (error) {
    logger.error('Create patient error:', error);
    next(error);
  }
});

// @route   GET /api/patients/:id
// @desc    Get patient by ID
// @access  Private
router.get('/:id', param('id').isMongoId(), canAccessPatient, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Invalid patient ID', 400));
    }

    const patient = await Patient.findById(req.params.id)
      .populate('userId', 'firstName lastName email dateOfBirth gender phoneNumber address')
      .populate('primaryCareProvider', 'firstName lastName specialty licenseNumber')
      .populate('careTeam.provider', 'firstName lastName specialty role')
      .populate('vitalSigns.recordedBy', 'firstName lastName')
      .populate('medications.prescribedBy', 'firstName lastName specialty')
      .populate('appointments.doctor', 'firstName lastName specialty');

    if (!patient) {
      return next(new AppError('Patient not found', 404));
    }

    res.json({
      status: 'success',
      data: {
        patient,
      },
    });
  } catch (error) {
    logger.error('Get patient error:', error);
    next(error);
  }
});

// @route   PUT /api/patients/:id
// @desc    Update patient record
// @access  Private (medical staff)
router.put('/:id', restrictTo('admin', 'doctor', 'nurse'), param('id').isMongoId(), async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Invalid patient ID', 400));
    }

    const allowedUpdates = [
      'insurance',
      'medicalHistory',
      'emergencyContacts',
      'primaryCareProvider',
      'status',
      'consents',
    ];

    const updates = {};
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    const patient = await Patient.findByIdAndUpdate(
      req.params.id,
      updates,
      { new: true, runValidators: true }
    ).populate('userId', 'firstName lastName email');

    if (!patient) {
      return next(new AppError('Patient not found', 404));
    }

    logger.info(`Patient record updated: ${patient.mrn} by ${req.user.email}`);

    res.json({
      status: 'success',
      data: {
        patient,
      },
    });
  } catch (error) {
    logger.error('Update patient error:', error);
    next(error);
  }
});

// @route   POST /api/patients/:id/vital-signs
// @desc    Add vital signs
// @access  Private (medical staff)
router.post('/:id/vital-signs', restrictTo('admin', 'doctor', 'nurse'), validateVitalSigns, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400));
    }

    const patient = await Patient.findById(req.params.id);
    if (!patient) {
      return next(new AppError('Patient not found', 404));
    }

    const vitalSigns = {
      ...req.body,
      recordedBy: req.user.id,
      recordedAt: new Date(),
    };

    // Calculate BMI if height and weight are provided
    if (vitalSigns.height?.value && vitalSigns.weight?.value) {
      const heightInM = vitalSigns.height.unit === 'cm' 
        ? vitalSigns.height.value / 100 
        : vitalSigns.height.value * 0.0254;
      const weightInKg = vitalSigns.weight.unit === 'kg' 
        ? vitalSigns.weight.value 
        : vitalSigns.weight.value * 0.453592;
      
      vitalSigns.bmi = Math.round((weightInKg / (heightInM * heightInM)) * 10) / 10;
    }

    patient.vitalSigns.push(vitalSigns);
    await patient.save();

    logger.info(`Vital signs added for patient: ${patient.mrn} by ${req.user.email}`);

    res.status(201).json({
      status: 'success',
      data: {
        vitalSigns: patient.vitalSigns[patient.vitalSigns.length - 1],
      },
    });
  } catch (error) {
    logger.error('Add vital signs error:', error);
    next(error);
  }
});

// @route   GET /api/patients/:id/vital-signs
// @desc    Get patient vital signs history
// @access  Private
router.get('/:id/vital-signs', canAccessPatient, async (req, res, next) => {
  try {
    const { limit = 10, startDate, endDate } = req.query;

    const patient = await Patient.findById(req.params.id)
      .populate('vitalSigns.recordedBy', 'firstName lastName');

    if (!patient) {
      return next(new AppError('Patient not found', 404));
    }

    let vitalSigns = patient.vitalSigns;

    // Filter by date range if provided
    if (startDate || endDate) {
      vitalSigns = vitalSigns.filter(vs => {
        const recordDate = new Date(vs.recordedAt);
        if (startDate && recordDate < new Date(startDate)) return false;
        if (endDate && recordDate > new Date(endDate)) return false;
        return true;
      });
    }

    // Sort by most recent first and limit
    vitalSigns = vitalSigns
      .sort((a, b) => new Date(b.recordedAt) - new Date(a.recordedAt))
      .slice(0, parseInt(limit));

    res.json({
      status: 'success',
      data: {
        vitalSigns,
        total: patient.vitalSigns.length,
      },
    });
  } catch (error) {
    logger.error('Get vital signs error:', error);
    next(error);
  }
});

// @route   GET /api/patients/:id/diagnoses
// @desc    Get patient diagnoses
// @access  Private
router.get('/:id/diagnoses', canAccessPatient, async (req, res, next) => {
  try {
    const { status, limit = 10, page = 1 } = req.query;

    const patient = await Patient.findById(req.params.id);
    if (!patient) {
      return next(new AppError('Patient not found', 404));
    }

    const query = { patient: req.params.id };
    if (status) {
      query.status = status;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const diagnoses = await Diagnosis.find(query)
      .populate('diagnosingPhysician', 'firstName lastName specialty')
      .populate('followUp.assignedTo', 'firstName lastName specialty')
      .sort({ diagnosisDate: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Diagnosis.countDocuments(query);

    res.json({
      status: 'success',
      data: {
        diagnoses,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total,
        },
      },
    });
  } catch (error) {
    logger.error('Get patient diagnoses error:', error);
    next(error);
  }
});

// @route   POST /api/patients/:id/medications
// @desc    Add medication to patient
// @access  Private (doctors only)
router.post('/:id/medications', restrictTo('doctor'), async (req, res, next) => {
  try {
    const patient = await Patient.findById(req.params.id);
    if (!patient) {
      return next(new AppError('Patient not found', 404));
    }

    const medication = {
      ...req.body,
      prescribedBy: req.user.id,
      prescribedAt: new Date(),
    };

    patient.medications.push(medication);
    await patient.save();

    logger.info(`Medication prescribed for patient: ${patient.mrn} by ${req.user.email}`);

    res.status(201).json({
      status: 'success',
      data: {
        medication: patient.medications[patient.medications.length - 1],
      },
    });
  } catch (error) {
    logger.error('Add medication error:', error);
    next(error);
  }
});

// @route   PUT /api/patients/:id/medications/:medicationId
// @desc    Update medication
// @access  Private (doctors only)
router.put('/:id/medications/:medicationId', restrictTo('doctor'), async (req, res, next) => {
  try {
    const patient = await Patient.findById(req.params.id);
    if (!patient) {
      return next(new AppError('Patient not found', 404));
    }

    const medication = patient.medications.id(req.params.medicationId);
    if (!medication) {
      return next(new AppError('Medication not found', 404));
    }

    // Update medication fields
    Object.keys(req.body).forEach(key => {
      if (key !== 'prescribedBy' && key !== 'prescribedAt') {
        medication[key] = req.body[key];
      }
    });

    await patient.save();

    logger.info(`Medication updated for patient: ${patient.mrn} by ${req.user.email}`);

    res.json({
      status: 'success',
      data: {
        medication,
      },
    });
  } catch (error) {
    logger.error('Update medication error:', error);
    next(error);
  }
});

// @route   POST /api/patients/:id/lab-tests/upload
// @desc    Upload and process lab test files
// @access  Private (medical staff)
router.post('/:id/lab-tests/upload', restrictTo('admin', 'doctor', 'nurse'), async (req, res, next) => {
  try {
    const patient = await Patient.findById(req.params.id);
    if (!patient) {
      return next(new AppError('Patient not found', 404));
    }

    // In a real implementation, you would process uploaded files
    // For now, we'll simulate file processing
    const processedTests = [];
    
    // TODO: Implement actual lab test file processing
    // For now, just acknowledge the upload without adding mock data
    const processedTests = [];

    await patient.save();

    logger.info(`Lab tests uploaded for patient: ${patient.mrn} by ${req.user.email}`);

    res.status(201).json({
      status: 'success',
      data: {
        labTests: processedTests,
        message: `${processedTests.length} lab test(s) processed successfully`
      },
    });
  } catch (error) {
    logger.error('Lab test upload error:', error);
    next(error);
  }
});

// @route   POST /api/patients/:id/lab-tests/manual
// @desc    Manually add lab test results
// @access  Private (medical staff)
router.post('/:id/lab-tests/manual', restrictTo('admin', 'doctor', 'nurse'), async (req, res, next) => {
  try {
    const patient = await Patient.findById(req.params.id);
    if (!patient) {
      return next(new AppError('Patient not found', 404));
    }

    const labTest = {
      ...req.body,
      orderedBy: req.user.id,
      reportedAt: new Date(),
    };

    patient.labResults.push(labTest);
    await patient.save();

    logger.info(`Manual lab test added for patient: ${patient.mrn} by ${req.user.email}`);

    res.status(201).json({
      status: 'success',
      data: {
        labTest: patient.labResults[patient.labResults.length - 1],
      },
    });
  } catch (error) {
    logger.error('Manual lab test error:', error);
    next(error);
  }
});

// @route   GET /api/patients/:id/lab-tests
// @desc    Get patient lab test history
// @access  Private
router.get('/:id/lab-tests', canAccessPatient, async (req, res, next) => {
  try {
    const { category, status, limit = 20, page = 1 } = req.query;

    const patient = await Patient.findById(req.params.id)
      .populate('labResults.orderedBy', 'firstName lastName specialty');

    if (!patient) {
      return next(new AppError('Patient not found', 404));
    }

    let labResults = patient.labResults;

    // Filter by category
    if (category) {
      labResults = labResults.filter(lab => lab.category === category);
    }

    // Filter by status
    if (status) {
      labResults = labResults.filter(lab => lab.status === status);
    }

    // Sort by most recent first
    labResults = labResults.sort((a, b) => new Date(b.reportedAt) - new Date(a.reportedAt));

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const paginatedResults = labResults.slice(skip, skip + parseInt(limit));

    res.json({
      status: 'success',
      data: {
        labResults: paginatedResults,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(labResults.length / parseInt(limit)),
          total: labResults.length,
        },
      },
    });
  } catch (error) {
    logger.error('Get lab tests error:', error);
    next(error);
  }
});

module.exports = router;