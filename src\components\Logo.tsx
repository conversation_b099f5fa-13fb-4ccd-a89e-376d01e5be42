import React from 'react';

interface LogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showText?: boolean;
}

export const Logo: React.FC<LogoProps> = ({ 
  className = '', 
  size = 'md', 
  showText = true 
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-xl',
    xl: 'text-2xl'
  };

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <img
        src="/lovable-uploads/logo-png.png"
        alt="MEDORA Logo"
        className={`${sizeClasses[size]} object-contain`}
        style={{ filter: 'none' }}
      />
      {showText && (
        <span className={`font-bold text-primary font-bruno ${textSizeClasses[size]}`}>
          MEDORA
        </span>
      )}
    </div>
  );
};

export default Logo;

