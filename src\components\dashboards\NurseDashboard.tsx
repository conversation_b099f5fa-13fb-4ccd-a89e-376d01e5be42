import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Shield, 
  Calendar, 
  Users, 
  FileText, 
  Activity, 
  Clock,
  Heart,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  User,
  Settings,
  Bell,
  Search,
  Plus,
  Clipboard,
  Thermometer
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

interface NurseDashboardProps {
  className?: string;
}

const NurseDashboard: React.FC<NurseDashboardProps> = ({ className }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [shiftProgress, setShiftProgress] = useState(65);

  const [dashboardData, setDashboardData] = useState({
    shiftStats: {
      totalPatients: 0,
      vitalsChecked: 0,
      medicationsGiven: 0,
      tasksCompleted: 0,
      tasksRemaining: 0
    },
    assignedPatients: [],
    upcomingTasks: [],
    alerts: []
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        const response = await fetch('/api/nurse/dashboard', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch dashboard data');
        }

        const data = await response.json();
        setDashboardData(data);
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
        toast({
          title: "Error",
          description: "Failed to load dashboard data. Please check your connection.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [toast]);

  const quickActions = [
    {
      icon: Plus,
      title: 'Record Vitals',
      description: 'Log patient vital signs',
      color: 'bg-blue-500',
      action: () => toast({ title: "Opening vitals recorder..." })
    },
    {
      icon: Clipboard,
      title: 'Medication Log',
      description: 'Record medication given',
      color: 'bg-green-500',
      action: () => toast({ title: "Opening medication log..." })
    },
    {
      icon: FileText,
      title: 'Care Notes',
      description: 'Update patient notes',
      color: 'bg-purple-500',
      action: () => toast({ title: "Opening care notes..." })
    },
    {
      icon: Users,
      title: 'Patient List',
      description: 'View assigned patients',
      color: 'bg-orange-500',
      action: () => toast({ title: "Loading patient list..." })
    }
  ];

  const shiftMetrics = [
    { label: 'Patients Assigned', value: '18', status: 'normal' },
    { label: 'Tasks Completed', value: '24/32', status: 'good' },
    { label: 'Medications Given', value: '12/15', status: 'normal' },
    { label: 'Vitals Recorded', value: '15/18', status: 'good' }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-teal-50 to-blue-50 ${className}`}>
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-r from-teal-500 to-blue-500 rounded-full flex items-center justify-center">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Welcome, Nurse {user?.lastName || 'Professional'}!
                </h1>
                <p className="text-sm text-gray-500">Day shift - {new Date().toLocaleDateString()}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                <Search className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="relative">
                <Bell className="w-4 h-4" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs"></span>
              </Button>
              <Button variant="ghost" size="sm">
                <Settings className="w-4 h-4" />
              </Button>
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-gray-600" />
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Shift Progress */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <Card className="bg-gradient-to-r from-teal-500 to-blue-500 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold mb-2">Shift Progress</h2>
                  <p className="text-teal-100">Day shift: 7:00 AM - 7:00 PM</p>
                </div>
                <div className="text-right">
                  <div className="text-4xl font-bold">{shiftProgress}%</div>
                  <div className="text-sm text-teal-100">completed</div>
                  <Progress value={shiftProgress} className="w-32 mt-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Stats Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100">Assigned Patients</p>
                    <p className="text-3xl font-bold">{dashboardData.shiftStats.totalPatients}</p>
                  </div>
                  <Users className="w-8 h-8 text-blue-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100">Tasks Completed</p>
                    <p className="text-3xl font-bold">{dashboardData.shiftStats.tasksCompleted}</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100">Medications Given</p>
                    <p className="text-3xl font-bold">{dashboardData.shiftStats.medicationsGiven}</p>
                  </div>
                  <Clipboard className="w-8 h-8 text-purple-200" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100">Vitals Checked</p>
                    <p className="text-3xl font-bold">{dashboardData.shiftStats.vitalsChecked}</p>
                  </div>
                  <Heart className="w-8 h-8 text-orange-200" />
                </div>
              </CardContent>
            </Card>
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2 + index * 0.05 }}
              >
                <Card 
                  className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:-translate-y-1"
                  onClick={action.action}
                >
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center`}>
                        <action.icon className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{action.title}</h4>
                        <p className="text-sm text-gray-500">{action.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Assigned Patients */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Users className="w-5 h-5 text-blue-500" />
                    <span>Assigned Patients</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData.assignedPatients.map((patient) => (
                      <div key={patient.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                            <User className="w-6 h-6 text-gray-600" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900">{patient.name}</h4>
                            <p className="text-sm text-gray-500">Room {patient.room} • {patient.condition}</p>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge 
                                variant={patient.priority === 'urgent' ? 'destructive' : patient.priority === 'high' ? 'default' : 'secondary'}
                              >
                                {patient.priority}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-500">Last vitals: {patient.lastVitals}</p>
                          <p className="text-sm text-gray-500">Next med: {patient.nextMedication}</p>
                          <Button size="sm" className="mt-2">
                            View Chart
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                  <Button className="w-full mt-4" variant="outline">
                    View All Patients
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Upcoming Tasks */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Clock className="w-5 h-5 text-purple-500" />
                    <span>Upcoming Tasks</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData.upcomingTasks.map((task) => (
                      <div key={task.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                          <h4 className="font-semibold text-gray-900">{task.task}</h4>
                          <p className="text-sm text-gray-500">{task.patients} patients</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-900">{task.time}</p>
                          <Badge 
                            variant={task.priority === 'high' ? 'destructive' : 'secondary'}
                            className="mt-1"
                          >
                            {task.priority}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                  <Button className="w-full mt-4" variant="outline">
                    View Full Schedule
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Shift Metrics */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Activity className="w-5 h-5 text-green-500" />
                    <span>Shift Metrics</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {shiftMetrics.map((metric, index) => (
                      <div key={metric.label} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{metric.label}</p>
                          <p className="text-sm text-gray-500">{metric.value}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <Badge 
                            variant={metric.status === 'good' ? 'default' : 'secondary'}
                          >
                            {metric.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Patient Alerts */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <AlertTriangle className="w-5 h-5 text-red-500" />
                    <span>Patient Alerts</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData.alerts.map((alert) => (
                      <div key={alert.id} className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-red-900">{alert.patient}</h4>
                            <p className="text-sm text-red-700">Room {alert.room}</p>
                            <p className="text-sm text-red-700">{alert.alert}</p>
                            <p className="text-xs text-red-500 mt-1">{alert.time}</p>
                          </div>
                          <Badge 
                            variant={alert.severity === 'high' ? 'destructive' : 'secondary'}
                          >
                            {alert.severity}
                          </Badge>
                        </div>
                        <Button size="sm" className="mt-3 w-full" variant="outline">
                          Respond
                        </Button>
                      </div>
                    ))}
                  </div>
                  <Button className="w-full mt-4" variant="outline">
                    View All Alerts
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Care Reminders */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Thermometer className="w-5 h-5 text-blue-500" />
                    <span>Care Reminders</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-800">
                        🌡️ Temperature checks due for 3 patients in Room 200-205
                      </p>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <p className="text-sm text-green-800">
                        💊 Medication round starts in 30 minutes
                      </p>
                    </div>
                    <div className="p-3 bg-purple-50 rounded-lg">
                      <p className="text-sm text-purple-800">
                        📝 End-of-shift documentation due at 6:30 PM
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NurseDashboard;
