import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CheckCircle, Upload, Mail, AlertCircle, FileText, Calendar } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const CheckPaymentPage = () => {
  const { toast } = useToast();
  const [checkDetails, setCheckDetails] = useState({
    checkNumber: '',
    amount: '',
    date: '',
    bankName: '',
    accountNumber: '',
    notes: '',
    planType: ''
  });
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast({
          title: "File too large",
          description: "Please upload an image smaller than 5MB.",
          variant: "destructive"
        });
        return;
      }
      setUploadedImage(file);
      toast({
        title: "Image uploaded",
        description: "Check image has been uploaded successfully.",
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));

    toast({
      title: "Check Payment Submitted",
      description: "Your check payment details have been recorded. We'll verify and process within 7-10 business days.",
    });

    setIsSubmitting(false);
    
    // Reset form
    setCheckDetails({
      checkNumber: '',
      amount: '',
      date: '',
      bankName: '',
      accountNumber: '',
      notes: '',
      planType: ''
    });
    setUploadedImage(null);
  };

  const plans = [
    { value: 'starter', label: 'Starter Plan - $0/month', price: 0 },
    { value: 'professional', label: 'Professional Plan - $99/month', price: 99 },
    { value: 'enterprise', label: 'Enterprise Plan - $299/month', price: 299 }
  ];

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-4">
            Check Payment Submission
          </h1>
          <p className="text-muted-foreground">
            Submit your check payment details and upload a photo for verification
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Check Payment Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="planType">Subscription Plan</Label>
                      <Select 
                        value={checkDetails.planType} 
                        onValueChange={(value) => {
                          const selectedPlan = plans.find(p => p.value === value);
                          setCheckDetails(prev => ({
                            ...prev,
                            planType: value,
                            amount: selectedPlan?.price.toString() || ''
                          }));
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select your plan" />
                        </SelectTrigger>
                        <SelectContent>
                          {plans.map((plan) => (
                            <SelectItem key={plan.value} value={plan.value}>
                              {plan.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="amount">Check Amount ($)</Label>
                      <Input
                        id="amount"
                        type="number"
                        step="0.01"
                        value={checkDetails.amount}
                        onChange={(e) => setCheckDetails(prev => ({
                          ...prev,
                          amount: e.target.value
                        }))}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="checkNumber">Check Number</Label>
                      <Input
                        id="checkNumber"
                        value={checkDetails.checkNumber}
                        onChange={(e) => setCheckDetails(prev => ({
                          ...prev,
                          checkNumber: e.target.value
                        }))}
                        placeholder="e.g., 1001"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="date">Check Date</Label>
                      <Input
                        id="date"
                        type="date"
                        value={checkDetails.date}
                        onChange={(e) => setCheckDetails(prev => ({
                          ...prev,
                          date: e.target.value
                        }))}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="bankName">Bank Name</Label>
                      <Input
                        id="bankName"
                        value={checkDetails.bankName}
                        onChange={(e) => setCheckDetails(prev => ({
                          ...prev,
                          bankName: e.target.value
                        }))}
                        placeholder="e.g., First National Bank"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="accountNumber">Account Number (Last 4 digits)</Label>
                      <Input
                        id="accountNumber"
                        value={checkDetails.accountNumber}
                        onChange={(e) => setCheckDetails(prev => ({
                          ...prev,
                          accountNumber: e.target.value
                        }))}
                        placeholder="****1234"
                        maxLength={4}
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="checkImage">Upload Check Image</Label>
                    <div className="mt-2">
                      <input
                        id="checkImage"
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                      <label
                        htmlFor="checkImage"
                        className="flex items-center justify-center w-full h-32 border-2 border-dashed border-muted-foreground/25 rounded-lg cursor-pointer hover:border-muted-foreground/50 transition-colors"
                      >
                        {uploadedImage ? (
                          <div className="text-center">
                            <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                            <p className="text-sm font-medium">{uploadedImage.name}</p>
                            <p className="text-xs text-muted-foreground">Click to change</p>
                          </div>
                        ) : (
                          <div className="text-center">
                            <Upload className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                            <p className="text-sm font-medium">Upload check image</p>
                            <p className="text-xs text-muted-foreground">PNG, JPG up to 5MB</p>
                          </div>
                        )}
                      </label>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="notes">Additional Notes (Optional)</Label>
                    <Textarea
                      id="notes"
                      value={checkDetails.notes}
                      onChange={(e) => setCheckDetails(prev => ({
                        ...prev,
                        notes: e.target.value
                      }))}
                      placeholder="Any additional information about this payment..."
                      rows={3}
                    />
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={isSubmitting || !checkDetails.checkNumber || !checkDetails.amount}
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit Check Payment'}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  Mailing Address
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <p className="font-medium">MEDORA Technologies Inc.</p>
                  <p>Accounts Receivable</p>
                  <p>123 Business Avenue</p>
                  <p>Suite 456</p>
                  <p>New York, NY 10001</p>
                  <p>United States</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  Important Notes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Make checks payable to "MEDORA Technologies Inc."</li>
                  <li>• Write your email address on the memo line</li>
                  <li>• Allow 7-10 business days for processing</li>
                  <li>• You'll receive confirmation once processed</li>
                  <li>• Upload a clear photo of the entire check</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Processing Timeline
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <div>
                      <p className="text-sm font-medium">Day 1: Submission</p>
                      <p className="text-xs text-muted-foreground">Check details recorded</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-muted-foreground rounded-full"></div>
                    <div>
                      <p className="text-sm font-medium">Day 3-5: Verification</p>
                      <p className="text-xs text-muted-foreground">Check received and verified</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-muted-foreground rounded-full"></div>
                    <div>
                      <p className="text-sm font-medium">Day 7-10: Processing</p>
                      <p className="text-xs text-muted-foreground">Account activated</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckPaymentPage;