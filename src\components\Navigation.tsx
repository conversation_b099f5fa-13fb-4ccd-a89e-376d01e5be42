import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import {
  Monitor,
  Settings,
  User,
  CreditCard,
  FileText,
  Users,
  Stethoscope,
  BarChart3,
  Shield,
  Home,
  LogOut,
  Calendar
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface NavigationItem {
  name: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  roles?: string[];
  description?: string;
}

// Function to get role-specific navigation items
const getNavigationItems = (userRole?: string): NavigationItem[] => {
  const baseItems: NavigationItem[] = [
    {
      name: 'Account',
      path: '/account',
      icon: User,
      description: 'Account settings'
    },
    {
      name: 'Billing',
      path: '/billing',
      icon: CreditCard,
      description: 'Billing & payments'
    }
  ];

  // Role-specific dashboard items
  const roleSpecificItems: NavigationItem[] = [];

  if (userRole === 'patient') {
    roleSpecificItems.push(
      {
        name: 'My Dashboard',
        path: '/patient-dashboard',
        icon: Home,
        description: 'Patient dashboard'
      },
      {
        name: 'Appointments',
        path: '/appointments',
        icon: Calendar,
        description: 'My appointments'
      },
      {
        name: 'Health Records',
        path: '/health-records',
        icon: FileText,
        description: 'My health records'
      }
    );
  } else if (userRole === 'doctor') {
    roleSpecificItems.push(
      {
        name: 'Doctor Dashboard',
        path: '/doctor-dashboard',
        icon: Home,
        description: 'Doctor dashboard'
      },
      {
        name: 'Medical Dashboard',
        path: '/medical-dashboard',
        icon: Stethoscope,
        description: 'Patient records & diagnosis'
      },
      {
        name: 'Appointments',
        path: '/appointments',
        icon: Calendar,
        description: 'Patient appointments'
      },
      {
        name: 'API Documentation',
        path: '/api-docs',
        icon: FileText,
        description: 'API documentation'
      }
    );
  } else if (userRole === 'nurse') {
    roleSpecificItems.push(
      {
        name: 'Nurse Dashboard',
        path: '/nurse-dashboard',
        icon: Home,
        description: 'Nurse dashboard'
      },
      {
        name: 'Medical Dashboard',
        path: '/medical-dashboard',
        icon: Stethoscope,
        description: 'Patient records & diagnosis'
      },
      {
        name: 'Appointments',
        path: '/appointments',
        icon: Calendar,
        description: 'Patient appointments'
      }
    );
  } else if (userRole === 'admin') {
    roleSpecificItems.push(
      {
        name: 'Admin Dashboard',
        path: '/dashboard',
        icon: Home,
        description: 'Admin dashboard'
      },
      {
        name: 'User Management',
        path: '/user-management',
        icon: Users,
        description: 'Manage users'
      },
      {
        name: 'Medical Dashboard',
        path: '/medical-dashboard',
        icon: Stethoscope,
        description: 'Patient records & diagnosis'
      },
      {
        name: 'Monitoring',
        path: '/monitoring',
        icon: Monitor,
        description: 'System monitoring'
      },
      {
        name: 'Settings',
        path: '/settings',
        icon: Settings,
        description: 'Global settings'
      },
      {
        name: 'API Documentation',
        path: '/api-docs',
        icon: FileText,
        description: 'API documentation'
      }
    );
  } else {
    // Default dashboard for unknown roles
    roleSpecificItems.push({
      name: 'Dashboard',
      path: '/dashboard',
      icon: Home,
      description: 'Main dashboard'
    });
  }

  return [...roleSpecificItems, ...baseItems];
};

interface NavigationProps {
  variant?: 'header' | 'sidebar' | 'mobile';
  className?: string;
}

export const Navigation: React.FC<NavigationProps> = ({ 
  variant = 'header',
  className 
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();

  const handleNavigation = (path: string) => {
    console.log('🔗 Navigating to:', path);
    try {
      navigate(path);
    } catch (error) {
      console.error('❌ Navigation error:', error);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('❌ Logout error:', error);
    }
  };

  const navigationItems = getNavigationItems(user?.role);

  const isActive = (path: string) => location.pathname === path;

  if (variant === 'header') {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        {navigationItems.slice(0, 4).map((item) => (
          <Button
            key={item.path}
            variant={isActive(item.path) ? "default" : "ghost"}
            size="sm"
            onClick={() => handleNavigation(item.path)}
            className={cn(
              "text-muted-foreground hover:text-foreground transition-colors",
              isActive(item.path) && "bg-primary text-primary-foreground"
            )}
          >
            <item.icon className="h-4 w-4 mr-1" />
            <span className="hidden lg:inline">{item.name}</span>
          </Button>
        ))}

        <Button
          variant="ghost"
          size="sm"
          onClick={handleLogout}
          className="text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <LogOut className="h-4 w-4 mr-1" />
          <span className="hidden lg:inline">Logout</span>
        </Button>
      </div>
    );
  }

  if (variant === 'sidebar') {
    return (
      <nav className={cn("space-y-2", className)}>
        {navigationItems.map((item) => (
          <Button
            key={item.path}
            variant={isActive(item.path) ? "default" : "ghost"}
            className={cn(
              "w-full justify-start",
              isActive(item.path) && "bg-primary text-primary-foreground"
            )}
            onClick={() => handleNavigation(item.path)}
          >
            <item.icon className="h-4 w-4 mr-2" />
            {item.name}
          </Button>
        ))}

        <div className="pt-4 border-t">
          <Button
            variant="ghost"
            className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
            onClick={handleLogout}
          >
            <LogOut className="h-4 w-4 mr-2" />
            Logout
          </Button>
        </div>
      </nav>
    );
  }

  if (variant === 'mobile') {
    return (
      <div className={cn("space-y-2", className)}>
        {navigationItems.map((item) => (
          <Button
            key={item.path}
            variant={isActive(item.path) ? "default" : "ghost"}
            className={cn(
              "w-full justify-start",
              isActive(item.path) && "bg-primary text-primary-foreground"
            )}
            onClick={() => handleNavigation(item.path)}
          >
            <item.icon className="h-4 w-4 mr-2" />
            <div className="text-left">
              <div>{item.name}</div>
              {item.description && (
                <div className="text-xs text-muted-foreground">{item.description}</div>
              )}
            </div>
          </Button>
        ))}

        <div className="pt-4 border-t">
          <Button
            variant="ghost"
            className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
            onClick={handleLogout}
          >
            <LogOut className="h-4 w-4 mr-2" />
            Logout
          </Button>
        </div>
      </div>
    );
  }

  return null;
};

export default Navigation;
