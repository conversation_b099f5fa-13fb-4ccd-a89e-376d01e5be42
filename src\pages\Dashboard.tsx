import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardHeader, CardContent, CardDescription, CardTitle } from '@/components/ui/card';
import { Phone, MessageSquare, Calendar, FileText, Settings, LogOut, User, Activity, Heart, Stethoscope, Users, Monitor, CreditCard } from 'lucide-react';
import VoiceCallInterface from '@/components/VoiceCallInterface';
import ChatInterface from '@/components/ChatInterface';
import { useNavigate, Navigate } from 'react-router-dom';
import ThemeSwitcher from '@/components/ThemeSwitcher';
import Navigation from '@/components/Navigation';

const Dashboard = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [showVoiceCall, setShowVoiceCall] = useState(false);
  const [showTextChat, setShowTextChat] = useState(false);

  // Redirect to role-specific dashboard if user has a specific role
  useEffect(() => {
    if (user?.role) {
      switch (user.role) {
        case 'patient':
          navigate('/patient-dashboard', { replace: true });
          return;
        case 'doctor':
          navigate('/doctor-dashboard', { replace: true });
          return;
        case 'nurse':
          navigate('/nurse-dashboard', { replace: true });
          return;
        case 'admin':
          // Admin can stay on general dashboard
          break;
        default:
          // Unknown role, stay on general dashboard
          break;
      }
    }
  }, [user?.role, navigate]);

  const handleStartVoiceCall = () => {
    setShowVoiceCall(true);
    setShowTextChat(false);
  };

  const handleStartTextChat = () => {
    setShowTextChat(true);
    setShowVoiceCall(false);
  };

  const handleEndCall = () => {
    setShowVoiceCall(false);
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Enhanced navigation handler with error handling
  const handleNavigation = (path: string) => {
    console.log('🔗 Dashboard Navigation to:', path);
    try {
      navigate(path);
    } catch (error) {
      console.error('❌ Navigation error:', error);
      // Fallback: try window.location
      window.location.href = path;
    }
  };

  if (showVoiceCall) {
    return <VoiceCallInterface onEndCall={handleEndCall} />;
  }

  if (showTextChat) {
    return (
      <div className="min-h-screen bg-background p-4">
        <div className="max-w-4xl mx-auto">
          <div className="mb-4 flex items-center justify-between">
            <h1 className="text-2xl font-bold text-foreground">Medical Chat Assistant</h1>
            <Button onClick={handleEndCall} variant="outline">
              Back to Dashboard
            </Button>
          </div>
          <div className="h-[calc(100vh-8rem)]">
            <ChatInterface className="h-full" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <img 
              src="/lovable-uploads/logo-png.png" 
              alt="MEDORA Logo" 
              className="h-8 w-8 object-contain"
            />
            <span className="text-xl font-bold" style={{color: '#9ACD32'}}>MEDORA</span>
          </div>
          
          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* Admin Navigation Links */}
            {user?.role === 'admin' && (
              <div className="hidden md:flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleNavigation('/monitoring')}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <Monitor className="h-4 w-4 mr-1" />
                  <span className="hidden lg:inline">Monitoring</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleNavigation('/settings')}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <Settings className="h-4 w-4 mr-1" />
                  <span className="hidden lg:inline">Settings</span>
                </Button>
              </div>
            )}
            
            {/* User Navigation Links */}
            <div className="hidden md:flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleNavigation('/account')}
                className="text-muted-foreground hover:text-foreground"
              >
                <User className="h-4 w-4 mr-1" />
                <span className="hidden lg:inline">Account</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleNavigation('/billing')}
                className="text-muted-foreground hover:text-foreground"
              >
                <CreditCard className="h-4 w-4 mr-1" />
                <span className="hidden lg:inline">Billing</span>
              </Button>
            </div>
            
            <span className="text-sm text-muted-foreground hidden sm:block">
              Welcome, {user?.firstName} {user?.lastName}
            </span>
            <span className="text-xs text-muted-foreground sm:hidden">
              {user?.firstName}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              className="border-red-500/50 text-red-400 hover:bg-red-500/20"
            >
              <LogOut className="h-4 w-4 sm:mr-2" />
              <span className="hidden sm:inline">Logout</span>
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-4 sm:py-8">
        <div className="space-y-6 sm:space-y-8">
          {/* Welcome Section with User Credentials */}
          <div className="text-center space-y-4">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground font-unica">
              Medical AI Dashboard
            </h1>
            <p className="text-base sm:text-lg lg:text-xl text-muted-foreground max-w-2xl mx-auto px-4">
              Your intelligent medical consultation platform powered by advanced AI
            </p>
            {/* User Credentials Summary */}
            <div className="max-w-3xl mx-auto grid grid-cols-1 sm:grid-cols-3 gap-3 mt-2">
              <div className="p-3 rounded-md border border-border bg-card/50">
                <p className="text-xs text-muted-foreground">Name</p>
                <p className="text-sm font-medium">{user?.firstName} {user?.lastName}</p>
              </div>
              <div className="p-3 rounded-md border border-border bg-card/50">
                <p className="text-xs text-muted-foreground">Email</p>
                <p className="text-sm font-medium break-all">{user?.email}</p>
              </div>
              <div className="p-3 rounded-md border border-border bg-card/50">
                <p className="text-xs text-muted-foreground">Role</p>
                <p className="text-sm font-medium capitalize">{user?.role}</p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={handleStartVoiceCall}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Voice Consultation</CardTitle>
                <Phone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Start Call</div>
                <p className="text-xs text-muted-foreground">
                  Begin voice-based medical consultation
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={handleStartTextChat}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Text Chat</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Start Chat</div>
                <p className="text-xs text-muted-foreground">
                  Begin text-based medical consultation
                </p>
              </CardContent>
            </Card>

            {(user?.role === 'admin' || user?.role === 'doctor' || user?.role === 'nurse') && (
              <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleNavigation('/medical-dashboard')}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Medical Dashboard</CardTitle>
                  <Stethoscope className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Manage</div>
                  <p className="text-xs text-muted-foreground">
                    Patient records & diagnosis
                  </p>
                </CardContent>
              </Card>
            )}

            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleNavigation('/appointments')}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Appointments</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">
                  Upcoming appointments
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleNavigation('/health-records')}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Health Records</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">View</div>
                <p className="text-xs text-muted-foreground">
                  Access your medical records
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card className="neu-inset">
            <CardHeader className="p-4 sm:p-6">
              <h2 className="text-xl sm:text-2xl font-semibold text-foreground">Recent Activity</h2>
            </CardHeader>
            <CardContent className="p-4 sm:p-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-3 sm:space-x-4 p-3 sm:p-4 bg-card/50 rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-foreground truncate">Welcome to MEDORA</p>
                    <p className="text-xs text-muted-foreground">Your medical AI assistant is ready</p>
                  </div>
                  <span className="text-xs text-muted-foreground flex-shrink-0">Just now</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* User Info */}
          <Card className="neu-inset">
            <CardHeader className="p-4 sm:p-6">
              <h2 className="text-xl sm:text-2xl font-semibold text-foreground">Profile Information</h2>
            </CardHeader>
            <CardContent className="p-4 sm:p-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-muted-foreground">Name</label>
                  <p className="text-foreground break-words">{user?.firstName} {user?.lastName}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-muted-foreground">Email</label>
                  <p className="text-foreground break-all">{user?.email}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-muted-foreground">Role</label>
                  <p className="text-foreground capitalize">{user?.role}</p>
                </div>
                <div className="space-y-1">
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <p className="text-green-500">Active</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
