import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  CreditCard,
  Lock,
  Shield,
  Check,
  ArrowLeft,
  Calendar,
  User,
  Mail,
  Building,
  ExternalLink
} from 'lucide-react';
import { toast } from 'sonner';
import { creditsService, type PlanInfo } from '@/services/creditsService';
import { useAuth } from '@/hooks/useAuth';

const Payment = () => {
  const [paymentMethod, setPaymentMethod] = useState<'card'|'paystack'|'flutterwave'>('card');
  const [billingInfo, setBillingInfo] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'US'
  });
  
  const [cardInfo, setCardInfo] = useState({
    number: '',
    expiry: '',
    cvc: '',
    name: ''
  });
  
  const [processing, setProcessing] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();

  // Plan data from URL params
  const params = new URLSearchParams(window.location.search);
  const selectedPlan = {
    name: params.get('plan') || 'Professional',
    price: Number(params.get('price')) || 79,
    billingCycle: (params.get('cycle') || 'monthly') as 'monthly' | 'yearly',
    features: [
      'Up to 200 patient consultations/month',
      'Advanced AI diagnostics',
      'Priority support',
      'API access',
      'Team collaboration (up to 5 users)'
    ]
  };

  const handleInputChange = (section, field, value) => {
    if (section === 'billing') {
      setBillingInfo(prev => ({ ...prev, [field]: value }));
    } else if (section === 'card') {
      setCardInfo(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setProcessing(true);

    const planCredits: Record<string, number> = { 'Basic': 1000, 'Professional': 5000, 'Enterprise': 50000 };
    const credits = planCredits[selectedPlan.name] ?? 1000;

    try {
      if (paymentMethod === 'paystack') {
        if (!user?.email) throw new Error('Missing user email');
        // Store pending plan before redirect
        localStorage.setItem('pendingPlan', JSON.stringify({ name: selectedPlan.name, price: selectedPlan.price, cycle: selectedPlan.billingCycle }));
        const init = await paymentService.initiatePaystack(user.email, selectedPlan.price, selectedPlan.name, selectedPlan.billingCycle);
        if (init.success && init.data?.authorization_url) {
          window.location.href = init.data.authorization_url;
          return; // Redirecting out of page
        } else {
          throw new Error(init.error || 'Failed to initiate Paystack');
        }
      } else if (paymentMethod === 'flutterwave') {
        if (!user?.email) throw new Error('Missing user email');
        // Store pending plan before redirect
        localStorage.setItem('pendingPlan', JSON.stringify({ name: selectedPlan.name, price: selectedPlan.price, cycle: selectedPlan.billingCycle }));
        const init = await paymentService.initiateFlutterwave(user.email, selectedPlan.price, selectedPlan.name, selectedPlan.billingCycle);
        if (init.success && init.data?.link) {
          window.location.href = init.data.link;
          return; // Redirecting out of page
        } else {
          throw new Error(init.error || 'Failed to initiate Flutterwave');
        }
      } else {
        // In-app demo card processing
        await new Promise(r => setTimeout(r, 1000));
        if (user?._id) {
          const plan: PlanInfo = { name: selectedPlan.name, monthlyCredits: credits, cycle: selectedPlan.billingCycle };
          creditsService.setPlan(user._id, plan);
          creditsService.resetCycle(user._id, plan);
        }
        toast.success(`Payment successful! ${selectedPlan.name} plan activated with ${credits} AI credits / month.`);
        window.location.href = '/billing';
      }
    } catch (err) {
      console.error('Payment error:', err);
      toast.error(err instanceof Error ? err.message : 'Payment failed');
    } finally {
      setProcessing(false);
    }
  };

  const formatCardNumber = (value) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiry = (value) => {
    const v = value.replace(/\D/g, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-white">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <img 
                src="/lovable-uploads/logo-png.png" 
                alt="MEDORA Logo" 
                className="h-10 w-10 object-contain"
              />
              <div>
                <h1 className="text-3xl font-bold" style={{color: '#9ACD32'}}>Secure Checkout</h1>
                <p className="text-muted-foreground">Complete your MEDORA subscription</p>
              </div>
            </div>
            <Button variant="outline" className="flex items-center gap-2" onClick={() => navigate('/pricing')}>
              <ArrowLeft className="h-4 w-4" />
              Back to Pricing
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {/* Order Summary */}
          <div className="order-2 lg:order-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" style={{color: '#9ACD32'}} />
                  Order Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">{selectedPlan.name} Plan</h3>
                    <p className="text-sm text-muted-foreground">Billed {selectedPlan.billingCycle}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">${selectedPlan.price}</p>
                    <p className="text-sm text-muted-foreground">/{selectedPlan.billingCycle === 'monthly' ? 'month' : 'year'}</p>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-2">
                  <h4 className="font-medium">Plan includes:</h4>
                  {selectedPlan.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Check className="h-4 w-4" style={{color: '#9ACD32'}} />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
                
                <Separator />
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>${selectedPlan.price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax</span>
                    <span>$0.00</span>
                  </div>
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total</span>
                    <span>${selectedPlan.price}</span>
                  </div>
                </div>
                
                <div className="bg-muted p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Lock className="h-4 w-4" style={{color: '#9ACD32'}} />
                    <span className="font-medium">Secure Payment</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Your payment information is encrypted and secure. We never store your credit card details.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Form */}
          <div className="order-1 lg:order-2">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Billing Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" style={{color: '#9ACD32'}} />
                    Billing Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">First Name</Label>
                      <Input
                        id="firstName"
                        value={billingInfo.firstName}
                        onChange={(e) => handleInputChange('billing', 'firstName', e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input
                        id="lastName"
                        value={billingInfo.lastName}
                        onChange={(e) => handleInputChange('billing', 'lastName', e.target.value)}
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={billingInfo.email}
                      onChange={(e) => handleInputChange('billing', 'email', e.target.value)}
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="company">Company (Optional)</Label>
                    <Input
                      id="company"
                      value={billingInfo.company}
                      onChange={(e) => handleInputChange('billing', 'company', e.target.value)}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      value={billingInfo.address}
                      onChange={(e) => handleInputChange('billing', 'address', e.target.value)}
                      required
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        value={billingInfo.city}
                        onChange={(e) => handleInputChange('billing', 'city', e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="state">State</Label>
                      <Input
                        id="state"
                        value={billingInfo.state}
                        onChange={(e) => handleInputChange('billing', 'state', e.target.value)}
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="zipCode">ZIP Code</Label>
                      <Input
                        id="zipCode"
                        value={billingInfo.zipCode}
                        onChange={(e) => handleInputChange('billing', 'zipCode', e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="country">Country</Label>
                      <select 
                        id="country"
                        value={billingInfo.country}
                        onChange={(e) => handleInputChange('billing', 'country', e.target.value)}
                        className="w-full px-3 py-2 border rounded-md bg-background"
                        required
                      >
                        <option value="US">United States</option>
                        <option value="CA">Canada</option>
                        <option value="UK">United Kingdom</option>
                        <option value="AU">Australia</option>
                      </select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Payment Method */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" style={{color: '#9ACD32'}} />
                    Payment Method
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-4">
                    <button
                      type="button"
                      onClick={() => setPaymentMethod('card')}
                      className={`flex-1 p-4 border rounded-lg flex items-center justify-center gap-2 transition-all ${
                        paymentMethod === 'card' ? 'border-2' : 'border'
                      }`}
                      style={paymentMethod === 'card' ? {borderColor: '#9ACD32'} : {}}
                    >
                      <CreditCard className="h-5 w-5" />
                      Card (demo)
                    </button>
                    <button
                      type="button"
                      onClick={() => setPaymentMethod('paystack')}
                      className={`flex-1 p-4 border rounded-lg flex items-center justify-center gap-2 transition-all ${
                        paymentMethod === 'paystack' ? 'border-2' : 'border'
                      }`}
                      style={paymentMethod === 'paystack' ? {borderColor: '#9ACD32'} : {}}
                    >
                      <ExternalLink className="h-5 w-5" />
                      Paystack
                    </button>
                    <button
                      type="button"
                      onClick={() => setPaymentMethod('flutterwave')}
                      className={`flex-1 p-4 border rounded-lg flex items-center justify-center gap-2 transition-all ${
                        paymentMethod === 'flutterwave' ? 'border-2' : 'border'
                      }`}
                      style={paymentMethod === 'flutterwave' ? {borderColor: '#9ACD32'} : {}}
                    >
                      <ExternalLink className="h-5 w-5" />
                      Flutterwave
                    </button>
                  </div>

                  {paymentMethod === 'card' && (
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="cardName">Cardholder Name</Label>
                        <Input
                          id="cardName"
                          value={cardInfo.name}
                          onChange={(e) => handleInputChange('card', 'name', e.target.value)}
                          required
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="cardNumber">Card Number</Label>
                        <Input
                          id="cardNumber"
                          placeholder="1234 5678 9012 3456"
                          value={cardInfo.number}
                          onChange={(e) => handleInputChange('card', 'number', formatCardNumber(e.target.value))}
                          maxLength={19}
                          required
                        />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="expiry">Expiry Date</Label>
                          <Input
                            id="expiry"
                            placeholder="MM/YY"
                            value={cardInfo.expiry}
                            onChange={(e) => handleInputChange('card', 'expiry', formatExpiry(e.target.value))}
                            maxLength={5}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="cvc">CVC</Label>
                          <Input
                            id="cvc"
                            placeholder="123"
                            value={cardInfo.cvc}
                            onChange={(e) => handleInputChange('card', 'cvc', e.target.value.replace(/\D/g, ''))}
                            maxLength={4}
                            required
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Submit Button */}
              <Button 
                type="submit" 
                className="w-full py-6 text-lg"
                style={{backgroundColor: '#9ACD32', color: '#000000'}}
                disabled={processing}
              >
                {processing ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Processing Payment...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Lock className="h-5 w-5" />
                    Complete Payment - ${selectedPlan.price}
                  </div>
                )}
              </Button>
              
              <p className="text-xs text-muted-foreground text-center">
                By completing this purchase, you agree to our Terms of Service and Privacy Policy. 
                Your subscription will automatically renew unless cancelled.
              </p>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Payment;