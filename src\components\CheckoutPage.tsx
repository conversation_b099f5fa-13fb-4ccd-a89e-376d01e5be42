import React, { useState } from 'react';
import { CreditCard, Lock, ArrowLeft, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';

const CheckoutPage = () => {
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [isProcessing, setIsProcessing] = useState(false);

  const handlePayment = async () => {
    setIsProcessing(true);
    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false);
      // Handle success/error
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Plans
          </Button>
          <h1 className="text-4xl font-bold text-foreground font-unica">CHECKOUT</h1>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Payment Form */}
          <div className="lg:col-span-2 space-y-8">
            {/* Plan Summary */}
            <Card className="p-6 neu-raised">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold">Professional Plan</h3>
                  <p className="text-muted-foreground">Monthly subscription</p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-primary">$99.00</div>
                  <p className="text-sm text-muted-foreground">per month</p>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t border-border">
                <div className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-emerald-500" />
                  <span className="text-sm">5,000 AI consultations per month</span>
                </div>
              </div>
            </Card>

            {/* Payment Method */}
            <Card className="p-6 neu-raised">
              <h3 className="text-xl font-semibold mb-6 flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                Payment Method
              </h3>

              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <button
                    onClick={() => setPaymentMethod('card')}
                    className={`p-4 border rounded-lg flex items-center space-x-3 transition-all ${
                      paymentMethod === 'card'
                        ? 'border-primary neu-pressed'
                        : 'border-border neu-flat hover:neu-raised'
                    }`}
                  >
                    <CreditCard className="h-5 w-5" />
                    <span>Credit Card</span>
                  </button>
                  
                  <button
                    onClick={() => setPaymentMethod('paypal')}
                    className={`p-4 border rounded-lg flex items-center space-x-3 transition-all ${
                      paymentMethod === 'paypal'
                        ? 'border-primary neu-pressed'
                        : 'border-border neu-flat hover:neu-raised'
                    }`}
                  >
                    <div className="w-5 h-5 bg-blue-500 rounded"></div>
                    <span>PayPal</span>
                  </button>
                </div>

                {paymentMethod === 'card' && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Card Number</label>
                      <Input 
                        placeholder="1234 5678 9012 3456" 
                        className="h-12 neu-inset" 
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Expiry Date</label>
                        <Input placeholder="MM/YY" className="h-12 neu-inset" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">CVC</label>
                        <Input placeholder="123" className="h-12 neu-inset" />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-2">Cardholder Name</label>
                      <Input placeholder="John Doe" className="h-12 neu-inset" />
                    </div>
                  </div>
                )}
              </div>
            </Card>

            {/* Billing Information */}
            <Card className="p-6 neu-raised">
              <h3 className="text-xl font-semibold mb-6">Billing Information</h3>
              <div className="grid md:grid-cols-2 gap-4">
                <Input placeholder="First Name" className="h-12 neu-inset" />
                <Input placeholder="Last Name" className="h-12 neu-inset" />
                <Input placeholder="Email Address" className="h-12 neu-inset md:col-span-2" />
                <Input placeholder="Company Name (Optional)" className="h-12 neu-inset md:col-span-2" />
                <Input placeholder="Address" className="h-12 neu-inset md:col-span-2" />
                <Input placeholder="City" className="h-12 neu-inset" />
                <Input placeholder="ZIP Code" className="h-12 neu-inset" />
              </div>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="space-y-6">
            <Card className="p-6 neu-raised">
              <h3 className="text-xl font-semibold mb-6">Order Summary</h3>
              
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span>Professional Plan</span>
                  <span>$99.00</span>
                </div>
                <div className="flex justify-between text-muted-foreground">
                  <span>Tax</span>
                  <span>$7.92</span>
                </div>
                <div className="flex justify-between text-muted-foreground">
                  <span>Processing Fee</span>
                  <span>$2.50</span>
                </div>
                
                <div className="border-t pt-4">
                  <div className="flex justify-between text-xl font-bold">
                    <span>Total</span>
                    <span className="text-primary">$109.42</span>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">Billed monthly</p>
                </div>
              </div>

              <div className="mt-6 p-4 bg-secondary/50 rounded-lg neu-inset">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium">Free Trial Included</p>
                    <p className="text-muted-foreground">7-day free trial. Cancel anytime.</p>
                  </div>
                </div>
              </div>
            </Card>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox id="terms" />
                <label htmlFor="terms" className="text-sm">
                  I agree to the Terms of Service and Privacy Policy
                </label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox id="emails" />
                <label htmlFor="emails" className="text-sm">
                  Send me product updates and medical insights
                </label>
              </div>
            </div>

            <Button 
              className="w-full h-14 text-lg" 
              onClick={handlePayment}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  <Lock className="h-5 w-5 mr-2" />
                  Complete Payment
                </>
              )}
            </Button>

            <div className="flex items-center justify-center space-x-4 text-xs text-muted-foreground">
              <Lock className="h-4 w-4" />
              <span>Secured by 256-bit SSL encryption</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;