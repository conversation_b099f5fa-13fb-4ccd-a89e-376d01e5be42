
import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  PhoneOff,
  Mic,
  MicOff,
  Video,
  VideoOff,
  Plus,
  Volume2,
  Pause,
  Square,
  MoreVertical,
  Send,
  MessageSquare,
  Phone
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { apiClient } from '@/services/api';
import { creditsService } from '@/services/creditsService';
import { useAuth } from '@/hooks/useAuth';
import lawraAvatar from '/lovable-uploads/047b7b2a-050d-44d4-8fe0-3bc138ac9ed7.png';

interface VoiceCallInterfaceProps {
  onEndCall?: () => void;
}

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

interface SpeechRecognitionEvent extends Event {
  resultIndex: number;
  results: SpeechRecognitionResultList;
}

interface SpeechRecognitionResultList {
  length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionResult {
  length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
  isFinal: boolean;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start(): void;
  stop(): void;
  abort(): void;
  onstart: ((this: SpeechRecognition, ev: Event) => void) | null;
  onend: ((this: SpeechRecognition, ev: Event) => void) | null;
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => void) | null;
  onerror: ((this: SpeechRecognition, ev: Event) => void) | null;
}

declare global {
  interface Window {
    SpeechRecognition: new () => SpeechRecognition;
    webkitSpeechRecognition: new () => SpeechRecognition;
  }
}

const VoiceCallInterface = ({ onEndCall }: VoiceCallInterfaceProps) => {
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [callStatus, setCallStatus] = useState<'dialing' | 'connecting' | 'connected' | 'ended'>('dialing');
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [showChat, setShowChat] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isVoiceMode, setIsVoiceMode] = useState(true); // Start in voice mode by default
  const [transcript, setTranscript] = useState('');
  const [speechStatus, setSpeechStatus] = useState('Ready to listen');
  const [recognitionState, setRecognitionState] = useState<'idle' | 'starting' | 'running' | 'stopping'>('idle');
  const [isSpeaking, setIsSpeaking] = useState(false);

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);

	  const { user } = useAuth();
  const selectedVoiceRef = useRef<SpeechSynthesisVoice | null>(null);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      if (SpeechRecognition) {
        recognitionRef.current = new SpeechRecognition();
        recognitionRef.current.continuous = true;
        recognitionRef.current.interimResults = true;
        recognitionRef.current.lang = 'en-US';
        recognitionRef.current.maxAlternatives = 1;

        // Add timeout settings to prevent hanging
        if ('webkitSpeechRecognition' in window) {
          // Chrome-specific settings
          recognitionRef.current.serviceURI = '';
        }

        recognitionRef.current.onstart = () => {
          console.log('Speech recognition started');
          setIsListening(true);
          setRecognitionState('running');
          setSpeechStatus('Listening... Speak now');
        };

        recognitionRef.current.onresult = (event: SpeechRecognitionEvent) => {
           let finalTranscript = '';
           let interimTranscript = '';

           for (let i = event.resultIndex; i < event.results.length; i++) {
             const result = event.results[i];
             if (result[0]) {
               if (result.isFinal) {
                 finalTranscript = result[0].transcript;
               } else {
                 interimTranscript = result[0].transcript;
               }
             }
           }

           if (finalTranscript) {
             setCurrentMessage(finalTranscript);
             setTranscript('');
             // Auto-send voice message
             if (isVoiceMode) {
               sendVoiceMessage(finalTranscript);
             }
           } else {
             setTranscript(interimTranscript);
           }
         };

        recognitionRef.current.onerror = (event) => {
          console.error('Speech recognition error:', event);
          setIsListening(false);
          setRecognitionState('idle');

          // Handle different types of errors
          switch (event.error) {
            case 'no-speech':
              console.log('No speech detected, restarting recognition...');
              setSpeechStatus('No speech detected, restarting...');
              // Auto-restart after a short delay for no-speech errors
              setTimeout(() => {
                if (isVoiceMode && recognitionState === 'idle') {
                  setSpeechStatus('Ready to listen');
                  startVoiceRecognition();
                }
              }, 2000);
              break;

            case 'aborted':
              console.log('Speech recognition aborted, will restart if needed');
              setSpeechStatus('Recognition aborted');
              // Don't auto-restart for aborted - it was intentional
              break;

            case 'audio-capture':
              console.error('Microphone access denied or not available');
              setSpeechStatus('Microphone not available');
              break;

            case 'not-allowed':
              console.error('Speech recognition permission denied');
              setSpeechStatus('Microphone permission denied');
              break;

            case 'network':
              console.error('Network error during speech recognition');
              setSpeechStatus('Network error, retrying...');
              setTimeout(() => {
                if (isVoiceMode && recognitionState === 'idle') {
                  startVoiceRecognition();
                }
              }, 3000);
              break;

            default:
              console.error('Unknown speech recognition error:', event.error);
              setSpeechStatus('Speech recognition error');
          }
        };

        recognitionRef.current.onend = () => {
          console.log('Speech recognition ended');
          setIsListening(false);
          setRecognitionState('idle');
          setSpeechStatus('Recognition ended');

          // Auto-restart recognition if still in voice mode and not manually stopped
          if (isVoiceMode && !isSpeaking && recognitionState !== 'stopping') {
            setTimeout(() => {
              if (isVoiceMode && !isListening && !isSpeaking && recognitionState === 'idle') {
                console.log('Auto-restarting speech recognition...');
                setSpeechStatus('Restarting...');
                startVoiceRecognition();
              }
            }, 1500);
          }
        };
      }

      // Initialize speech synthesis
      if (window.speechSynthesis) {
        synthRef.current = window.speechSynthesis;
        // Load available voices asynchronously (Chrome quirk)
        const loadVoices = () => {
          const voices = synthRef.current?.getVoices() || [];
          // Prefer high-quality natural voices where available
          const preferredNames = [
            'Google UK English Female', 'Google US English', 'Samantha', 'Victoria', 'Moira',
            'Microsoft Aria Online (Natural) - English (United States)',
            'Microsoft Jenny Online (Natural) - English (United States)'
          ];
          const match = voices.find(v => preferredNames.some(n => v.name.includes(n)))
            || voices.find(v => v.lang.startsWith('en'))
            || null;
          selectedVoiceRef.current = match;
        };
        loadVoices();
        if (synthRef.current.onvoiceschanged !== undefined) {
          synthRef.current.onvoiceschanged = loadVoices as any;
        }
      }
    }

    return () => {
      console.log('Cleaning up speech recognition...');
      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
          recognitionRef.current.abort();
        } catch (error) {
          console.log('Error during cleanup:', error);
        }
      }
      if (synthRef.current) {
        synthRef.current.cancel();
      }
      setIsListening(false);
      setRecognitionState('idle');
    };
  }, [isVoiceMode]);

  // Auto-start voice recognition and welcome message when component loads
  useEffect(() => {
    const timer = setTimeout(() => {
      if (callStatus === 'connected' && isVoiceMode) {
        // Add welcome message
        const welcomeMessage: Message = {
          id: 'welcome-' + Date.now(),
          text: "Hello! I'm MEDORA, your AI medical assistant. I'm ready to help you with any health concerns. Please speak clearly and tell me how I can assist you today.",
          sender: 'ai',
          timestamp: new Date()
        };
        setMessages([welcomeMessage]);

        // Speak welcome message
        speakText(welcomeMessage.text);

        // Start voice recognition after welcome message
        setTimeout(() => {
          if (!isListening && recognitionState === 'idle') {
            startVoiceRecognition();
          }
        }, 5000); // Wait for welcome message to finish
      }
    }, 1000); // Wait 1 second after connection

    return () => clearTimeout(timer);
  }, [callStatus, isVoiceMode]);

  const handleEndCall = () => {
    setCallStatus('ended');
    onEndCall?.();
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const toggleVideo = () => {
    setIsVideoOff(!isVideoOff);
  };

  const toggleRecording = () => {
    setIsRecording(!isRecording);
  };

  const toggleChat = () => {
    setShowChat(!showChat);
  };

  const sendMessage = async () => {
    if (currentMessage.trim()) {
      const userMessage: Message = {
        id: Date.now().toString(),
        text: currentMessage,
        sender: 'user',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, userMessage]);
      const messageText = currentMessage;
      setCurrentMessage('');

      try {
        // Send to AI service using API client
        const response = await apiClient.post('/ai/chat', {
          message: messageText,
          conversationId: 'voice_call_' + Date.now()
        });

        if (response.success && response.data) {
          const aiResponse: Message = {
            id: (Date.now() + 1).toString(),
            text: response.data.response,
            sender: 'ai',
            timestamp: new Date()
          };
          setMessages(prev => [...prev, aiResponse]);

          // Speak AI response if in voice mode
          if (isVoiceMode) {
            speakText(aiResponse.text);
          }
        } else {
          console.error('AI API Error:', response.error);
          throw new Error(response.error || 'Failed to get AI response');
        }
      } catch (error) {
        console.error('Error sending message:', error);

        let errorMessage = "I'm sorry, I'm having trouble connecting right now. Please try again.";

        if (error instanceof Error) {
          if (error.message.includes('Failed to fetch') || error.message.includes('ERR_CONNECTION_REFUSED')) {
            errorMessage = "Unable to connect to the AI service. Please check if the backend server is running.";
          } else if (error.message.includes('OpenAI')) {
            errorMessage = "AI service is temporarily unavailable. Please try again later.";
          }
        }

        const errorResponse: Message = {
          id: (Date.now() + 1).toString(),
          text: errorMessage,
          sender: 'ai',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, errorResponse]);
      }
    }
  };

  const sendVoiceMessage = useCallback(async (text: string) => {
    // Spend 1 AI credit for voice message (same as chat)
    if (user?._id) {
      const spend = creditsService.spend(user._id, 1);
      if (!spend.success) {
        const outMsg: Message = {
          id: Date.now().toString(),
          text: 'You have run out of AI credits. Please upgrade your plan.',
          sender: 'ai',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, outMsg]);
        speakText(outMsg.text);
        return;
      }
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      text: text,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);

    try {
      // Send to AI service using the same endpoint as text chat
      const response = await apiClient.post('/ai/chat', {
        message: text,
        conversationId: 'voice_call_' + Date.now(),
        isVoiceInput: true
      });

      if (response.success && response.data) {
        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          text: response.data.response,
          sender: 'ai',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, aiResponse]);

        // Always speak AI response in voice mode
        speakText(aiResponse.text);
      } else {
        throw new Error(response.error || 'Failed to get AI voice response');
      }
    } catch (error) {
      console.error('Error sending voice message:', error);
      const errorResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: "I'm sorry, I'm having trouble processing your voice input. Please try again.",
        sender: 'ai',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorResponse]);
      speakText(errorResponse.text);
    }
  }, []);

  const speakText = (text: string) => {
    if (synthRef.current && !isMuted) {
      // Cancel any ongoing speech
      synthRef.current.cancel();

      const utterance = new SpeechSynthesisUtterance(text);

      // Use selected natural voice if available
      if (selectedVoiceRef.current) {
        utterance.voice = selectedVoiceRef.current;
      } else {
        const fallback = synthRef.current.getVoices().find(v => v.lang.startsWith('en'));
        if (fallback) utterance.voice = fallback;
      }

      // More human-like prosody
      utterance.rate = 0.92; // natural pace
      utterance.pitch = 1.05; // subtle warmth
      utterance.volume = 1.0; // clear

      // Add slight pauses for commas and periods
      utterance.text = text
        .replace(/, /g, ',  ')
        .replace(/\. /g, '.   ');

      // Event hooks

      utterance.onstart = () => {
        setIsSpeaking(true);
        console.log('🔊 AI speaking:', text);
      };

      utterance.onend = () => {
        setIsSpeaking(false);
        console.log('🔇 AI finished speaking');
      };

      utterance.onerror = (error) => {
        setIsSpeaking(false);
        console.error('Speech synthesis error:', error);
      };

      synthRef.current.speak(utterance);
    }
  };

  const startVoiceRecognition = () => {
    if (!recognitionRef.current) {
      console.error('Speech recognition not available');
      setSpeechStatus('Speech recognition not available');
      return;
    }

    // Prevent multiple simultaneous starts
    if (recognitionState === 'starting' || recognitionState === 'running') {
      console.log('Speech recognition already starting/running, skipping...');
      return;
    }

    try {
      console.log('Starting speech recognition...');
      setRecognitionState('starting');
      setSpeechStatus('Starting recognition...');
      recognitionRef.current.start();
      setIsVoiceMode(true);
    } catch (error) {
      console.error('Error starting speech recognition:', error);
      setRecognitionState('idle');
      setSpeechStatus('Failed to start recognition');

      // If recognition is already running, stop and restart
      if (error.name === 'InvalidStateError') {
        console.log('Recognition already running, stopping first...');
        stopVoiceRecognition();
        setTimeout(() => {
          if (recognitionState === 'idle') {
            startVoiceRecognition();
          }
        }, 1000);
      }
    }
  };

  const stopVoiceRecognition = () => {
    if (!recognitionRef.current) {
      return;
    }

    console.log('Stopping speech recognition...');
    setRecognitionState('stopping');
    setSpeechStatus('Stopping recognition...');

    try {
      if (isListening || recognitionState === 'running') {
        recognitionRef.current.stop();
      }
      setIsVoiceMode(false);

      // Force state reset after a delay
      setTimeout(() => {
        setIsListening(false);
        setRecognitionState('idle');
        setSpeechStatus('Voice mode disabled');
      }, 500);
    } catch (error) {
      console.error('Error stopping speech recognition:', error);
      setIsListening(false);
      setRecognitionState('idle');
      setSpeechStatus('Voice mode disabled');
    }
  };

  const toggleVoiceMode = () => {
    if (isVoiceMode) {
      stopVoiceRecognition();
    } else {
      startVoiceRecognition();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  };

  const handleDial = () => {
    setCallStatus('connecting');
    // Simulate connection process
    setTimeout(() => {
      setCallStatus('connected');
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-black flex flex-col p-3 sm:p-4 lg:p-6 text-white">
      {/* Dialing Page */}
      {callStatus === 'dialing' && (
        <div className="flex-1 flex flex-col items-center justify-center space-y-6 sm:space-y-8 px-4">
          <div className="flex flex-col items-center space-y-4 sm:space-y-6">
            <div className="relative">
              <div className="w-32 h-32 sm:w-36 sm:h-36 lg:w-40 lg:h-40 rounded-full overflow-hidden border-4 border-gray-600 neu-raised">
                <img
                  src={lawraAvatar}
                  alt="MEDORA AI Assistant"
                  className="w-full h-full object-cover rounded-full"
                />
              </div>
            </div>

            <div className="text-center space-y-2">
              <h2 className="text-2xl sm:text-3xl font-medium text-white font-unica uppercase">MEDORA</h2>
              <p className="text-base sm:text-lg text-gray-400 font-unica uppercase">MEDICAL AI CONSULTANT</p>
              <p className="text-gray-300 text-sm px-4">Ready to assist with your medical consultation</p>
            </div>
          </div>

          {/* Dial Button */}
          <div className="flex flex-col items-center space-y-4 sm:space-y-6">
            <Button
              onClick={handleDial}
              className="h-20 w-20 sm:h-24 sm:w-24 rounded-full bg-green-600 hover:bg-green-700 border-0 neu-raised hover:neu-flat transition-all duration-200"
            >
              <Phone className="h-10 w-10 sm:h-12 sm:w-12 text-white" />
            </Button>
            <p className="text-gray-400 text-sm">Tap to start consultation</p>
          </div>
        </div>
      )}

      {/* Call Interface - Connecting/Connected States */}
      {(callStatus === 'connecting' || callStatus === 'connected' || callStatus === 'ended') && (
        <>
          {/* Top Section - Profile */}
          <div className="flex flex-col items-center space-y-3 sm:space-y-4 mb-4 sm:mb-6">
            <div className="relative">
              <div className="w-24 h-24 sm:w-28 sm:h-28 lg:w-32 lg:h-32 rounded-full overflow-hidden border-4 border-gray-600 neu-raised">
                <img
                  src={lawraAvatar}
                  alt="MEDORA AI Assistant"
                  className="w-full h-full object-cover rounded-full"
                />
              </div>
              {callStatus === 'connecting' && (
                <div className="absolute inset-0 rounded-full border-2 border-blue-500 animate-pulse"></div>
              )}
              {isSpeaking && (
                <div className="absolute inset-0 rounded-full border-2 border-green-400 animate-pulse shadow-lg shadow-green-400/50"></div>
              )}
              {isListening && (
                <div className="absolute inset-0 rounded-full border-2 border-red-400 animate-pulse shadow-lg shadow-red-400/50"></div>
              )}
            </div>

            <div className="text-center space-y-1">
              <h2 className="text-xl sm:text-2xl font-medium text-white font-unica uppercase">MEDORA</h2>
              <p className="text-gray-400 text-xs sm:text-sm font-unica uppercase">MEDICAL AI CONSULTANT</p>
              <p className="text-gray-400 text-xs px-4">
                {callStatus === 'connecting' && 'Connecting...'}
                {callStatus === 'connected' && !isListening && !isSpeaking && speechStatus}
                {callStatus === 'connected' && isListening && 'Listening to your voice...'}
                {callStatus === 'connected' && isSpeaking && 'AI is speaking...'}
                {callStatus === 'ended' && 'Call Ended'}
              </p>

              {/* Speech Recognition Status */}
              {callStatus === 'connected' && isVoiceMode && (
                <div className="text-center mt-2">
                  <p className="text-green-400 text-xs">
                    🎤 Voice Mode: {speechStatus}
                  </p>
                  <p className="text-gray-500 text-xs">
                    State: {recognitionState}
                  </p>
                  {transcript && (
                    <p className="text-blue-400 text-xs mt-1">
                      "{transcript}"
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Chat Interface */}
          {showChat && (
            <div className="flex-1 flex flex-col bg-gray-800/50 rounded-lg p-4 mb-6 neu-inset">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-white">Chat with MEDORA</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleChat}
                className="text-gray-400 hover:text-white"
              >
                ×
              </Button>
            </div>

            <ScrollArea className="flex-1 mb-4 h-64">
              <div className="space-y-3">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${
                      message.sender === 'user' ? 'justify-end' : 'justify-start'
                    }`}
                  >
                    <div
                      className={`max-w-xs px-3 py-2 rounded-lg ${
                        message.sender === 'user'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-700 text-gray-100'
                      }`}
                    >
                      <p className="text-sm">{message.text}</p>
                      <p className="text-xs opacity-70 mt-1">
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>

                <div className="space-y-2">
                  {/* Voice transcription display */}
                  {(transcript || isListening) && (
                    <div className="bg-blue-900/30 rounded-lg p-3 border border-blue-500/30">
                      <div className="flex items-center space-x-2 mb-2">
                        <div className={`w-2 h-2 rounded-full ${
                          isListening ? 'bg-red-500 animate-pulse' : 'bg-gray-500'
                        }`}></div>
                        <span className="text-xs text-blue-300">
                          {isListening ? 'Listening...' : 'Voice input ready'}
                        </span>
                      </div>
                      {transcript && (
                        <p className="text-sm text-gray-300 italic">
                          "{transcript}"
                        </p>
                      )}
                    </div>
                  )}

                  <div className="flex space-x-2">
                    <Input
                      value={currentMessage}
                      onChange={(e) => setCurrentMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder={isVoiceMode ? "Speak or type your message..." : "Type your message..."}
                      className="flex-1 bg-gray-700 border-gray-600 text-white placeholder-gray-400"
                      disabled={isListening}
                    />
                    <Button
                      onClick={toggleVoiceMode}
                      className={`${
                        isVoiceMode
                          ? 'bg-red-600 hover:bg-red-700'
                          : 'bg-blue-600 hover:bg-blue-700'
                      } text-white`}
                      size="icon"
                      title={isVoiceMode ? 'Stop voice input' : 'Start voice input'}
                    >
                      <Mic className={`h-4 w-4 ${
                        isListening ? 'animate-pulse' : ''
                      }`} />
                    </Button>
                    <Button
                      onClick={sendMessage}
                      className="bg-green-600 hover:bg-green-700 text-black"
                      size="icon"
                      disabled={isListening}
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            )}

          {/* Spacer when chat is not shown */}
          {!showChat && <div className="flex-1" />}

          {/* Control Buttons Section */}
          <div className="space-y-10">
            {/* First Row - Secondary Controls */}
            <div className="flex justify-center space-x-8 sm:space-x-12 lg:space-x-16">
              <Button
                variant="ghost"
                size="icon"
                className="h-12 w-12 sm:h-14 sm:w-14 lg:h-16 lg:w-16 rounded-full bg-gray-800 hover:bg-gray-700 border-0 neu-raised hover:neu-flat transition-all duration-200"
                onClick={toggleRecording}
              >
                {isRecording ? (
                  <div className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 bg-red-500 rounded-sm"></div>
                ) : (
                  <div className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 rounded-full border-2 border-white"></div>
                )}
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className={`h-12 w-12 sm:h-14 sm:w-14 lg:h-16 lg:w-16 rounded-full border-0 neu-raised hover:neu-flat transition-all duration-200 ${
                  showChat ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-800 hover:bg-gray-700'
                }`}
                onClick={toggleChat}
              >
                <MessageSquare className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-white" />
               </Button>

               <Button
                 variant="ghost"
                 size="icon"
                 className="h-12 w-12 sm:h-14 sm:w-14 lg:h-16 lg:w-16 rounded-full bg-gray-800 hover:bg-gray-700 border-0 neu-raised hover:neu-flat transition-all duration-200"
               >
                 <Pause className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-white" />
               </Button>
             </div>

             {/* Second Row - Main Controls */}
             <div className="flex justify-center space-x-8 sm:space-x-12 lg:space-x-16">
               <Button
                 variant="ghost"
                 size="icon"
                 className={`h-16 w-16 sm:h-18 sm:w-18 rounded-full border-0 neu-raised hover:neu-flat transition-all duration-200 ${
                   isListening
                     ? 'bg-red-600 hover:bg-red-700 animate-pulse'
                     : isMuted
                     ? 'bg-gray-800 hover:bg-gray-700'
                     : 'bg-green-600 hover:bg-green-700'
                 }`}
                 onClick={() => {
                   if (isListening) {
                     stopVoiceRecognition();
                   } else {
                     toggleMute();
                   }
                 }}
               >
                 {isMuted ? (
                   <MicOff className="h-6 w-6 sm:h-7 sm:w-7 text-red-500" />
                 ) : isListening ? (
                   <Mic className="h-6 w-6 sm:h-7 sm:w-7 text-white animate-pulse" />
                 ) : (
                   <Mic className="h-6 w-6 sm:h-7 sm:w-7 text-white" />
                 )}
               </Button>

                <Button
                  variant="ghost"
                  size="icon"
                  className="h-18 w-18 sm:h-20 sm:w-20 rounded-full bg-green-600 hover:bg-green-700 border-0 neu-raised hover:neu-flat transition-all duration-200"
                  onClick={() => {
                    if (!showChat) {
                      toggleChat();
                    }
                  }}
                >
                  <Phone className="h-7 w-7 sm:h-8 sm:w-8 text-black" />
                </Button>

                <Button
                  variant="ghost"
                  size="icon"
                  className="h-16 w-16 sm:h-18 sm:w-18 rounded-full bg-gray-800 hover:bg-gray-700 border-0 neu-raised hover:neu-flat transition-all duration-200"
                  onClick={toggleVideo}
                >
                  {isVideoOff ? (
                    <VideoOff className="h-6 w-6 sm:h-7 sm:w-7 text-white" />
                  ) : (
                    <Video className="h-6 w-6 sm:h-7 sm:w-7 text-white" />
                  )}
                </Button>
              </div>

              {/* Third Row - End Call and Additional Controls */}
              <div className="flex justify-center items-center space-x-8 sm:space-x-12 lg:space-x-16">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-12 w-12 sm:h-14 sm:w-14 lg:h-16 lg:w-16 rounded-full bg-gray-800 hover:bg-gray-700 border-0 neu-raised hover:neu-flat transition-all duration-200"
                >
                  <Volume2 className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-white" />
                 </Button>

                 {/* End Call Button - Larger and Red */}
                 <Button
                   variant="ghost"
                   size="icon"
                   className="h-20 w-20 sm:h-22 sm:w-22 lg:h-24 lg:w-24 rounded-full bg-red-500 hover:bg-red-600 border-0 neu-raised hover:neu-pressed transition-all duration-200"
                   onClick={handleEndCall}
                 >
                   <PhoneOff className="h-8 w-8 sm:h-9 sm:w-9 lg:h-10 lg:w-10 text-white" />
                 </Button>

                 <Button
                   variant="ghost"
                   size="icon"
                   className="h-12 w-12 sm:h-14 sm:w-14 lg:h-16 lg:w-16 rounded-full bg-gray-800 hover:bg-gray-700 border-0 neu-raised hover:neu-flat transition-all duration-200"
                 >
                   <MoreVertical className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-white" />
                 </Button>
               </div>
             </div>

             {/* Bottom Navigation Dots */}
             <div className="flex justify-center space-x-4 mt-8">
               <div className="w-2 h-8 bg-white/40 rounded-full neu-inset"></div>
               <div className="w-8 h-8 bg-white rounded-full neu-raised"></div>
               <div className="w-2 h-8 bg-white/40 rounded-full neu-inset"></div>
             </div>
           </>
      )}
    </div>
  );
};

export default VoiceCallInterface;
