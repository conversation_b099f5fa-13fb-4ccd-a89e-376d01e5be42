/**
 * MEDORA Frontend Configuration
 * Centralized configuration management to replace import.meta.env references
 */

// Configuration interface
interface Config {
  // API Configuration
  api: {
    baseUrl: string;
    url: string;
    timeout: number;
  };

  // Application Configuration
  app: {
    name: string;
    version: string;
    description: string;
    environment: string;
    isDevelopment: boolean;
    isProduction: boolean;
  };

  // Theme Configuration
  theme: {
    defaultTheme: string;
  };

  // Development Configuration
  development: {
    devMode: boolean;
    debug: boolean;
  };

  // Feature Flags
  features: {
    voiceChat: boolean;
    videoCalls: boolean;
    aiDiagnosis: boolean;
    payments: boolean;
  };

  // Contact Information
  contact: {
    supportEmail: string;
    phone: string;
  };

  // Social Media
  social: {
    twitterHandle: string;
  };

  // Analytics (Optional)
  analytics: {
    googleAnalyticsId: string;
    hotjarId: string;
  };

  // Social Media Authentication
  oauth: {
    google: {
      clientId: string;
    };
    facebook: {
      appId: string;
    };
    github: {
      clientId: string;
    };
    twitter: {
      clientId: string;
    };
    apple: {
      clientId: string;
    };
    linkedin: {
      clientId: string;
    };
  };

  // External Services
  services: {
    stripe: {
      publishableKey: string;
    };
    paystack: {
      publicKey: string;
    };
  };

  // CDN Configuration (Optional)
  cdn: {
    url: string;
    assetsUrl: string;
  };
}

// Helper function to get environment variable with fallback
const getEnvVar = (key: string, defaultValue: string = ''): string => {
  return import.meta.env[key] || defaultValue;
};

// Helper function to get boolean environment variable
const getBooleanEnvVar = (key: string, defaultValue: boolean = false): boolean => {
  const value = import.meta.env[key];
  if (value === undefined) return defaultValue;
  return value === 'true' || value === '1';
};

// Configuration object
const config: Config = {
  // API Configuration
  api: {
    baseUrl: getEnvVar('VITE_API_BASE_URL', 'http://localhost:5000/api'),
    url: getEnvVar('VITE_API_URL', 'http://localhost:5000'),
    timeout: parseInt(getEnvVar('VITE_API_TIMEOUT', '30000'))
  },

  // Application Configuration
  app: {
    name: getEnvVar('VITE_APP_NAME', 'MEDORA'),
    version: getEnvVar('VITE_APP_VERSION', '1.0.0'),
    description: getEnvVar('VITE_APP_DESCRIPTION', 'AI-Powered Medical Assistant Platform'),
    environment: getEnvVar('VITE_NODE_ENV', import.meta.env.MODE || 'development'),
    isDevelopment: import.meta.env.DEV || false,
    isProduction: import.meta.env.PROD || false
  },

  // Theme Configuration
  theme: {
    defaultTheme: getEnvVar('VITE_DEFAULT_THEME', 'dark')
  },

  // Development Configuration
  development: {
    devMode: getBooleanEnvVar('VITE_DEV_MODE', import.meta.env.DEV || false),
    debug: getBooleanEnvVar('VITE_DEBUG', import.meta.env.DEV || false)
  },

  // Feature Flags
  features: {
    voiceChat: getBooleanEnvVar('VITE_ENABLE_VOICE_CHAT', true),
    videoCalls: getBooleanEnvVar('VITE_ENABLE_VIDEO_CALLS', true),
    aiDiagnosis: getBooleanEnvVar('VITE_ENABLE_AI_DIAGNOSIS', true),
    payments: getBooleanEnvVar('VITE_ENABLE_PAYMENTS', true)
  },

  // Contact Information
  contact: {
    supportEmail: getEnvVar('VITE_SUPPORT_EMAIL', '<EMAIL>'),
    phone: getEnvVar('VITE_CONTACT_PHONE', '******-123-4567')
  },

  // Social Media
  social: {
    twitterHandle: getEnvVar('VITE_TWITTER_HANDLE', '@medora_ai')
  },

  // Analytics (Optional)
  analytics: {
    googleAnalyticsId: getEnvVar('VITE_GOOGLE_ANALYTICS_ID'),
    hotjarId: getEnvVar('VITE_HOTJAR_ID')
  },

  // Social Media Authentication
  oauth: {
    google: {
      clientId: getEnvVar('VITE_GOOGLE_CLIENT_ID')
    },
    facebook: {
      appId: getEnvVar('VITE_FACEBOOK_APP_ID')
    },
    github: {
      clientId: getEnvVar('VITE_GITHUB_CLIENT_ID')
    },
    twitter: {
      clientId: getEnvVar('VITE_TWITTER_CLIENT_ID')
    },
    apple: {
      clientId: getEnvVar('VITE_APPLE_CLIENT_ID')
    },
    linkedin: {
      clientId: getEnvVar('VITE_LINKEDIN_CLIENT_ID')
    }
  },

  // External Services
  services: {
    stripe: {
      publishableKey: getEnvVar('VITE_STRIPE_PUBLISHABLE_KEY')
    },
    paystack: {
      publicKey: getEnvVar('VITE_PAYSTACK_PUBLIC_KEY')
    }
  },

  // CDN Configuration (Optional)
  cdn: {
    url: getEnvVar('VITE_CDN_URL'),
    assetsUrl: getEnvVar('VITE_ASSETS_URL')
  }
};

// Helper functions
export const isFeatureEnabled = (feature: keyof Config['features']): boolean => {
  return config.features[feature];
};

export const getApiUrl = (endpoint: string = ''): string => {
  return `${config.api.baseUrl}${endpoint}`;
};

export const isDevelopment = (): boolean => {
  return config.app.isDevelopment;
};

export const isProduction = (): boolean => {
  return config.app.isProduction;
};

export const shouldDebug = (): boolean => {
  return config.development.debug;
};

// Validation function
export const validateConfig = (): string[] => {
  const errors: string[] = [];
  
  if (!config.api.baseUrl) {
    errors.push('API base URL is not configured');
  }
  
  if (config.app.isProduction && config.api.baseUrl.includes('localhost')) {
    errors.push('Production build should not use localhost API URL');
  }
  
  return errors;
};

// Log configuration in development
if (config.development.debug) {
  console.log(' MEDORA Configuration:', {
    environment: config.app.environment,
    apiBaseUrl: config.api.baseUrl,
    features: config.features,
    isDevelopment: config.app.isDevelopment,
    isProduction: config.app.isProduction
  });
}

// Validate configuration
const configErrors = validateConfig();
if (configErrors.length > 0) {
  console.warn(' Configuration warnings:', configErrors);
}

export default config;
