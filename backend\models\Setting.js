const mongoose = require('mongoose');

const SettingSchema = new mongoose.Schema(
  {
    siteName: String,
    siteDescription: String,
    timezone: String,
    language: String,
    maintenanceMode: Boolean,
    sessionTimeout: Number,
    maxLoginAttempts: Number,
    passwordMinLength: Number,
    requireTwoFactor: Boolean,
    enableAuditLog: Boolean,
    smtpHost: String,
    smtpPort: Number,
    smtpUser: String,
    smtpPassword: String,
    fromEmail: String,
    fromName: String,
    aiConfidenceThreshold: Number,
    mlModelPath: String,
    enableAutoRetrain: Boolean,
    maxConcurrentRequests: Number,
    dbBackupFrequency: String,
    dbRetentionDays: Number,
    enableQueryLogging: Boolean,
    emailNotifications: Boolean,
    pushNotifications: Boolean,
    slackWebhook: String,
    alertThreshold: Number,
  },
  { timestamps: true }
);

module.exports = mongoose.model('Setting', SettingSchema);

