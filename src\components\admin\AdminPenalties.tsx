import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  AlertTriangle, 
  DollarSign, 
  Clock, 
  CheckCircle,
  XCircle,
  Plus,
  Edit,
  Trash2,
  User,
  Calendar,
  FileText
} from 'lucide-react';

const AdminPenalties = () => {
  const [selectedType, setSelectedType] = useState('all');
  const [showAddPenalty, setShowAddPenalty] = useState(false);

  // Mock penalty data
  const penalties = [
    {
      id: 'PEN001',
      userId: 'USR001',
      userName: 'Dr. <PERSON>',
      email: '<EMAIL>',
      type: 'late_consultation',
      amount: 50.00,
      currency: 'USD',
      reason: 'Missed scheduled consultation without prior notice',
      status: 'active',
      issuedDate: '2024-01-20',
      dueDate: '2024-02-20',
      description: '<PERSON><PERSON> waited 30 minutes for consultation that never occurred'
    },
    {
      id: 'PEN002',
      userId: 'USR002',
      userName: 'Dr. Michael Brown',
      email: '<EMAIL>',
      type: 'policy_violation',
      amount: 100.00,
      currency: 'USD',
      reason: 'Inappropriate communication with patient',
      status: 'paid',
      issuedDate: '2024-01-15',
      dueDate: '2024-02-15',
      paidDate: '2024-01-18',
      description: 'Used unprofessional language during consultation'
    },
    {
      id: 'PEN003',
      userId: 'USR003',
      userName: 'Nurse Mary Wilson',
      email: '<EMAIL>',
      type: 'documentation_error',
      amount: 25.00,
      currency: 'USD',
      reason: 'Incomplete patient documentation',
      status: 'disputed',
      issuedDate: '2024-01-18',
      dueDate: '2024-02-18',
      disputeDate: '2024-01-19',
      description: 'Failed to complete required patient intake forms'
    },
    {
      id: 'PEN004',
      userId: 'USR004',
      userName: 'Dr. Emily Davis',
      email: '<EMAIL>',
      type: 'system_misuse',
      amount: 75.00,
      currency: 'USD',
      reason: 'Unauthorized access to patient records',
      status: 'overdue',
      issuedDate: '2024-01-10',
      dueDate: '2024-02-10',
      description: 'Accessed patient records outside of assigned cases'
    }
  ];

  const penaltyTypes = [
    { value: 'late_consultation', label: 'Late Consultation', color: 'bg-yellow-100 text-yellow-800' },
    { value: 'policy_violation', label: 'Policy Violation', color: 'bg-red-100 text-red-800' },
    { value: 'documentation_error', label: 'Documentation Error', color: 'bg-orange-100 text-orange-800' },
    { value: 'system_misuse', label: 'System Misuse', color: 'bg-purple-100 text-purple-800' },
    { value: 'other', label: 'Other', color: 'bg-gray-100 text-gray-800' }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-blue-100 text-blue-800"><Clock className="w-3 h-3 mr-1" />Active</Badge>;
      case 'paid':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Paid</Badge>;
      case 'disputed':
        return <Badge className="bg-yellow-100 text-yellow-800"><AlertTriangle className="w-3 h-3 mr-1" />Disputed</Badge>;
      case 'overdue':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Overdue</Badge>;
      case 'waived':
        return <Badge className="bg-gray-100 text-gray-800">Waived</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    const penaltyType = penaltyTypes.find(pt => pt.value === type);
    return penaltyType ? (
      <Badge className={penaltyType.color}>{penaltyType.label}</Badge>
    ) : (
      <Badge className="bg-gray-100 text-gray-800">{type}</Badge>
    );
  };

  const filteredPenalties = penalties.filter(penalty => 
    selectedType === 'all' || penalty.type === selectedType
  );

  const stats = {
    total: penalties.length,
    active: penalties.filter(p => p.status === 'active').length,
    paid: penalties.filter(p => p.status === 'paid').length,
    disputed: penalties.filter(p => p.status === 'disputed').length,
    overdue: penalties.filter(p => p.status === 'overdue').length,
    totalAmount: penalties.reduce((sum, p) => sum + p.amount, 0),
    paidAmount: penalties.filter(p => p.status === 'paid').reduce((sum, p) => sum + p.amount, 0)
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Penalty Management</h1>
          <p className="text-muted-foreground">Manage penalties and violations</p>
        </div>
        <Button 
          className="bg-[#ADF802] hover:bg-[#84cc16] text-black"
          onClick={() => setShowAddPenalty(true)}
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Penalty
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Penalties</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">All time</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.active}</div>
            <p className="text-xs text-muted-foreground">Pending payment</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.paid}</div>
            <p className="text-xs text-muted-foreground">Resolved</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Disputed</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.disputed}</div>
            <p className="text-xs text-muted-foreground">Under review</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.overdue}</div>
            <p className="text-xs text-muted-foreground">Past due date</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">${stats.totalAmount.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">${stats.paidAmount.toLocaleString()} collected</p>
          </CardContent>
        </Card>
      </div>

      {/* Penalties List */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Penalty Records</CardTitle>
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 border border-input rounded-md bg-background"
            >
              <option value="all">All Types</option>
              {penaltyTypes.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredPenalties.map((penalty) => (
              <div key={penalty.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                      <AlertTriangle className="w-6 h-6 text-red-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-semibold">{penalty.userName}</h3>
                        {getStatusBadge(penalty.status)}
                        {getTypeBadge(penalty.type)}
                        <Badge variant="outline" className="text-xs">
                          {penalty.id}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{penalty.email}</p>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-2">
                        <div>
                          <span className="font-medium">Amount:</span> ${penalty.amount.toLocaleString()} {penalty.currency}
                        </div>
                        <div>
                          <span className="font-medium">Issued:</span> {penalty.issuedDate}
                        </div>
                        <div>
                          <span className="font-medium">Due:</span> {penalty.dueDate}
                        </div>
                        {penalty.paidDate && (
                          <div>
                            <span className="font-medium">Paid:</span> {penalty.paidDate}
                          </div>
                        )}
                      </div>
                      <div className="text-sm mb-2">
                        <span className="font-medium">Reason:</span> {penalty.reason}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        <span className="font-medium">Description:</span> {penalty.description}
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <Edit className="w-4 h-4 mr-1" />
                      Edit
                    </Button>
                    {penalty.status === 'active' && (
                      <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Mark Paid
                      </Button>
                    )}
                    {penalty.status === 'disputed' && (
                      <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                        <FileText className="w-4 h-4 mr-1" />
                        Review
                      </Button>
                    )}
                    <Button variant="ghost" size="sm">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Add Penalty Modal would go here */}
      {showAddPenalty && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Add New Penalty</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">User</label>
                <Input placeholder="Search user..." />
              </div>
              <div>
                <label className="text-sm font-medium">Type</label>
                <select className="w-full px-3 py-2 border border-input rounded-md bg-background">
                  {penaltyTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="text-sm font-medium">Amount ($)</label>
                <Input type="number" placeholder="0.00" />
              </div>
              <div>
                <label className="text-sm font-medium">Reason</label>
                <Input placeholder="Brief reason for penalty" />
              </div>
              <div>
                <label className="text-sm font-medium">Description</label>
                <Textarea placeholder="Detailed description of the violation" />
              </div>
              <div className="flex space-x-2">
                <Button className="flex-1 bg-[#ADF802] hover:bg-[#84cc16] text-black">
                  Add Penalty
                </Button>
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => setShowAddPenalty(false)}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default AdminPenalties;
