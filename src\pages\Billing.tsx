import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  CreditCard,
  DollarSign,
  Calendar,
  Download,
  Plus,
  CheckCircle,
  AlertTriangle,
  Clock,
  Users,
  Zap,
  Shield,
  Star,
  ArrowRight,
  Receipt,
  Settings,
  Brain
} from "lucide-react";
import { useAuth } from '@/hooks/useAuth';
import { creditsService } from '@/services/creditsService';

interface BillingInfo {
  currentPlan: string;
  planPrice: number;
  billingCycle: string;
  nextBillingDate: string;
  usage: {
    patients: number;
    consultations: number;
    storage: number;
  };
  limits: {
    patients: number;
    consultations: number;
    storage: number;
  };
  paymentMethod: {
    type: string;
    last4: string;
    expiry: string;
  };
}

interface Invoice {
  id: string;
  date: string;
  amount: number;
  status: 'paid' | 'pending' | 'failed';
  description: string;
}

const Billing = () => {
  const [billingInfo, setBillingInfo] = useState<BillingInfo>({
    currentPlan: "Professional",
    planPrice: 99,
    billingCycle: "monthly",
    nextBillingDate: "2024-02-15",
    usage: {
      patients: 245,
      consultations: 1200,
      storage: 2.5
    },
    limits: {
      patients: 500,
      consultations: 2000,
      storage: 10
    },
    paymentMethod: {
      type: "Visa",
      last4: "4242",
      expiry: "12/25"
    }
  });

  const [invoices] = useState<Invoice[]>([
    {
      id: "INV-2024-001",
      date: "2024-01-15",
      amount: 99.00,
      status: "paid",
      description: "Professional Plan - January 2024"
    },
    {
      id: "INV-2023-012",
      date: "2023-12-15",
      amount: 99.00,
      status: "paid",
      description: "Professional Plan - December 2023"
    },
    {
      id: "INV-2023-011",
      date: "2023-11-15",
      amount: 99.00,
      status: "paid",
      description: "Professional Plan - November 2023"
    }
  ]);

  const getUsagePercentage = (current: number, limit: number) => {
    return Math.min((current / limit) * 100, 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage < 70) return 'bg-green-500';
    if (percentage < 90) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const plans = [
    {
      name: "Starter",
      price: 29,
      features: [
        "Up to 100 patients",
        "500 consultations/month",
        "5GB storage",
        "Basic AI features",
        "Email support"
      ],
      popular: false
    },
    {
      name: "Professional",
      price: 99,
      features: [
        "Up to 500 patients",
        "2000 consultations/month",
        "10GB storage",
        "Advanced AI features",
        "Priority support",
        "Custom integrations"
      ],
      popular: true
    },
    {
      name: "Enterprise",
      price: 299,
      features: [
        "Unlimited patients",
        "Unlimited consultations",
        "100GB storage",
        "Custom AI models",
        "24/7 support",
        "White-label solution",
        "API access"
      ],
      popular: false
    }
  ];

  const { user } = useAuth();
  const remaining = user?._id ? creditsService.getRemaining(user._id) : 0;
  const used = user?._id ? creditsService.getUsed(user._id) : 0;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold gradient-text">Billing & Subscription</h1>
          <p className="text-gray-600">Manage your subscription and payment methods</p>
        </div>
        <Button className="gradient-primary">
          <Plus className="w-4 h-4 mr-2" />
          Upgrade Plan
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Current Plan */}
        <div className="lg:col-span-2">
          <Card className="modern-card">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Current Plan</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Active
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-2xl font-bold">{billingInfo.currentPlan} Plan</h3>
                  <p className="text-gray-600">${billingInfo.planPrice}/month</p>
                  <div className="mt-2 flex items-center gap-2 text-sm text-gray-700">
                    <Brain className="w-4 h-4" />
                    <span>AI Credits Remaining:</span>
                    <Badge>{remaining.toLocaleString()}</Badge>
                    <span className="ml-2 text-xs text-gray-500">Used: {used.toLocaleString()}</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Next billing</p>
                  <p className="font-semibold">{new Date(billingInfo.nextBillingDate).toLocaleDateString()}</p>
                </div>
              </div>

              {/* Usage Metrics */}
              <div className="space-y-4">
                <h4 className="font-semibold">Usage This Month</h4>
                
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Patients</span>
                      <span>{billingInfo.usage.patients} / {billingInfo.limits.patients}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${getUsageColor(getUsagePercentage(billingInfo.usage.patients, billingInfo.limits.patients))}`}
                        style={{ width: `${getUsagePercentage(billingInfo.usage.patients, billingInfo.limits.patients)}%` }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Consultations</span>
                      <span>{billingInfo.usage.consultations} / {billingInfo.limits.consultations}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${getUsageColor(getUsagePercentage(billingInfo.usage.consultations, billingInfo.limits.consultations))}`}
                        style={{ width: `${getUsagePercentage(billingInfo.usage.consultations, billingInfo.limits.consultations)}%` }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Storage</span>
                      <span>{billingInfo.usage.storage}GB / {billingInfo.limits.storage}GB</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${getUsageColor(getUsagePercentage(billingInfo.usage.storage, billingInfo.limits.storage))}`}
                        style={{ width: `${getUsagePercentage(billingInfo.usage.storage, billingInfo.limits.storage)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Payment Method */}
        <Card className="modern-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CreditCard className="w-5 h-5" />
              <span>Payment Method</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <CreditCard className="w-6 h-6 text-blue-600" />
              <div>
                <p className="font-semibold">{billingInfo.paymentMethod.type} •••• {billingInfo.paymentMethod.last4}</p>
                <p className="text-sm text-gray-600">Expires {billingInfo.paymentMethod.expiry}</p>
              </div>
            </div>
            <Button variant="outline" className="w-full">
              <Settings className="w-4 h-4 mr-2" />
              Update Payment Method
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Available Plans */}
      <Card className="modern-card">
        <CardHeader>
          <CardTitle>Available Plans</CardTitle>
          <CardDescription>Choose the plan that best fits your needs</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {plans.map((plan, index) => (
              <Card key={index} className={`modern-card relative ${plan.popular ? 'ring-2 ring-green-500' : ''}`}>
                {plan.popular && (
                  <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-green-500">
                    Most Popular
                  </Badge>
                )}
                <CardHeader>
                  <CardTitle className="text-center">{plan.name}</CardTitle>
                  <div className="text-center">
                    <span className="text-3xl font-bold">${plan.price}</span>
                    <span className="text-gray-600">/month</span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button 
                    className={`w-full ${plan.popular ? 'gradient-primary' : 'variant-outline'}`}
                    variant={plan.popular ? 'default' : 'outline'}
                  >
                    {plan.name === billingInfo.currentPlan ? 'Current Plan' : 'Choose Plan'}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Billing History */}
      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Billing History</span>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {invoices.map((invoice) => (
              <div key={invoice.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <Receipt className="w-5 h-5 text-gray-600" />
                  <div>
                    <p className="font-semibold">{invoice.description}</p>
                    <p className="text-sm text-gray-600">{invoice.id} • {new Date(invoice.date).toLocaleDateString()}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="font-semibold">${invoice.amount.toFixed(2)}</span>
                  <Badge 
                    variant="secondary" 
                    className={
                      invoice.status === 'paid' ? 'bg-green-100 text-green-800' :
                      invoice.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }
                  >
                    {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                  </Badge>
                  <Button variant="ghost" size="sm">
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Billing Support */}
      <Card className="modern-card">
        <CardHeader>
          <CardTitle>Need Help?</CardTitle>
          <CardDescription>Our billing team is here to help you</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-semibold mb-2">Contact Support</h4>
              <p className="text-sm text-gray-600 mb-3">Get help with billing questions</p>
              <Button variant="outline" className="w-full">
                Contact Support
              </Button>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-semibold mb-2">Billing FAQ</h4>
              <p className="text-sm text-gray-600 mb-3">Find answers to common questions</p>
              <Button variant="outline" className="w-full">
                View FAQ
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Billing;
