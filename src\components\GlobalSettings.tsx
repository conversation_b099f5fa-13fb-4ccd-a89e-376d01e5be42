import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Palette, 
  Globe, 
  Shield, 
  Mail, 
  Zap, 
  Code, 
  Eye,
  Upload,
  Save,
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const GlobalSettings = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  
  // UI Settings State
  const [uiSettings, setUiSettings] = useState({
    primaryColor: '#0070f3',
    secondaryColor: '#10b981',
    accentColor: '#f59e0b',
    logo: '',
    siteName: 'MEDORA',
    favicon: '',
    theme: 'system',
    darkMode: true,
    compactMode: false
  });

  // SEO Settings State
  const [seoSettings, setSeoSettings] = useState({
    siteTitle: 'MEDORA - AI-Powered Medical Consultant',
    metaDescription: 'Professional medical AI consultant for diagnosis, consultations, and prescription management',
    keywords: 'medical AI, medical consultant, diagnosis, prescription, healthcare',
    ogImage: '',
    twitterHandle: '@medora_ai',
    googleAnalytics: '',
    googleSearchConsole: '',
    robotsTxt: `User-agent: *
Allow: /
Sitemap: https://medora.ai/sitemap.xml`
  });

  // App Configuration State
  const [appSettings, setAppSettings] = useState({
    maintenanceMode: false,
    registrationEnabled: true,
    emailVerificationRequired: true,
    maxUsersPerPlan: {
      starter: 1,
      professional: 5,
      enterprise: 100
    },
    rateLimiting: {
      enabled: true,
      requestsPerMinute: 60,
      requestsPerHour: 1000
    },
    features: {
      aiConsultations: true,
      documentGeneration: true,
      voiceQueries: true,
      multiLanguage: true,
      apiAccess: true
    }
  });

  // Security Settings State
  const [securitySettings, setSecuritySettings] = useState({
    passwordMinLength: 8,
    requireSpecialChars: true,
    requireNumbers: true,
    sessionTimeout: 24,
    maxLoginAttempts: 5,
    twoFactorRequired: false,
    ipWhitelist: '',
    corsOrigins: 'https://medora.ai,https://app.medora.ai'
  });

  // Email Settings State
  const [emailSettings, setEmailSettings] = useState({
    provider: 'sendgrid',
    smtpHost: '',
    smtpPort: 587,
    smtpUser: '',
    smtpPassword: '',
    fromEmail: '<EMAIL>',
    fromName: 'MEDORA',
    templates: {
      welcome: 'Welcome to MEDORA!',
      passwordReset: 'Reset your password',
      paymentSuccess: 'Payment confirmed'
    }
  });

  const handleSave = async (section: string) => {
    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    toast({
      title: "Settings Saved",
      description: `${section} settings have been updated successfully.`,
    });
    
    setIsLoading(false);
  };

  const ColorPicker = ({ value, onChange, label }: { value: string; onChange: (color: string) => void; label: string }) => (
    <div className="space-y-2">
      <Label>{label}</Label>
      <div className="flex items-center gap-2">
        <input
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-12 h-10 rounded border"
        />
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="#000000"
          className="font-mono"
        />
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
              <Settings className="h-8 w-8" />
              Global Settings
            </h1>
            <p className="text-muted-foreground">Configure system-wide settings and preferences</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset to Defaults
            </Button>
            <Button>
              <Save className="h-4 w-4 mr-2" />
              Save All
            </Button>
          </div>
        </div>

        <Tabs defaultValue="ui" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="ui">UI & Branding</TabsTrigger>
            <TabsTrigger value="seo">SEO</TabsTrigger>
            <TabsTrigger value="app">App Config</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="integrations">Integrations</TabsTrigger>
          </TabsList>

          <TabsContent value="ui" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5" />
                    Color Scheme
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ColorPicker
                    value={uiSettings.primaryColor}
                    onChange={(color) => setUiSettings(prev => ({ ...prev, primaryColor: color }))}
                    label="Primary Color"
                  />
                  <ColorPicker
                    value={uiSettings.secondaryColor}
                    onChange={(color) => setUiSettings(prev => ({ ...prev, secondaryColor: color }))}
                    label="Secondary Color"
                  />
                  <ColorPicker
                    value={uiSettings.accentColor}
                    onChange={(color) => setUiSettings(prev => ({ ...prev, accentColor: color }))}
                    label="Accent Color"
                  />
                  <div className="space-y-2">
                    <Label>Theme</Label>
                    <Select value={uiSettings.theme} onValueChange={(value) => setUiSettings(prev => ({ ...prev, theme: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="system">System</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Branding</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="siteName">Site Name</Label>
                    <Input
                      id="siteName"
                      value={uiSettings.siteName}
                      onChange={(e) => setUiSettings(prev => ({ ...prev, siteName: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="logo">Logo URL</Label>
                    <div className="flex gap-2">
                      <Input
                        id="logo"
                        value={uiSettings.logo}
                        onChange={(e) => setUiSettings(prev => ({ ...prev, logo: e.target.value }))}
                        placeholder="https://example.com/logo.png"
                      />
                      <Button variant="outline">
                        <Upload className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="favicon">Favicon URL</Label>
                    <div className="flex gap-2">
                      <Input
                        id="favicon"
                        value={uiSettings.favicon}
                        onChange={(e) => setUiSettings(prev => ({ ...prev, favicon: e.target.value }))}
                        placeholder="https://example.com/favicon.ico"
                      />
                      <Button variant="outline">
                        <Upload className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="darkMode">Enable Dark Mode</Label>
                    <Switch
                      id="darkMode"
                      checked={uiSettings.darkMode}
                      onCheckedChange={(checked) => setUiSettings(prev => ({ ...prev, darkMode: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="compactMode">Compact Mode</Label>
                    <Switch
                      id="compactMode"
                      checked={uiSettings.compactMode}
                      onCheckedChange={(checked) => setUiSettings(prev => ({ ...prev, compactMode: checked }))}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="flex justify-end">
              <Button onClick={() => handleSave('UI & Branding')} disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save UI Settings'}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="seo" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  SEO Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="siteTitle">Site Title</Label>
                    <Input
                      id="siteTitle"
                      value={seoSettings.siteTitle}
                      onChange={(e) => setSeoSettings(prev => ({ ...prev, siteTitle: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="twitterHandle">Twitter Handle</Label>
                    <Input
                      id="twitterHandle"
                      value={seoSettings.twitterHandle}
                      onChange={(e) => setSeoSettings(prev => ({ ...prev, twitterHandle: e.target.value }))}
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="metaDescription">Meta Description</Label>
                  <Textarea
                    id="metaDescription"
                    value={seoSettings.metaDescription}
                    onChange={(e) => setSeoSettings(prev => ({ ...prev, metaDescription: e.target.value }))}
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {seoSettings.metaDescription.length}/160 characters
                  </p>
                </div>

                <div>
                  <Label htmlFor="keywords">Keywords</Label>
                  <Input
                    id="keywords"
                    value={seoSettings.keywords}
                    onChange={(e) => setSeoSettings(prev => ({ ...prev, keywords: e.target.value }))}
                    placeholder="keyword1, keyword2, keyword3"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="googleAnalytics">Google Analytics ID</Label>
                    <Input
                      id="googleAnalytics"
                      value={seoSettings.googleAnalytics}
                      onChange={(e) => setSeoSettings(prev => ({ ...prev, googleAnalytics: e.target.value }))}
                      placeholder="GA-XXXXXXXXX-X"
                    />
                  </div>
                  <div>
                    <Label htmlFor="googleSearchConsole">Search Console ID</Label>
                    <Input
                      id="googleSearchConsole"
                      value={seoSettings.googleSearchConsole}
                      onChange={(e) => setSeoSettings(prev => ({ ...prev, googleSearchConsole: e.target.value }))}
                      placeholder="google-site-verification=..."
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="robotsTxt">Robots.txt</Label>
                  <Textarea
                    id="robotsTxt"
                    value={seoSettings.robotsTxt}
                    onChange={(e) => setSeoSettings(prev => ({ ...prev, robotsTxt: e.target.value }))}
                    rows={6}
                    className="font-mono text-sm"
                  />
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button onClick={() => handleSave('SEO')} disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save SEO Settings'}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="app" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    Application Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Maintenance Mode</Label>
                      <p className="text-sm text-muted-foreground">Temporarily disable site access</p>
                    </div>
                    <Switch
                      checked={appSettings.maintenanceMode}
                      onCheckedChange={(checked) => setAppSettings(prev => ({ ...prev, maintenanceMode: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>User Registration</Label>
                      <p className="text-sm text-muted-foreground">Allow new user registrations</p>
                    </div>
                    <Switch
                      checked={appSettings.registrationEnabled}
                      onCheckedChange={(checked) => setAppSettings(prev => ({ ...prev, registrationEnabled: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Email Verification Required</Label>
                      <p className="text-sm text-muted-foreground">Require email verification for new users</p>
                    </div>
                    <Switch
                      checked={appSettings.emailVerificationRequired}
                      onCheckedChange={(checked) => setAppSettings(prev => ({ ...prev, emailVerificationRequired: checked }))}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Feature Flags</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {Object.entries(appSettings.features).map(([feature, enabled]) => (
                    <div key={feature} className="flex items-center justify-between">
                      <div>
                        <Label className="capitalize">{feature.replace(/([A-Z])/g, ' $1')}</Label>
                      </div>
                      <Switch
                        checked={enabled}
                        onCheckedChange={(checked) => 
                          setAppSettings(prev => ({
                            ...prev,
                            features: { ...prev.features, [feature]: checked }
                          }))
                        }
                      />
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Rate Limiting</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <Label>Enable Rate Limiting</Label>
                  <Switch
                    checked={appSettings.rateLimiting.enabled}
                    onCheckedChange={(checked) => 
                      setAppSettings(prev => ({
                        ...prev,
                        rateLimiting: { ...prev.rateLimiting, enabled: checked }
                      }))
                    }
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Requests per Minute</Label>
                    <Input
                      type="number"
                      value={appSettings.rateLimiting.requestsPerMinute}
                      onChange={(e) => 
                        setAppSettings(prev => ({
                          ...prev,
                          rateLimiting: { ...prev.rateLimiting, requestsPerMinute: parseInt(e.target.value) }
                        }))
                      }
                    />
                  </div>
                  <div>
                    <Label>Requests per Hour</Label>
                    <Input
                      type="number"
                      value={appSettings.rateLimiting.requestsPerHour}
                      onChange={(e) => 
                        setAppSettings(prev => ({
                          ...prev,
                          rateLimiting: { ...prev.rateLimiting, requestsPerHour: parseInt(e.target.value) }
                        }))
                      }
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button onClick={() => handleSave('App Configuration')} disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save App Settings'}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Security Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Password Minimum Length</Label>
                    <Input
                      type="number"
                      value={securitySettings.passwordMinLength}
                      onChange={(e) => setSecuritySettings(prev => ({ ...prev, passwordMinLength: parseInt(e.target.value) }))}
                    />
                  </div>
                  <div>
                    <Label>Session Timeout (hours)</Label>
                    <Input
                      type="number"
                      value={securitySettings.sessionTimeout}
                      onChange={(e) => setSecuritySettings(prev => ({ ...prev, sessionTimeout: parseInt(e.target.value) }))}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Require Special Characters</Label>
                    <Switch
                      checked={securitySettings.requireSpecialChars}
                      onCheckedChange={(checked) => setSecuritySettings(prev => ({ ...prev, requireSpecialChars: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>Require Numbers</Label>
                    <Switch
                      checked={securitySettings.requireNumbers}
                      onCheckedChange={(checked) => setSecuritySettings(prev => ({ ...prev, requireNumbers: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label>Two-Factor Authentication Required</Label>
                    <Switch
                      checked={securitySettings.twoFactorRequired}
                      onCheckedChange={(checked) => setSecuritySettings(prev => ({ ...prev, twoFactorRequired: checked }))}
                    />
                  </div>
                </div>

                <div>
                  <Label>CORS Origins</Label>
                  <Input
                    value={securitySettings.corsOrigins}
                    onChange={(e) => setSecuritySettings(prev => ({ ...prev, corsOrigins: e.target.value }))}
                    placeholder="https://domain1.com,https://domain2.com"
                  />
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button onClick={() => handleSave('Security')} disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Security Settings'}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="email" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  Email Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Email Provider</Label>
                  <Select value={emailSettings.provider} onValueChange={(value) => setEmailSettings(prev => ({ ...prev, provider: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sendgrid">SendGrid</SelectItem>
                      <SelectItem value="mailgun">Mailgun</SelectItem>
                      <SelectItem value="smtp">Custom SMTP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>From Email</Label>
                    <Input
                      value={emailSettings.fromEmail}
                      onChange={(e) => setEmailSettings(prev => ({ ...prev, fromEmail: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label>From Name</Label>
                    <Input
                      value={emailSettings.fromName}
                      onChange={(e) => setEmailSettings(prev => ({ ...prev, fromName: e.target.value }))}
                    />
                  </div>
                </div>

                {emailSettings.provider === 'smtp' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg">
                    <div>
                      <Label>SMTP Host</Label>
                      <Input
                        value={emailSettings.smtpHost}
                        onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpHost: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label>SMTP Port</Label>
                      <Input
                        type="number"
                        value={emailSettings.smtpPort}
                        onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpPort: parseInt(e.target.value) }))}
                      />
                    </div>
                    <div>
                      <Label>SMTP Username</Label>
                      <Input
                        value={emailSettings.smtpUser}
                        onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpUser: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label>SMTP Password</Label>
                      <Input
                        type="password"
                        value={emailSettings.smtpPassword}
                        onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpPassword: e.target.value }))}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button onClick={() => handleSave('Email')} disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Email Settings'}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="integrations" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  Third-party Integrations
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">Stripe</h3>
                      <Badge variant="default">Connected</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">Payment processing</p>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">PayPal</h3>
                      <Badge variant="secondary">Disconnected</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">Alternative payments</p>
                    <Button variant="outline" size="sm">Connect</Button>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">Google Analytics</h3>
                      <Badge variant="default">Connected</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">Website analytics</p>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">Intercom</h3>
                      <Badge variant="secondary">Disconnected</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">Customer support</p>
                    <Button variant="outline" size="sm">Connect</Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button onClick={() => handleSave('Integrations')} disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Integration Settings'}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default GlobalSettings;