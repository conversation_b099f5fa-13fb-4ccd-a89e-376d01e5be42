import React from 'react';
import { motion } from 'framer-motion';
import NurseDashboard from '@/components/dashboards/NurseDashboard';
import { useAuth } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';

const NurseDashboardPage: React.FC = () => {
  const { user, isAuthenticated, loading } = useAuth();

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-teal-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-teal-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/nurse-login" replace />;
  }

  // Redirect if wrong role
  if (user?.role !== 'nurse') {
    const redirectPath = user?.role === 'patient' ? '/patient-dashboard' : 
                        user?.role === 'doctor' ? '/doctor-dashboard' : 
                        user?.role === 'admin' ? '/admin-dashboard' : '/nurse-login';
    return <Navigate to={redirectPath} replace />;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.5 }}
    >
      <NurseDashboard />
    </motion.div>
  );
};

export default NurseDashboardPage;
