# MEDORA AI Backend - Vercel Deployment Guide

## 🚀 Deploying to Vercel

This guide will help you deploy the MEDORA AI Backend to Vercel with proper configuration.

## Prerequisites

- Vercel account
- GitHub repository (already set up)
- MongoDB Atlas database
- OpenAI API key
- Paystack account (for payments)

## Step 1: Prepare Your Repository

The repository is already configured with `vercel.json` for Vercel deployment.

## Step 2: Connect to Vercel

### Option A: Vercel Dashboard
1. Go to [vercel.com](https://vercel.com)
2. Sign in with your GitHub account
3. Click "New Project"
4. Import your repository: `obibiifeanyi/medora-ai-backend`
5. Configure the project settings

### Option B: Vercel CLI
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy from your project directory
vercel

# Follow the prompts to configure your project
```

## Step 3: Configure Environment Variables

In your Vercel project dashboard, go to **Settings > Environment Variables** and add:

### Required Variables
```env
NODE_ENV=production
MONGODB_URI=mongodb+srv://username:<EMAIL>/medora
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
SESSION_SECRET=your-session-secret-key-here-also-make-it-random
OPENAI_API_KEY=sk-your-openai-api-key-here
FRONTEND_URL=https://www.medoraai.me
CORS_ORIGIN=https://www.medoraai.me,https://medoraai.me
```

### Payment Configuration
```env
PAYSTACK_SECRET_KEY=sk_live_your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=pk_live_your_paystack_public_key
```

### Email Configuration (Optional)
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### OAuth Configuration (Optional)
```env
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
```

## Step 4: Deploy

### Automatic Deployment
Once connected to GitHub, Vercel will automatically deploy on every push to the main branch.

### Manual Deployment
```bash
vercel --prod
```

## Step 5: Configure Custom Domain (Optional)

1. In Vercel dashboard, go to **Settings > Domains**
2. Add your custom domain (e.g., `api.medoraai.me`)
3. Configure DNS records as instructed by Vercel

## Step 6: Verify Deployment

### Health Check
Visit your deployed URL + `/api/health`:
```
https://medora-ai-backend.vercel.app/api/health
```

Expected response:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "uptime": 3600,
    "version": "1.0.0"
  }
}
```

### Test API Endpoints
```bash
# Test authentication endpoint
curl -X POST https://medora-ai-backend.vercel.app/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","firstName":"Test","lastName":"User","role":"patient"}'
```

## Vercel Configuration Details

### vercel.json Explanation
```json
{
  "version": 2,
  "builds": [
    {
      "src": "simple-server.js",
      "use": "@vercel/node"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/simple-server.js"
    }
  ],
  "functions": {
    "simple-server.js": {
      "maxDuration": 30
    }
  }
}
```

- **builds**: Specifies how to build the application
- **routes**: Routes all requests to the main server file
- **functions**: Configures function timeout (30 seconds max for Pro plan)

## Environment Variables Security

### ⚠️ Important Security Notes:
1. **Never commit sensitive data** to your repository
2. **Use Vercel Environment Variables** for all secrets
3. **Rotate API keys regularly**
4. **Use different keys** for development and production

### Setting Environment Variables in Vercel:
1. Go to your project dashboard
2. Navigate to **Settings > Environment Variables**
3. Add each variable with appropriate scope:
   - **Production**: For live deployment
   - **Preview**: For preview deployments
   - **Development**: For local development

## Monitoring and Logs

### View Logs
1. Go to your Vercel project dashboard
2. Click on **Functions** tab
3. View real-time logs and errors

### Performance Monitoring
- Monitor function execution time
- Check memory usage
- Review error rates

## Troubleshooting

### Common Issues

1. **Function Timeout**
   - Increase `maxDuration` in `vercel.json`
   - Optimize database queries
   - Use connection pooling

2. **Environment Variables Not Loading**
   - Check variable names match exactly
   - Ensure variables are set for correct environment
   - Redeploy after adding variables

3. **CORS Errors**
   - Verify `CORS_ORIGIN` includes your frontend URL
   - Check frontend is making requests to correct backend URL

4. **Database Connection Issues**
   - Verify MongoDB URI is correct
   - Check MongoDB Atlas IP whitelist (allow all: 0.0.0.0/0)
   - Ensure database user has proper permissions

### Debug Mode
Add to environment variables for debugging:
```env
DEBUG=true
LOG_LEVEL=debug
```

## Scaling Considerations

### Vercel Limits (Hobby Plan)
- **Function Duration**: 10 seconds
- **Function Memory**: 1024 MB
- **Bandwidth**: 100 GB/month
- **Executions**: 100 GB-hours/month

### Vercel Pro Plan
- **Function Duration**: 60 seconds
- **Function Memory**: 3008 MB
- **Bandwidth**: 1 TB/month
- **Executions**: 1000 GB-hours/month

## Production Checklist

- [ ] Environment variables configured
- [ ] Database connection tested
- [ ] API endpoints responding
- [ ] CORS configured correctly
- [ ] Payment integration tested
- [ ] Email notifications working
- [ ] Error monitoring set up
- [ ] Custom domain configured (optional)
- [ ] SSL certificate active
- [ ] Performance monitoring enabled

## Support

For deployment issues:
- **Vercel Documentation**: https://vercel.com/docs
- **MEDORA Support**: <EMAIL>
- **GitHub Issues**: https://github.com/obibiifeanyi/medora-ai-backend/issues

---

**Deployment URL**: https://medora-ai-backend.vercel.app
**Custom Domain**: https://api.medoraai.me (if configured)
**Status Page**: https://medora-ai-backend.vercel.app/api/health
