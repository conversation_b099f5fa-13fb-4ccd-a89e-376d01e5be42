import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle2, XCircle, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { paymentService } from '@/services/paymentService';
import { creditsService, type PlanInfo } from '@/services/creditsService';
import { useAuth } from '@/hooks/useAuth';

const planCredits: Record<string, number> = { 'Basic': 1000, 'Professional': 5000, 'Enterprise': 50000 };

export default function PaymentCallback() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [status, setStatus] = useState<'pending'|'success'|'failed'>('pending');
  const [message, setMessage] = useState('Verifying your payment...');

  useEffect(() => {
    const verify = async () => {
      try {
        const params = new URLSearchParams(window.location.search);
        const provider = params.get('provider');
        if (!provider) throw new Error('Missing provider');

        // Retrieve pending plan stored before redirect
        const pendingRaw = localStorage.getItem('pendingPlan');
        const pending = pendingRaw ? JSON.parse(pendingRaw) : null;

        if (provider === 'paystack') {
          const reference = params.get('reference');
          if (!reference) throw new Error('Missing reference');
          const res = await paymentService.verifyPaystack(reference);
          if (res.success && res.data?.status === 'success') {
            completeSubscription(pending);
          } else {
            throw new Error(res.error || 'Payment verification failed');
          }
        } else if (provider === 'flutterwave') {
          const transaction_id = params.get('transaction_id');
          if (!transaction_id) throw new Error('Missing transaction_id');
          const res = await paymentService.verifyFlutterwave(transaction_id);
          if (res.success && (res.data?.status === 'successful' || res.data?.status === 'success')) {
            completeSubscription(pending);
          } else {
            throw new Error(res.error || 'Payment verification failed');
          }
        } else {
          throw new Error('Unsupported provider');
        }
      } catch (err) {
        console.error('Callback error:', err);
        setStatus('failed');
        setMessage(err instanceof Error ? err.message : 'Payment verification failed');
        toast.error('Payment verification failed');
      }
    };

    const completeSubscription = (pending: any) => {
      try {
        const planName = pending?.name || 'Professional';
        const cycle = pending?.cycle || 'monthly';
        const credits = planCredits[planName] ?? 1000;
        if (user?._id) {
          const plan: PlanInfo = { name: planName, monthlyCredits: credits, cycle };
          creditsService.setPlan(user._id, plan);
          creditsService.resetCycle(user._id, plan);
        }
        localStorage.removeItem('pendingPlan');
        setStatus('success');
        setMessage(`Payment successful! ${planName} plan activated with ${credits.toLocaleString()} AI credits / month.`);
        toast.success('Payment successful!');
        setTimeout(() => navigate('/billing'), 1500);
      } catch (e) {
        console.error('Subscription completion error:', e);
        setStatus('failed');
        setMessage('Failed to activate subscription');
        toast.error('Failed to activate subscription');
      }
    };

    verify();
  }, [navigate, user?._id]);

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="max-w-lg w-full">
        <CardHeader>
          <CardTitle>Payment Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-center">
          {status === 'pending' && (
            <div className="flex flex-col items-center space-y-2">
              <Loader2 className="h-10 w-10 animate-spin" />
              <p>{message}</p>
            </div>
          )}
          {status === 'success' && (
            <div className="flex flex-col items-center space-y-2 text-green-600">
              <CheckCircle2 className="h-10 w-10" />
              <p>{message}</p>
              <Button onClick={() => navigate('/billing')}>Go to Billing</Button>
            </div>
          )}
          {status === 'failed' && (
            <div className="flex flex-col items-center space-y-2 text-red-600">
              <XCircle className="h-10 w-10" />
              <p>{message}</p>
              <Button variant="outline" onClick={() => navigate('/payment')}>Try Again</Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

