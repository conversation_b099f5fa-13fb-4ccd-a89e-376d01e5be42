import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, X, Star, Zap, Shield, Users, Brain, Stethoscope } from 'lucide-react';
import { toast } from 'sonner';

const Pricing = () => {
  const [billingCycle, setBillingCycle] = useState('monthly');

  const plans = [
    {
      name: 'Basic',
      description: 'Perfect for individual healthcare professionals',
      monthlyPrice: 29,
      yearlyPrice: 290,
      icon: Stethoscope,
      popular: false,
      features: [
        { name: 'Includes 1,000 AI credits / month', included: true },
        { name: 'Up to 50 patient consultations/month', included: true },
        { name: 'Basic symptom analysis', included: true },
        { name: 'Drug interaction checker', included: true },
        { name: 'Email support', included: true },
        { name: 'Basic reporting', included: true },
        { name: 'Advanced AI diagnostics', included: false },
        { name: 'Priority support', included: false },
        { name: 'Custom integrations', included: false },
        { name: 'Team collaboration', included: false }
      ]
    },
    {
      name: 'Professional',
      description: 'Ideal for medical practices and clinics',
      monthlyPrice: 79,
      yearlyPrice: 790,
      icon: Brain,
      popular: true,
      features: [
        { name: 'Includes 5,000 AI credits / month', included: true },
        { name: 'Up to 200 patient consultations/month', included: true },
        { name: 'Advanced AI diagnostics', included: true },
        { name: 'Comprehensive symptom analysis', included: true },
        { name: 'Drug interaction & allergy alerts', included: true },
        { name: 'Treatment recommendations', included: true },
        { name: 'Priority email & chat support', included: true },
        { name: 'Advanced analytics & reporting', included: true },
        { name: 'API access', included: true },
        { name: 'Team collaboration (up to 5 users)', included: true },
        { name: 'Custom integrations', included: false }
      ]
    },
    {
      name: 'Enterprise',
      description: 'For hospitals and large healthcare organizations',
      monthlyPrice: 199,
      yearlyPrice: 1990,
      icon: Shield,
      popular: false,
      features: [
        { name: 'Includes 50,000 AI credits / month', included: true },
        { name: 'Unlimited patient consultations', included: true },
        { name: 'Full AI diagnostic suite', included: true },
        { name: 'Advanced machine learning models', included: true },
        { name: 'Custom AI model training', included: true },
        { name: 'Dedicated account manager', included: true },
        { name: '24/7 phone & chat support', included: true },
        { name: 'Custom integrations & APIs', included: true },
        { name: 'Unlimited team members', included: true },
        { name: 'HIPAA compliance & security', included: true },
        { name: 'On-premise deployment option', included: true }
      ]
    }
  ];

  const handleSubscribe = (planName, price) => {
    // For now keep in-app checkout page, but you can also go straight to provider from here if desired
    const params = new URLSearchParams({ plan: planName, price: String(price), cycle: billingCycle });
    window.location.href = `/payment?${params.toString()}`;
  };

  const getPrice = (plan) => {
    return billingCycle === 'monthly' ? plan.monthlyPrice : plan.yearlyPrice;
  };

  const getSavings = (plan) => {
    const monthlyCost = plan.monthlyPrice * 12;
    const yearlyCost = plan.yearlyPrice;
    return monthlyCost - yearlyCost;
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-white">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <img 
                src="/lovable-uploads/logo-png.png" 
                alt="MEDORA Logo" 
                className="h-12 w-12 object-contain"
              />
              <span className="text-3xl font-bold font-bruno" style={{color: '#9ACD32'}}>MEDORA</span>
            </div>
            <h1 className="text-4xl font-bold mb-2 font-bruno" style={{color: '#9ACD32'}}>Choose Your Plan</h1>
            <p className="text-muted-foreground text-lg">
              Select the perfect plan for your medical practice and unlock the power of AI-driven healthcare
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Billing Toggle */}
        <div className="flex justify-center mb-12">
          <div className="bg-muted p-1 rounded-lg">
            <button
              onClick={() => setBillingCycle('monthly')}
              className={`px-6 py-2 rounded-md transition-all ${
                billingCycle === 'monthly'
                  ? 'text-white'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
              style={billingCycle === 'monthly' ? {backgroundColor: '#9ACD32'} : {}}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle('yearly')}
              className={`px-6 py-2 rounded-md transition-all ${
                billingCycle === 'yearly'
                  ? 'text-white'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
              style={billingCycle === 'yearly' ? {backgroundColor: '#9ACD32'} : {}}
            >
              Yearly
              <Badge className="ml-2 bg-green-100 text-green-800">Save up to 20%</Badge>
            </button>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => {
            const IconComponent = plan.icon;
            const price = getPrice(plan);
            const savings = getSavings(plan);
            
            return (
              <Card 
                key={index} 
                className={`relative ${plan.popular ? 'ring-2 ring-offset-2' : ''}`}
                style={plan.popular ? {ringColor: '#9ACD32'} : {}}
              >
                {plan.popular && (
                  <div 
                    className="absolute -top-3 left-1/2 transform -translate-x-1/2 px-4 py-1 rounded-full text-white text-sm font-medium"
                    style={{backgroundColor: '#9ACD32'}}
                  >
                    Most Popular
                  </div>
                )}
                
                <CardHeader className="text-center pb-4">
                  <div className="flex justify-center mb-4">
                    <div 
                      className="p-3 rounded-full"
                      style={{backgroundColor: '#9ACD32', opacity: 0.1}}
                    >
                      <IconComponent className="h-8 w-8" style={{color: '#9ACD32'}} />
                    </div>
                  </div>
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <p className="text-muted-foreground">{plan.description}</p>
                  
                  <div className="mt-4">
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold">${price}</span>
                      <span className="text-muted-foreground ml-1">/{billingCycle === 'monthly' ? 'month' : 'year'}</span>
                    </div>
                    {billingCycle === 'yearly' && savings > 0 && (
                      <p className="text-sm text-green-600 mt-1">
                        Save ${savings} per year
                      </p>
                    )}
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <Button 
                    className="w-full"
                    style={plan.popular ? {backgroundColor: '#9ACD32', color: 'white'} : {}}
                    variant={plan.popular ? 'default' : 'outline'}
                    onClick={() => handleSubscribe(plan.name, price)}
                  >
                    {plan.popular ? 'Get Started' : 'Choose Plan'}
                  </Button>
                  
                  <div className="space-y-3">
                    <h4 className="font-semibold">Features included:</h4>
                    {plan.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-start gap-3">
                        {feature.included ? (
                          <Check className="h-5 w-5 mt-0.5 flex-shrink-0" style={{color: '#9ACD32'}} />
                        ) : (
                          <X className="h-5 w-5 mt-0.5 text-muted-foreground flex-shrink-0" />
                        )}
                        <span className={`text-sm ${feature.included ? '' : 'text-muted-foreground'}`}>
                          {feature.name}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* FAQ Section */}
        <div className="mt-20 max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12" style={{color: '#9ACD32'}}>Frequently Asked Questions</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Is there a free trial?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Yes! We offer a 14-day free trial for all plans. No credit card required to get started.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Can I change plans anytime?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Absolutely. You can upgrade or downgrade your plan at any time. Changes take effect immediately.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Is my data secure?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Yes, we're HIPAA compliant and use enterprise-grade security. All data is encrypted in transit and at rest.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Do you offer custom solutions?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Yes, our Enterprise plan includes custom integrations and we can work with you on specialized requirements.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center">
          <Card className="max-w-2xl mx-auto">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold mb-4" style={{color: '#9ACD32'}}>Ready to get started?</h3>
              <p className="text-muted-foreground mb-6">
                Join thousands of healthcare professionals who trust MEDORA for AI-powered medical consultations.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="text-black dark:text-[#ADF802]"
                  style={{backgroundColor: '#9ACD32'}}
                  onClick={() => handleSubscribe('Professional', plans[1].monthlyPrice)}
                >
                  Start Free Trial
                </Button>
                <Button variant="outline" size="lg">
                  Contact Sales
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Pricing;