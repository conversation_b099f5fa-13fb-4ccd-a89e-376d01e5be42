// Simple client-side AI credits service (demo mode)
// Stores per-user plan and credits in localStorage

export type BillingCycle = 'monthly' | 'yearly';

export interface PlanInfo {
  name: string;
  monthlyCredits: number; // credits allocated per month (even for yearly plans)
  cycle: BillingCycle;
}

function lsKey(userId: string, suffix: string) {
  return `ai_${suffix}_${userId}`;
}

function getNow(): Date {
  return new Date();
}

function sameMonth(a: Date, b: Date) {
  return a.getFullYear() === b.getFullYear() && a.getMonth() === b.getMonth();
}

export const creditsService = {
  getPlan(userId: string): PlanInfo | null {
    const raw = localStorage.getItem(lsKey(userId, 'plan'));
    if (!raw) return null;
    try {
      return JSON.parse(raw);
    } catch {
      return null;
    }
  },

  setPlan(userId: string, plan: PlanInfo) {
    localStorage.setItem(lsKey(userId, 'plan'), JSON.stringify(plan));
    // If setting a plan for the first time, initialize credits for the cycle
    const initialized = localStorage.getItem(lsKey(userId, 'initialized'));
    if (!initialized) {
      this.resetCycle(userId, plan);
      localStorage.setItem(lsKey(userId, 'initialized'), '1');
    }
  },

  resetCycle(userId: string, plan?: PlanInfo) {
    const p = plan || this.getPlan(userId);
    if (!p) return;
    localStorage.setItem(lsKey(userId, 'remaining'), String(p.monthlyCredits));
    localStorage.setItem(lsKey(userId, 'used'), '0');
    localStorage.setItem(lsKey(userId, 'last_reset'), getNow().toISOString());
  },

  ensureMonthlyReset(userId: string) {
    const p = this.getPlan(userId);
    if (!p) return;
    const lastRaw = localStorage.getItem(lsKey(userId, 'last_reset'));
    if (!lastRaw) {
      this.resetCycle(userId, p);
      return;
    }
    const last = new Date(lastRaw);
    const now = getNow();
    // Calendar month reset
    if (!sameMonth(last, now)) {
      this.resetCycle(userId, p);
    }
  },

  getRemaining(userId: string): number {
    this.ensureMonthlyReset(userId);
    const v = localStorage.getItem(lsKey(userId, 'remaining'));
    return v ? parseInt(v, 10) : 0;
  },

  getUsed(userId: string): number {
    this.ensureMonthlyReset(userId);
    const v = localStorage.getItem(lsKey(userId, 'used'));
    return v ? parseInt(v, 10) : 0;
  },

  spend(userId: string, amount = 1): { success: boolean; remaining: number } {
    this.ensureMonthlyReset(userId);
    const remaining = this.getRemaining(userId);
    if (remaining < amount) {
      return { success: false, remaining };
    }
    const newRemaining = remaining - amount;
    localStorage.setItem(lsKey(userId, 'remaining'), String(newRemaining));
    const used = this.getUsed(userId);
    localStorage.setItem(lsKey(userId, 'used'), String(used + amount));
    return { success: true, remaining: newRemaining };
  },

  add(userId: string, amount: number) {
    this.ensureMonthlyReset(userId);
    const remaining = this.getRemaining(userId);
    localStorage.setItem(lsKey(userId, 'remaining'), String(remaining + amount));
  }
};

