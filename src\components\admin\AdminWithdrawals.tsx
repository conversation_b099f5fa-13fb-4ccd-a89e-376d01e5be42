import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Download,
  AlertTriangle,
  CreditCard,
  Building2,
  Calendar
} from 'lucide-react';

const AdminWithdrawals = () => {
  const [selectedStatus, setSelectedStatus] = useState('all');

  // Mock withdrawal data
  const withdrawals = [
    {
      id: 'WD001',
      userId: 'USR001',
      userName: 'Dr. <PERSON>',
      email: '<EMAIL>',
      amount: 2500.00,
      currency: 'USD',
      method: 'bank_transfer',
      bankName: 'Chase Bank',
      accountNumber: '****1234',
      status: 'pending',
      requestDate: '2024-01-20',
      reason: 'Monthly earnings withdrawal'
    },
    {
      id: 'WD002',
      userId: 'USR002',
      userName: '<PERSON>. <PERSON>',
      email: '<EMAIL>',
      amount: 1800.00,
      currency: 'USD',
      method: 'paypal',
      paypalEmail: '<EMAIL>',
      status: 'approved',
      requestDate: '2024-01-18',
      approvedDate: '2024-01-19',
      processedDate: '2024-01-19',
      reason: 'Consultation fees'
    },
    {
      id: 'WD003',
      userId: 'USR003',
      userName: 'Nurse <PERSON>',
      email: '<EMAIL>',
      amount: 750.00,
      currency: 'USD',
      method: 'bank_transfer',
      bankName: 'Bank of America',
      accountNumber: '****5678',
      status: 'rejected',
      requestDate: '2024-01-15',
      rejectedDate: '2024-01-17',
      rejectionReason: 'Insufficient balance',
      reason: 'Weekly earnings'
    },
    {
      id: 'WD004',
      userId: 'USR004',
      userName: 'Dr. Emily Davis',
      email: '<EMAIL>',
      amount: 3200.00,
      currency: 'USD',
      method: 'crypto',
      walletAddress: '**********************************',
      status: 'processing',
      requestDate: '2024-01-19',
      approvedDate: '2024-01-20',
      reason: 'Monthly consultation earnings'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'processing':
        return <Badge className="bg-blue-100 text-blue-800"><Eye className="w-3 h-3 mr-1" />Processing</Badge>;
      case 'approved':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Rejected</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'bank_transfer':
        return <Building2 className="w-4 h-4 text-blue-600" />;
      case 'paypal':
        return <CreditCard className="w-4 h-4 text-blue-600" />;
      case 'crypto':
        return <DollarSign className="w-4 h-4 text-orange-600" />;
      default:
        return <CreditCard className="w-4 h-4 text-gray-600" />;
    }
  };

  const filteredWithdrawals = withdrawals.filter(withdrawal => 
    selectedStatus === 'all' || withdrawal.status === selectedStatus
  );

  const stats = {
    total: withdrawals.length,
    pending: withdrawals.filter(w => w.status === 'pending').length,
    processing: withdrawals.filter(w => w.status === 'processing').length,
    approved: withdrawals.filter(w => w.status === 'approved').length,
    rejected: withdrawals.filter(w => w.status === 'rejected').length,
    totalAmount: withdrawals.reduce((sum, w) => sum + w.amount, 0),
    pendingAmount: withdrawals.filter(w => w.status === 'pending').reduce((sum, w) => sum + w.amount, 0)
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Withdrawal Management</h1>
          <p className="text-muted-foreground">Review and process withdrawal requests</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">All time</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">${stats.pendingAmount.toLocaleString()}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing</CardTitle>
            <Eye className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.processing}</div>
            <p className="text-xs text-muted-foreground">Being processed</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
            <p className="text-xs text-muted-foreground">Successfully paid</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
            <p className="text-xs text-muted-foreground">Failed requests</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">${stats.totalAmount.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">All requests</p>
          </CardContent>
        </Card>
      </div>

      {/* Withdrawal Requests */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Withdrawal Requests</CardTitle>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-input rounded-md bg-background"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="approved">Completed</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredWithdrawals.map((withdrawal) => (
              <div key={withdrawal.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-[#ADF802]/20 rounded-full flex items-center justify-center">
                      {getMethodIcon(withdrawal.method)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-semibold">{withdrawal.userName}</h3>
                        {getStatusBadge(withdrawal.status)}
                        <Badge variant="outline" className="text-xs">
                          {withdrawal.id}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{withdrawal.email}</p>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Amount:</span> ${withdrawal.amount.toLocaleString()} {withdrawal.currency}
                        </div>
                        <div>
                          <span className="font-medium">Method:</span> {withdrawal.method.replace('_', ' ')}
                        </div>
                        <div>
                          <span className="font-medium">Requested:</span> {withdrawal.requestDate}
                        </div>
                        <div>
                          <span className="font-medium">Reason:</span> {withdrawal.reason}
                        </div>
                      </div>
                      <div className="mt-2 text-sm">
                        {withdrawal.method === 'bank_transfer' && (
                          <div>
                            <span className="font-medium">Bank:</span> {withdrawal.bankName} - {withdrawal.accountNumber}
                          </div>
                        )}
                        {withdrawal.method === 'paypal' && (
                          <div>
                            <span className="font-medium">PayPal:</span> {withdrawal.paypalEmail}
                          </div>
                        )}
                        {withdrawal.method === 'crypto' && (
                          <div>
                            <span className="font-medium">Wallet:</span> {withdrawal.walletAddress}
                          </div>
                        )}
                      </div>
                      {withdrawal.rejectionReason && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                          <div className="flex items-center text-red-800 text-sm">
                            <AlertTriangle className="w-4 h-4 mr-1" />
                            <span className="font-medium">Rejection Reason:</span>
                          </div>
                          <p className="text-red-700 text-sm mt-1">{withdrawal.rejectionReason}</p>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-1" />
                      Details
                    </Button>
                    {withdrawal.status === 'pending' && (
                      <>
                        <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Approve
                        </Button>
                        <Button variant="destructive" size="sm">
                          <XCircle className="w-4 h-4 mr-1" />
                          Reject
                        </Button>
                      </>
                    )}
                    <Button variant="ghost" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminWithdrawals;
