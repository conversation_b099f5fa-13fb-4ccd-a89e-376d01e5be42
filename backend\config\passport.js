const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const FacebookStrategy = require('passport-facebook').Strategy;
const GitHubStrategy = require('passport-github2').Strategy;
const TwitterStrategy = require('passport-twitter').Strategy;
const LinkedInStrategy = require('passport-linkedin-oauth2').Strategy;
const User = require('../models/User');

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, user._id);
});

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findById(id);
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

// Google OAuth Strategy
if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
  passport.use(new GoogleStrategy({
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: `${process.env.OAUTH_CALLBACK_BASE_URL}/google/callback`,
    scope: ['profile', 'email']
  }, async (accessToken, refreshToken, profile, done) => {
    try {
      console.log('Google OAuth profile:', profile);
      
      // Check if user already exists with this Google ID
      let user = await User.findOne({ 'socialAuth.google.id': profile.id });
      
      if (user) {
        return done(null, user);
      }
      
      // Check if user exists with same email
      const email = profile.emails && profile.emails[0] ? profile.emails[0].value : null;
      if (email) {
        user = await User.findOne({ email: email.toLowerCase() });
        if (user) {
          // Link Google account to existing user
          user.socialAuth.google = {
            id: profile.id,
            email: email,
            verified: profile.emails[0].verified || false
          };
          if (!user.profilePicture.url && profile.photos && profile.photos[0]) {
            user.profilePicture = {
              url: profile.photos[0].value,
              provider: 'google'
            };
          }
          await user.save();
          return done(null, user);
        }
      }
      
      // Create new user
      const newUser = new User({
        email: email ? email.toLowerCase() : `google_${profile.id}@medora.temp`,
        firstName: profile.name.givenName || 'Google',
        lastName: profile.name.familyName || 'User',
        role: 'patient', // Default role for social auth users
        isVerified: profile.emails && profile.emails[0] ? profile.emails[0].verified : false,
        registrationMethod: 'google',
        socialAuth: {
          google: {
            id: profile.id,
            email: email,
            verified: profile.emails && profile.emails[0] ? profile.emails[0].verified : false
          }
        },
        profilePicture: profile.photos && profile.photos[0] ? {
          url: profile.photos[0].value,
          provider: 'google'
        } : {}
      });
      
      await newUser.save();
      done(null, newUser);
    } catch (error) {
      console.error('Google OAuth error:', error);
      done(error, null);
    }
  }));
}

// Facebook OAuth Strategy
if (process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET) {
  passport.use(new FacebookStrategy({
    clientID: process.env.FACEBOOK_APP_ID,
    clientSecret: process.env.FACEBOOK_APP_SECRET,
    callbackURL: `${process.env.OAUTH_CALLBACK_BASE_URL}/facebook/callback`,
    profileFields: ['id', 'emails', 'name', 'picture.type(large)']
  }, async (accessToken, refreshToken, profile, done) => {
    try {
      console.log('Facebook OAuth profile:', profile);
      
      let user = await User.findOne({ 'socialAuth.facebook.id': profile.id });
      
      if (user) {
        return done(null, user);
      }
      
      const email = profile.emails && profile.emails[0] ? profile.emails[0].value : null;
      if (email) {
        user = await User.findOne({ email: email.toLowerCase() });
        if (user) {
          user.socialAuth.facebook = {
            id: profile.id,
            email: email
          };
          if (!user.profilePicture.url && profile.photos && profile.photos[0]) {
            user.profilePicture = {
              url: profile.photos[0].value,
              provider: 'facebook'
            };
          }
          await user.save();
          return done(null, user);
        }
      }
      
      const newUser = new User({
        email: email ? email.toLowerCase() : `facebook_${profile.id}@medora.temp`,
        firstName: profile.name.givenName || 'Facebook',
        lastName: profile.name.familyName || 'User',
        role: 'patient',
        isVerified: false,
        registrationMethod: 'facebook',
        socialAuth: {
          facebook: {
            id: profile.id,
            email: email
          }
        },
        profilePicture: profile.photos && profile.photos[0] ? {
          url: profile.photos[0].value,
          provider: 'facebook'
        } : {}
      });
      
      await newUser.save();
      done(null, newUser);
    } catch (error) {
      console.error('Facebook OAuth error:', error);
      done(error, null);
    }
  }));
}

// GitHub OAuth Strategy
if (process.env.GITHUB_CLIENT_ID && process.env.GITHUB_CLIENT_SECRET) {
  passport.use(new GitHubStrategy({
    clientID: process.env.GITHUB_CLIENT_ID,
    clientSecret: process.env.GITHUB_CLIENT_SECRET,
    callbackURL: `${process.env.OAUTH_CALLBACK_BASE_URL}/github/callback`,
    scope: ['user:email']
  }, async (accessToken, refreshToken, profile, done) => {
    try {
      console.log('GitHub OAuth profile:', profile);
      
      let user = await User.findOne({ 'socialAuth.github.id': profile.id });
      
      if (user) {
        return done(null, user);
      }
      
      const email = profile.emails && profile.emails[0] ? profile.emails[0].value : null;
      if (email) {
        user = await User.findOne({ email: email.toLowerCase() });
        if (user) {
          user.socialAuth.github = {
            id: profile.id,
            username: profile.username,
            email: email
          };
          if (!user.profilePicture.url && profile.photos && profile.photos[0]) {
            user.profilePicture = {
              url: profile.photos[0].value,
              provider: 'github'
            };
          }
          await user.save();
          return done(null, user);
        }
      }
      
      const newUser = new User({
        email: email ? email.toLowerCase() : `github_${profile.username}@medora.temp`,
        firstName: profile.displayName ? profile.displayName.split(' ')[0] : profile.username,
        lastName: profile.displayName ? profile.displayName.split(' ').slice(1).join(' ') || 'User' : 'User',
        role: 'patient',
        isVerified: false,
        registrationMethod: 'github',
        socialAuth: {
          github: {
            id: profile.id,
            username: profile.username,
            email: email
          }
        },
        profilePicture: profile.photos && profile.photos[0] ? {
          url: profile.photos[0].value,
          provider: 'github'
        } : {}
      });
      
      await newUser.save();
      done(null, newUser);
    } catch (error) {
      console.error('GitHub OAuth error:', error);
      done(error, null);
    }
  }));
}

// Twitter OAuth Strategy
if (process.env.TWITTER_CLIENT_ID && process.env.TWITTER_CLIENT_SECRET) {
  passport.use(new TwitterStrategy({
    consumerKey: process.env.TWITTER_CLIENT_ID,
    consumerSecret: process.env.TWITTER_CLIENT_SECRET,
    callbackURL: `${process.env.OAUTH_CALLBACK_BASE_URL}/twitter/callback`,
    includeEmail: true
  }, async (token, tokenSecret, profile, done) => {
    try {
      console.log('Twitter OAuth profile:', profile);
      
      let user = await User.findOne({ 'socialAuth.twitter.id': profile.id });
      
      if (user) {
        return done(null, user);
      }
      
      const email = profile.emails && profile.emails[0] ? profile.emails[0].value : null;
      if (email) {
        user = await User.findOne({ email: email.toLowerCase() });
        if (user) {
          user.socialAuth.twitter = {
            id: profile.id,
            username: profile.username
          };
          if (!user.profilePicture.url && profile.photos && profile.photos[0]) {
            user.profilePicture = {
              url: profile.photos[0].value.replace('_normal', '_400x400'),
              provider: 'twitter'
            };
          }
          await user.save();
          return done(null, user);
        }
      }
      
      const newUser = new User({
        email: email ? email.toLowerCase() : `twitter_${profile.username}@medora.temp`,
        firstName: profile.displayName ? profile.displayName.split(' ')[0] : profile.username,
        lastName: profile.displayName ? profile.displayName.split(' ').slice(1).join(' ') || 'User' : 'User',
        role: 'patient',
        isVerified: false,
        registrationMethod: 'twitter',
        socialAuth: {
          twitter: {
            id: profile.id,
            username: profile.username
          }
        },
        profilePicture: profile.photos && profile.photos[0] ? {
          url: profile.photos[0].value.replace('_normal', '_400x400'),
          provider: 'twitter'
        } : {}
      });
      
      await newUser.save();
      done(null, newUser);
    } catch (error) {
      console.error('Twitter OAuth error:', error);
      done(error, null);
    }
  }));
}

// LinkedIn OAuth Strategy
if (process.env.LINKEDIN_CLIENT_ID && process.env.LINKEDIN_CLIENT_SECRET) {
  passport.use(new LinkedInStrategy({
    clientID: process.env.LINKEDIN_CLIENT_ID,
    clientSecret: process.env.LINKEDIN_CLIENT_SECRET,
    callbackURL: `${process.env.OAUTH_CALLBACK_BASE_URL}/linkedin/callback`,
    scope: ['r_emailaddress', 'r_liteprofile']
  }, async (accessToken, refreshToken, profile, done) => {
    try {
      console.log('LinkedIn OAuth profile:', profile);
      
      let user = await User.findOne({ 'socialAuth.linkedin.id': profile.id });
      
      if (user) {
        return done(null, user);
      }
      
      const email = profile.emails && profile.emails[0] ? profile.emails[0].value : null;
      if (email) {
        user = await User.findOne({ email: email.toLowerCase() });
        if (user) {
          user.socialAuth.linkedin = {
            id: profile.id,
            email: email
          };
          if (!user.profilePicture.url && profile.photos && profile.photos[0]) {
            user.profilePicture = {
              url: profile.photos[0].value,
              provider: 'linkedin'
            };
          }
          await user.save();
          return done(null, user);
        }
      }
      
      const newUser = new User({
        email: email ? email.toLowerCase() : `linkedin_${profile.id}@medora.temp`,
        firstName: profile.name.givenName || 'LinkedIn',
        lastName: profile.name.familyName || 'User',
        role: 'patient',
        isVerified: false,
        registrationMethod: 'linkedin',
        socialAuth: {
          linkedin: {
            id: profile.id,
            email: email
          }
        },
        profilePicture: profile.photos && profile.photos[0] ? {
          url: profile.photos[0].value,
          provider: 'linkedin'
        } : {}
      });
      
      await newUser.save();
      done(null, newUser);
    } catch (error) {
      console.error('LinkedIn OAuth error:', error);
      done(error, null);
    }
  }));
}

module.exports = passport;
