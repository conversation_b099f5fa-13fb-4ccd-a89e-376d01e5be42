# MEDORA AI Backend - Security Guide

## 🔒 Environment Variables Security

### ⚠️ CRITICAL SECURITY NOTICE

**NEVER commit sensitive data to your repository!** This includes:
- Database passwords
- API keys (OpenAI, Paystack, etc.)
- JWT secrets
- Email passwords
- OAuth client secrets

## 🛡️ Secure Configuration

### 1. Local Development
Use the provided `.env.example` as a template:
```bash
cp .env.example .env
# Edit .env with your actual values
```

### 2. Production Deployment (Vercel)
Set environment variables in Vercel dashboard:
1. Go to your project settings
2. Navigate to **Environment Variables**
3. Add each variable individually
4. Set appropriate scope (Production/Preview/Development)

### 3. Environment Variable Scopes

#### Production Variables
```env
NODE_ENV=production
MONGODB_URI=mongodb+srv://user:<EMAIL>/medora
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
SESSION_SECRET=your-session-secret-minimum-32-characters
OPENAI_API_KEY=sk-your-openai-api-key
PAYSTACK_SECRET_KEY=sk_live_your_paystack_secret
FRONTEND_URL=https://www.medoraai.me
CORS_ORIGIN=https://www.medoraai.me,https://medoraai.me
```

#### Development Variables
```env
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/medora-dev
JWT_SECRET=dev-jwt-secret-for-testing-only
SESSION_SECRET=dev-session-secret-for-testing-only
OPENAI_API_KEY=sk-your-openai-test-key
PAYSTACK_SECRET_KEY=sk_test_your_paystack_test_key
FRONTEND_URL=http://localhost:3000
CORS_ORIGIN=http://localhost:3000,http://localhost:5173
```

## 🔐 Security Best Practices

### 1. API Key Management
- **Rotate keys regularly** (monthly recommended)
- **Use different keys** for development and production
- **Monitor API usage** for unusual activity
- **Set up billing alerts** for API services

### 2. Database Security
- **Use MongoDB Atlas** with IP whitelisting
- **Enable authentication** on your database
- **Use strong passwords** (minimum 16 characters)
- **Regular backups** with encryption
- **Monitor database access logs**

### 3. JWT Security
- **Use strong secrets** (minimum 32 characters)
- **Set appropriate expiration** (7 days recommended)
- **Implement refresh tokens** for long-term access
- **Validate tokens** on every request
- **Blacklist compromised tokens**

### 4. CORS Configuration
```javascript
// Secure CORS setup
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'https://www.medoraai.me',
      'https://medoraai.me',
      // Add other trusted domains
    ];
    
    if (!origin || allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};
```

### 5. Rate Limiting
```javascript
// Implement rate limiting
const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});

app.use('/api/', limiter);
```

## 🚨 Security Incident Response

### If API Keys Are Compromised:
1. **Immediately revoke** the compromised keys
2. **Generate new keys** with different values
3. **Update environment variables** in all environments
4. **Monitor for unauthorized usage**
5. **Review access logs** for suspicious activity
6. **Notify relevant stakeholders**

### If Database Is Compromised:
1. **Change database passwords** immediately
2. **Review database access logs**
3. **Check for data exfiltration**
4. **Update connection strings**
5. **Implement additional security measures**
6. **Consider data breach notification** if required

## 🔍 Security Monitoring

### 1. Logging
Enable comprehensive logging:
```env
LOG_LEVEL=info
ENABLE_AUDIT_LOG=true
```

### 2. Error Tracking
Monitor for security-related errors:
- Failed authentication attempts
- Invalid JWT tokens
- CORS violations
- Rate limit exceeded
- Database connection failures

### 3. Performance Monitoring
Watch for unusual patterns:
- Sudden traffic spikes
- Unusual API usage patterns
- High error rates
- Slow response times

## 🛠️ Security Tools

### 1. Environment Variable Validation
```javascript
// Validate required environment variables
const requiredEnvVars = [
  'MONGODB_URI',
  'JWT_SECRET',
  'OPENAI_API_KEY',
  'FRONTEND_URL'
];

requiredEnvVars.forEach(envVar => {
  if (!process.env[envVar]) {
    console.error(`Missing required environment variable: ${envVar}`);
    process.exit(1);
  }
});
```

### 2. Security Headers
```javascript
// Security headers middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

### 3. Input Validation
```javascript
// Validate all inputs
const { body, validationResult } = require('express-validator');

app.post('/api/auth/login',
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 }),
  (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    // Process login
  }
);
```

## 📋 Security Checklist

### Development
- [ ] `.env` file in `.gitignore`
- [ ] No sensitive data in code
- [ ] Input validation implemented
- [ ] Error handling doesn't expose sensitive info
- [ ] HTTPS used for all external API calls

### Deployment
- [ ] Environment variables set in platform
- [ ] CORS configured for production domains
- [ ] Rate limiting enabled
- [ ] Security headers configured
- [ ] Database access restricted
- [ ] API keys rotated from development

### Monitoring
- [ ] Logging enabled
- [ ] Error tracking configured
- [ ] Performance monitoring active
- [ ] Security alerts set up
- [ ] Regular security reviews scheduled

## 📞 Security Contact

For security issues:
- **Email**: <EMAIL>
- **Response Time**: 24 hours for critical issues
- **PGP Key**: Available on request

## 🔗 Additional Resources

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Node.js Security Checklist](https://blog.risingstack.com/node-js-security-checklist/)
- [MongoDB Security Checklist](https://docs.mongodb.com/manual/administration/security-checklist/)
- [Vercel Security](https://vercel.com/docs/security)

---

**Remember**: Security is an ongoing process, not a one-time setup. Regularly review and update your security measures.
