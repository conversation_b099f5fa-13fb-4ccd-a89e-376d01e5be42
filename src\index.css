
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Mohave:ital,wght@0,300..700;1,300..700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Unica+One&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Bruno+Ace&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Mobile Typography */
@media (max-width: 768px) {
  body {
    font-weight: 500 !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 700 !important;
    letter-spacing: -0.025em;
  }

  p, span, div {
    font-weight: 500 !important;
    color: rgb(15 23 42) !important; /* slate-900 for light theme */
  }

  .dark p, .dark span, .dark div {
    color: rgb(248 250 252) !important; /* slate-50 for dark theme */
  }
}

/* Light theme text improvements */
.light {
  --foreground: 15 23 42; /* slate-900 */
  --muted-foreground: 51 65 85; /* slate-600 */
}

/* Enhanced button styles for mobile */
@media (max-width: 768px) {
  button {
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    padding: 0.75rem 1.5rem !important;
  }
}

:root {
  --background: 0 0% 98%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 97%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 97%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 78 100% 49%;
  --primary-foreground: 0 0% 0%;
  --secondary: 220 14.3% 95.9%;
  --secondary-foreground: 220.9 39.3% 11%;
  --muted: 220 14.3% 95.9%;
  --muted-foreground: 220 8.9% 46.1%;
  --accent: 220 14.3% 95.9%;
  --accent-foreground: 220.9 39.3% 11%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --ring: 78 100% 49%;
  --radius: 0.5rem;
}

.dark {
  --background: 0 0% 0%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 0%;
  --popover-foreground: 0 0% 98%;
  --primary: 78 100% 60%;
  --primary-foreground: 0 0% 0%;
  --secondary: 0 0% 8%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 8%;
  --muted-foreground: 0 0% 65%;
  --accent: 0 0% 12%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 12%;
  --input: 0 0% 8%;
  --ring: 78 100% 60%;
  --sidebar-background: 0 0% 0%;
  --sidebar-foreground: 0 0% 98%;
  --sidebar-primary: 78 100% 60%;
  --sidebar-primary-foreground: 0 0% 0%;
  --sidebar-accent: 0 0% 8%;
  --sidebar-accent-foreground: 0 0% 98%;
  --sidebar-border: 0 0% 12%;
  --sidebar-ring: 78 100% 60%;
}

.lemon {
  --background: 78 100% 98%;
  --foreground: 78 100% 10%;
  --card: 78 100% 96%;
  --card-foreground: 78 100% 10%;
  --popover: 78 100% 96%;
  --popover-foreground: 78 100% 10%;
  --primary: 78 100% 49%;
  --primary-foreground: 0 0% 0%;
  --secondary: 78 100% 90%;
  --secondary-foreground: 78 100% 15%;
  --muted: 78 100% 92%;
  --muted-foreground: 78 50% 35%;
  --accent: 78 100% 85%;
  --accent-foreground: 78 100% 15%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 78 100% 85%;
  --input: 78 100% 85%;
  --ring: 78 100% 49%;
  --sidebar-background: 78 100% 98%;
  --sidebar-foreground: 78 100% 10%;
  --sidebar-primary: 78 100% 49%;
  --sidebar-primary-foreground: 0 0% 0%;
  --sidebar-accent: 78 100% 90%;
  --sidebar-accent-foreground: 78 100% 15%;
  --sidebar-border: 78 100% 85%;
  --sidebar-ring: 78 100% 49%;
}

* {
  border-color: hsl(var(--border));
}

body {
  color: hsl(var(--foreground));
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  font-family: 'Mohave', sans-serif;
}

/* Custom gradient backgrounds */
.gradient-primary {
  background: #ADF802;
}

.gradient-secondary {
  background: #bef264;
}

.gradient-light {
  background: #f0fdf4;
}

.gradient-card {
  background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
  border: 2px solid #ADF802;
  box-shadow: 0 4px 6px -1px rgba(173, 248, 2, 0.2), 0 2px 4px -1px rgba(173, 248, 2, 0.1);
}

/* Remove neumorphic effects and replace with modern shadows */
.modern-card {
  background: hsl(var(--card));
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 2px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  color: hsl(var(--card-foreground));
}

/* Dark theme specific styles */
.dark .modern-card {
  background: #0a0a0a;
  box-shadow: 0 4px 6px -1px rgba(173, 248, 2, 0.15), 0 2px 4px -1px rgba(173, 248, 2, 0.1);
  border: 2px solid rgba(173, 248, 2, 0.3);
}

.dark .gradient-card {
  background: linear-gradient(135deg, #000000 0%, #0a0a0a 100%);
  border: 2px solid rgba(173, 248, 2, 0.3);
  box-shadow: 0 4px 6px -1px rgba(173, 248, 2, 0.15), 0 2px 4px -1px rgba(173, 248, 2, 0.1);
}

/* Dark theme body background */
.dark body {
  background: #000000;
  color: hsl(var(--foreground));
}

/* Dark theme input styles */
.dark .modern-input {
  background: #0a0a0a;
  border: 1px solid #1a1a1a;
  color: #ffffff;
}

.dark .modern-input:focus {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px rgba(173, 248, 2, 0.2);
}

/* Dark theme for all inputs */
.dark input,
.dark textarea,
.dark select {
  background: #0a0a0a !important;
  border-color: #1a1a1a !important;
  color: #ffffff !important;
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
  border-color: hsl(var(--primary)) !important;
  box-shadow: 0 0 0 2px rgba(173, 248, 2, 0.2) !important;
}

/* Dark theme gradient styles */
.dark .gradient-primary {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
  color: hsl(var(--primary-foreground));
}

.dark .gradient-text {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.7) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Dark theme button styles */
.dark .btn-primary {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.9) 100%);
  border: 1px solid hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.dark .btn-primary:hover {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.9) 0%, hsl(var(--primary) / 0.8) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(173, 248, 2, 0.3);
}

/* Dark theme scrollbar */
.dark ::-webkit-scrollbar {
  width: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.dark ::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.6);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}

/* Global dark theme overrides */
.dark * {
  border-color: #1a1a1a;
}

.dark .bg-white {
  background-color: #000000 !important;
}

.dark .bg-gray-50 {
  background-color: #0a0a0a !important;
}

.dark .bg-gray-100 {
  background-color: #0f0f0f !important;
}

.dark .bg-gray-200 {
  background-color: #1a1a1a !important;
}

.dark .text-gray-900 {
  color: #ffffff !important;
}

.dark .text-gray-800 {
  color: #f0f0f0 !important;
}

.dark .text-gray-700 {
  color: #e0e0e0 !important;
}

.dark .text-gray-600 {
  color: #d0d0d0 !important;
}

/* Dark theme for cards and containers */
.dark [class*="bg-white"],
.dark [class*="bg-gray-50"],
.dark [class*="bg-background"] {
  background-color: #000000 !important;
}

.dark [class*="bg-card"] {
  background-color: #0a0a0a !important;
}

/* Dark theme for modals and dropdowns */
.dark .bg-popover {
  background-color: #000000 !important;
}

.dark .bg-muted {
  background-color: #0a0a0a !important;
}

/* Force black background on root elements */
.dark html {
  background-color: #000000 !important;
}

.dark body {
  background-color: #000000 !important;
}

.dark #root {
  background-color: #000000 !important;
}

/* Dark theme for main containers */
.dark .min-h-screen {
  background-color: #000000 !important;
}

.dark .h-screen {
  background-color: #000000 !important;
}

/* Override any gradient backgrounds in dark mode */
.dark [class*="bg-gradient"] {
  background: #000000 !important;
}

.modern-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

/* Button styles */
.btn-primary {
  background: #ADF802;
  color: #000;
  border: 2px solid #ADF802;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(173, 248, 2, 0.3);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 8px -1px rgba(173, 248, 2, 0.4);
  background: #84cc16;
  border-color: #84cc16;
}

.btn-secondary {
  background: #bef264;
  color: #000;
  border: 2px solid #bef264;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(190, 242, 100, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 8px -1px rgba(190, 242, 100, 0.4);
  background: #a3e635;
  border-color: #a3e635;
}

/* Input styles */
.modern-input {
  background: white;
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  transition: all 0.3s ease;
  color: #1a1a1a;
}

.modern-input:focus {
  border-color: #ADF802;
  box-shadow: 0 0 0 3px rgba(173, 248, 2, 0.2);
  outline: none;
}

/* Dark theme input styles */
.dark .modern-input {
  background: #0a0a0a;
  border: 2px solid rgba(173, 248, 2, 0.3);
  color: #ffffff;
}

.dark .modern-input:focus {
  border-color: rgba(173, 248, 2, 0.6);
  box-shadow: 0 0 0 3px rgba(173, 248, 2, 0.1);
}

/* Loading animations */
.loading-spinner {
  border: 3px solid #dcfce7;
  border-top: 3px solid #ADF802;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pulse animation */
.pulse-green {
  animation: pulse-green 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-green {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Fade in animation */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in animation */
.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Micro interactions */
.hover-lift {
  transition: transform 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.scale-on-hover {
  transition: transform 0.2s ease;
}

.scale-on-hover:hover {
  transform: scale(1.05);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #dcfce7;
}

::-webkit-scrollbar-thumb {
  background: #ADF802;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #84cc16;
}

/* Status indicators */
.status-online {
  background: #ADF802;
  box-shadow: 0 0 0 2px white, 0 0 0 4px #ADF802;
}

.status-busy {
  background: #84cc16;
  box-shadow: 0 0 0 2px white, 0 0 0 4px #84cc16;
}

.status-offline {
  background: #6b7280;
  box-shadow: 0 0 0 2px white, 0 0 0 4px #6b7280;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Gradient text */
.gradient-text {
  color: #ADF802;
}

/* Success/Error states */
.success-state {
  border-color: #ADF802;
  background-color: #f0fdf4;
}

/* Light theme improvements */
.light .text-muted-foreground {
  color: hsl(220 8.9% 46.1%) !important;
}

.light .border {
  border-color: hsl(220 13% 91%) !important;
}

.light .bg-muted {
  background-color: hsl(220 14.3% 95.9%) !important;
}

/* Dark theme border opacity adjustments */
.dark .border-primary {
  border-color: rgba(173, 248, 2, 0.3) !important;
}

.dark .border-primary\/20 {
  border-color: rgba(173, 248, 2, 0.2) !important;
}

.dark .border-primary\/30 {
  border-color: rgba(173, 248, 2, 0.3) !important;
}

.dark .border-primary\/40 {
  border-color: rgba(173, 248, 2, 0.3) !important;
}

.dark .border-primary\/50 {
  border-color: rgba(173, 248, 2, 0.3) !important;
}

.error-state {
  border-color: #ef4444;
  background-color: #fef2f2;
}

/* Responsive design */
@media (max-width: 768px) {
  .modern-card {
    border-radius: 8px;
    margin: 8px;
  }
  
  .btn-primary,
  .btn-secondary {
    padding: 10px 20px;
    font-size: 14px;
  }
}

/* Enhanced Waveform Animations */
@keyframes waveform {
  0%, 100% {
    transform: scaleY(0.3);
    opacity: 0.6;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes bounce-wave {
  0%, 100% {
    transform: scaleY(0.4) translateY(0);
    background-color: #4ade80;
  }
  25% {
    transform: scaleY(0.8) translateY(-2px);
    background-color: #22c55e;
  }
  50% {
    transform: scaleY(1.2) translateY(-4px);
    background-color: #16a34a;
  }
  75% {
    transform: scaleY(0.9) translateY(-2px);
    background-color: #22c55e;
  }
}

.waveform-bar {
  animation: waveform 0.8s ease-in-out infinite;
}

.bounce-wave-bar {
  animation: bounce-wave 0.6s ease-in-out infinite;
}
