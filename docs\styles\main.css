:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4ade80;
    --warning-color: #fbbf24;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --shadow-neumorphic: 20px 20px 60px #d1d9e6, -20px -20px 60px #ffffff;
    --shadow-inset: inset 20px 20px 60px #d1d9e6, inset -20px -20px 60px #ffffff;
    --shadow-3d: 0 10px 30px rgba(0,0,0,0.3), 0 1px 8px rgba(0,0,0,0.2);
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--primary-color));
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: var(--gradient-primary);
    padding: 60px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

.logo {
    font-family: 'Bruno Ace SC', cursive;
    font-size: 4rem;
    color: white;
    text-shadow: var(--shadow-3d);
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.subtitle {
    font-size: 1.5rem;
    color: rgba(255,255,255,0.9);
    font-weight: 300;
    position: relative;
    z-index: 2;
}

/* Navigation */
.nav {
    background: var(--bg-secondary);
    padding: 20px 0;
    box-shadow: var(--shadow-neumorphic);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-list {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 30px;
    list-style: none;
}

.nav-item a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
    background: var(--bg-primary);
    box-shadow: 5px 5px 15px #d1d9e6, -5px -5px 15px #ffffff;
}

.nav-item a:hover,
.nav-item a.active {
    box-shadow: var(--shadow-inset);
    color: var(--primary-color);
}

/* Section Headers */
.section {
    margin: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-family: 'Bruno Ace SC', cursive;
    font-size: 2.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: var(--shadow-3d);
    margin-bottom: 20px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Code Blocks */
.code-block {
    background: #1e293b;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 10px;
    margin: 15px 0;
    overflow-x: auto;
    font-family: 'Fira Code', monospace;
    box-shadow: var(--shadow-inset);
}

.code-block pre {
    margin: 0;
}

/* Alert Boxes */
.alert {
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    border-left: 5px solid;
    box-shadow: var(--shadow-neumorphic);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    border-color: var(--info-color);
    color: var(--info-color);
}

.alert-warning {
    background: rgba(251, 191, 36, 0.1);
    border-color: var(--warning-color);
    color: #d97706;
}

.alert-success {
    background: rgba(74, 222, 128, 0.1);
    border-color: var(--success-color);
    color: #059669;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--error-color);
    color: var(--error-color);
}

/* Footer */
.footer {
    background: var(--gradient-primary);
    color: white;
    text-align: center;
    padding: 40px 0;
    margin-top: 80px;
}

.footer-logo {
    font-family: 'Bruno Ace SC', cursive;
    margin-bottom: 20px;
}

.footer-version {
    margin-top: 20px;
    opacity: 0.8;
}

.footer-links {
    margin-top: 30px;
}

.footer-links a {
    color: white;
    text-decoration: none;
    margin: 0 15px;
    transition: opacity 0.3s ease;
}

.footer-links a:hover {
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .logo {
        font-size: 2.5rem;
    }
    
    .nav-list {
        flex-direction: column;
        align-items: center;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .container {
        padding: 0 15px;
    }
}

/* Print Styles */
@media print {
    .nav, .header::before {
        display: none;
    }
    
    .card, .step {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        background: white;
    }
}
