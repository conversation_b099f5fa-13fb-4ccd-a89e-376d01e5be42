const logger = require('../utils/logger');
const { OpenAI } = require('openai');

/**
 * Agentic System for Medical AI Assistant
 * Coordinates multiple specialized AI agents for comprehensive medical analysis
 */
class AgenticMedicalSystem {
  constructor(enhancedAIService, multimodalRAG) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    
    this.enhancedAI = enhancedAIService;
    this.multimodalRAG = multimodalRAG;
    
    // Initialize specialized agents
    this.agents = {
      diagnostician: new DiagnosticAgent(this.openai, this.multimodalRAG),
      pharmacologist: new PharmacologyAgent(this.openai, this.multimodalRAG),
      radiologist: new RadiologyAgent(this.openai, this.multimodalRAG),
      pathologist: new PathologyAgent(this.openai, this.multimodalRAG),
      emergencyPhysician: new EmergencyPhysicianAgent(this.openai, this.multimodalRAG),
      internist: new InternistAgent(this.openai, this.multimodalRAG),
      coordinator: new CoordinatorAgent(this.openai)
    };
    
    this.isInitialized = false;
    this.initialize();
  }

  async initialize() {
    try {
      // Initialize all agents
      await Promise.all(
        Object.values(this.agents).map(agent => agent.initialize())
      );
      
      this.isInitialized = true;
      logger.info('Agentic Medical System initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Agentic Medical System:', error);
    }
  }

  /**
   * Main consultation method using multiple agents
   */
  async conductMultiAgentConsultation(patientData, consultationType = 'comprehensive') {
    try {
      logger.info(`Starting multi-agent consultation: ${consultationType}`);
      
      const consultation = {
        timestamp: new Date(),
        patientId: patientData.patientId,
        consultationType,
        agentReports: {},
        synthesis: null,
        recommendations: null,
        confidence: 0
      };

      // Step 1: Emergency assessment (always first)
      consultation.agentReports.emergency = await this.agents.emergencyPhysician.assess(patientData);
      
      // If emergency findings, prioritize immediate care
      if (consultation.agentReports.emergency.urgency === 'immediate') {
        consultation.synthesis = await this.agents.coordinator.synthesizeEmergencyFindings(
          consultation.agentReports.emergency
        );
        return consultation;
      }

      // Step 2: Parallel agent consultations
      const agentPromises = [];
      
      // Diagnostic agent - always included
      agentPromises.push(
        this.agents.diagnostician.analyze(patientData)
          .then(result => ({ agent: 'diagnostician', result }))
      );
      
      // Internal medicine perspective
      agentPromises.push(
        this.agents.internist.analyze(patientData)
          .then(result => ({ agent: 'internist', result }))
      );
      
      // Pharmacology review if medications involved
      if (patientData.medications || patientData.allergies) {
        agentPromises.push(
          this.agents.pharmacologist.analyze(patientData)
            .then(result => ({ agent: 'pharmacologist', result }))
        );
      }
      
      // Radiology review if imaging available
      if (patientData.imaging && patientData.imaging.length > 0) {
        agentPromises.push(
          this.agents.radiologist.analyze(patientData)
            .then(result => ({ agent: 'radiologist', result }))
        );
      }
      
      // Pathology review if lab results available
      if (patientData.labResults && patientData.labResults.length > 0) {
        agentPromises.push(
          this.agents.pathologist.analyze(patientData)
            .then(result => ({ agent: 'pathologist', result }))
        );
      }

      // Wait for all agent analyses
      const agentResults = await Promise.all(agentPromises);
      
      // Compile agent reports
      agentResults.forEach(({ agent, result }) => {
        consultation.agentReports[agent] = result;
      });

      // Step 3: Coordinator synthesis
      consultation.synthesis = await this.agents.coordinator.synthesizeFindings(
        consultation.agentReports,
        patientData
      );

      // Step 4: Generate final recommendations
      consultation.recommendations = await this.generateFinalRecommendations(
        consultation.synthesis,
        consultation.agentReports,
        patientData
      );

      // Step 5: Calculate overall confidence
      consultation.confidence = this.calculateOverallConfidence(consultation.agentReports);

      logger.info(`Multi-agent consultation completed for patient: ${patientData.patientId}`);
      return consultation;
    } catch (error) {
      logger.error('Error in multi-agent consultation:', error);
      throw error;
    }
  }

  /**
   * Generate final recommendations based on all agent inputs
   */
  async generateFinalRecommendations(synthesis, agentReports, patientData) {
    try {
      const prompt = `
As the coordinating physician with 25 years of experience, synthesize the following specialist consultations into final recommendations:

SYNTHESIS: ${JSON.stringify(synthesis, null, 2)}

SPECIALIST REPORTS:
${Object.entries(agentReports).map(([agent, report]) => 
  `${agent.toUpperCase()}: ${JSON.stringify(report, null, 2)}`
).join('\n\n')}

PATIENT DATA: ${JSON.stringify(patientData, null, 2)}

Provide final recommendations in the following format:
1. Primary Diagnosis (with confidence level)
2. Differential Diagnoses (ranked by probability)
3. Immediate Actions Required
4. Diagnostic Workup Plan
5. Treatment Recommendations
6. Follow-up Plan
7. Patient Education Points
8. Red Flags to Watch For

Be specific, evidence-based, and consider all specialist inputs while maintaining clinical judgment.
`;

      const response = await this.openai.chat.completions.create({
        model: "gpt-4-turbo-preview",
        messages: [
          {
            role: "system",
            content: "You are an experienced attending physician coordinating a multidisciplinary consultation. Provide comprehensive, evidence-based recommendations."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.3
      });

      return this.parseRecommendations(response.choices[0].message.content);
    } catch (error) {
      logger.error('Error generating final recommendations:', error);
      throw error;
    }
  }

  /**
   * Parse structured recommendations from AI response
   */
  parseRecommendations(content) {
    const sections = {
      primaryDiagnosis: '',
      differentialDiagnoses: [],
      immediateActions: [],
      diagnosticWorkup: [],
      treatment: [],
      followUp: [],
      patientEducation: [],
      redFlags: []
    };

    // Simple parsing logic - in production, this would be more sophisticated
    const lines = content.split('\n');
    let currentSection = null;

    lines.forEach(line => {
      const trimmed = line.trim();
      if (trimmed.includes('Primary Diagnosis')) {
        currentSection = 'primaryDiagnosis';
      } else if (trimmed.includes('Differential Diagnoses')) {
        currentSection = 'differentialDiagnoses';
      } else if (trimmed.includes('Immediate Actions')) {
        currentSection = 'immediateActions';
      } else if (trimmed.includes('Diagnostic Workup')) {
        currentSection = 'diagnosticWorkup';
      } else if (trimmed.includes('Treatment Recommendations')) {
        currentSection = 'treatment';
      } else if (trimmed.includes('Follow-up Plan')) {
        currentSection = 'followUp';
      } else if (trimmed.includes('Patient Education')) {
        currentSection = 'patientEducation';
      } else if (trimmed.includes('Red Flags')) {
        currentSection = 'redFlags';
      } else if (trimmed && currentSection) {
        if (currentSection === 'primaryDiagnosis') {
          sections[currentSection] = trimmed;
        } else if (Array.isArray(sections[currentSection])) {
          sections[currentSection].push(trimmed);
        }
      }
    });

    return sections;
  }

  /**
   * Calculate overall confidence based on agent agreement
   */
  calculateOverallConfidence(agentReports) {
    const confidences = Object.values(agentReports)
      .map(report => report.confidence || 0.5)
      .filter(conf => conf > 0);

    if (confidences.length === 0) return 0.5;

    // Calculate weighted average with agreement bonus
    const avgConfidence = confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;
    
    // Bonus for agent agreement (simplified)
    const variance = confidences.reduce((sum, conf) => sum + Math.pow(conf - avgConfidence, 2), 0) / confidences.length;
    const agreementBonus = Math.max(0, 0.1 - variance); // Up to 10% bonus for low variance
    
    return Math.min(1.0, avgConfidence + agreementBonus);
  }

  /**
   * Get agent-specific consultation
   */
  async getSpecialistConsultation(agentType, patientData) {
    if (!this.agents[agentType]) {
      throw new Error(`Unknown agent type: ${agentType}`);
    }

    return await this.agents[agentType].analyze(patientData);
  }

  /**
   * Add new medical knowledge to all agents
   */
  async updateMedicalKnowledge(knowledgeUpdate) {
    try {
      await Promise.all(
        Object.values(this.agents).map(agent => 
          agent.updateKnowledge ? agent.updateKnowledge(knowledgeUpdate) : Promise.resolve()
        )
      );
      
      logger.info('Medical knowledge updated across all agents');
    } catch (error) {
      logger.error('Error updating medical knowledge:', error);
      throw error;
    }
  }
}

/**
 * Base class for specialized medical agents
 */
class MedicalAgent {
  constructor(openai, multimodalRAG, specialty) {
    this.openai = openai;
    this.multimodalRAG = multimodalRAG;
    this.specialty = specialty;
    this.isInitialized = false;
  }

  async initialize() {
    this.isInitialized = true;
    logger.info(`${this.specialty} agent initialized`);
  }

  async analyze(patientData) {
    throw new Error('analyze method must be implemented by subclass');
  }

  async updateKnowledge(knowledgeUpdate) {
    // Default implementation - can be overridden
    logger.info(`Knowledge updated for ${this.specialty} agent`);
  }
}

/**
 * Diagnostic Agent - Focuses on differential diagnosis
 */
class DiagnosticAgent extends MedicalAgent {
  constructor(openai, multimodalRAG) {
    super(openai, multimodalRAG, 'Diagnostician');
  }

  async analyze(patientData) {
    try {
      // Retrieve relevant medical literature
      const ragResults = await this.multimodalRAG.retrieveRelevantInformation(
        `${patientData.chiefComplaint} ${patientData.symptoms?.join(' ') || ''}`,
        { includeTexts: true, includeDrugs: false, maxResults: 5 }
      );

      const prompt = `
As an experienced diagnostician with 25 years of practice, analyze this patient presentation:

PATIENT DATA: ${JSON.stringify(patientData, null, 2)}

RELEVANT MEDICAL LITERATURE:
${ragResults.texts.map(text => text.content).join('\n\n')}

Provide a diagnostic analysis including:
1. Most likely diagnosis with reasoning
2. Top 5 differential diagnoses with probabilities
3. Key diagnostic criteria for each
4. Recommended diagnostic tests
5. Clinical reasoning process
6. Confidence level (0-1)

Focus on evidence-based medicine and clinical experience.
`;

      const response = await this.openai.chat.completions.create({
        model: "gpt-4-turbo-preview",
        messages: [
          {
            role: "system",
            content: "You are an expert diagnostician with 25 years of clinical experience. Provide thorough, evidence-based diagnostic analysis."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 1500,
        temperature: 0.2
      });

      return {
        agent: 'diagnostician',
        analysis: response.choices[0].message.content,
        confidence: 0.85,
        timestamp: new Date(),
        ragSources: ragResults.texts.length
      };
    } catch (error) {
      logger.error('Error in diagnostic analysis:', error);
      throw error;
    }
  }
}

/**
 * Emergency Physician Agent - Focuses on triage and immediate care
 */
class EmergencyPhysicianAgent extends MedicalAgent {
  constructor(openai, multimodalRAG) {
    super(openai, multimodalRAG, 'Emergency Physician');
  }

  async assess(patientData) {
    try {
      const prompt = `
As an emergency physician with 25 years of experience, perform immediate triage assessment:

PATIENT DATA: ${JSON.stringify(patientData, null, 2)}

Provide emergency assessment:
1. Triage level (immediate/urgent/semi-urgent/routine)
2. Life-threatening conditions to rule out
3. Immediate interventions required
4. Vital sign interpretation
5. Red flags identified
6. Disposition recommendation
7. Confidence level (0-1)

Focus on patient safety and immediate stabilization needs.
`;

      const response = await this.openai.chat.completions.create({
        model: "gpt-4-turbo-preview",
        messages: [
          {
            role: "system",
            content: "You are an experienced emergency physician. Prioritize patient safety and immediate care needs."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.1
      });

      // Parse urgency level from response
      const content = response.choices[0].message.content;
      let urgency = 'routine';
      if (content.toLowerCase().includes('immediate')) urgency = 'immediate';
      else if (content.toLowerCase().includes('urgent')) urgency = 'urgent';
      else if (content.toLowerCase().includes('semi-urgent')) urgency = 'semi-urgent';

      return {
        agent: 'emergency',
        assessment: content,
        urgency,
        confidence: 0.9,
        timestamp: new Date()
      };
    } catch (error) {
      logger.error('Error in emergency assessment:', error);
      throw error;
    }
  }
}

// Additional agent classes would be implemented similarly...
class PharmacologyAgent extends MedicalAgent {
  constructor(openai, multimodalRAG) {
    super(openai, multimodalRAG, 'Pharmacologist');
  }

  async analyze(patientData) {
    // Implementation for drug interaction and prescription analysis
    return {
      agent: 'pharmacologist',
      analysis: 'Pharmacology analysis placeholder',
      confidence: 0.8,
      timestamp: new Date()
    };
  }
}

class RadiologyAgent extends MedicalAgent {
  constructor(openai, multimodalRAG) {
    super(openai, multimodalRAG, 'Radiologist');
  }

  async analyze(patientData) {
    // Implementation for imaging analysis
    return {
      agent: 'radiologist',
      analysis: 'Radiology analysis placeholder',
      confidence: 0.8,
      timestamp: new Date()
    };
  }
}

class PathologyAgent extends MedicalAgent {
  constructor(openai, multimodalRAG) {
    super(openai, multimodalRAG, 'Pathologist');
  }

  async analyze(patientData) {
    // Implementation for lab result analysis
    return {
      agent: 'pathologist',
      analysis: 'Pathology analysis placeholder',
      confidence: 0.8,
      timestamp: new Date()
    };
  }
}

class InternistAgent extends MedicalAgent {
  constructor(openai, multimodalRAG) {
    super(openai, multimodalRAG, 'Internist');
  }

  async analyze(patientData) {
    // Implementation for internal medicine perspective
    return {
      agent: 'internist',
      analysis: 'Internal medicine analysis placeholder',
      confidence: 0.8,
      timestamp: new Date()
    };
  }
}

class CoordinatorAgent extends MedicalAgent {
  constructor(openai) {
    super(openai, null, 'Coordinator');
  }

  async synthesizeFindings(agentReports, patientData) {
    // Implementation for synthesizing multiple agent findings
    return {
      synthesis: 'Coordinated findings synthesis placeholder',
      confidence: 0.85,
      timestamp: new Date()
    };
  }

  async synthesizeEmergencyFindings(emergencyReport) {
    return {
      emergencySynthesis: emergencyReport.assessment,
      immediateActions: ['Stabilize patient', 'Call appropriate specialists'],
      confidence: emergencyReport.confidence
    };
  }
}

module.exports = AgenticMedicalSystem;
