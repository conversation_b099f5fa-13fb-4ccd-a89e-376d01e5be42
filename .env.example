# MEDORA Frontend Environment Variables
# Copy this file to .env and fill in your actual values

# API Configuration
VITE_API_URL=https://medora-ai-backend.vercel.app
VITE_API_BASE_URL=https://medora-ai-backend.vercel.app/api

# Application Configuration
VITE_APP_NAME=MEDORA
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION="AI-Powered Medical Assistant Platform"

# Theme Configuration
VITE_DEFAULT_THEME=dark

# Development Configuration
VITE_DEV_MODE=true
VITE_DEBUG=true

# Analytics (Optional)
VITE_GOOGLE_ANALYTICS_ID=
VITE_HOTJAR_ID=

# Social Media Authentication
VITE_GOOGLE_CLIENT_ID=
VITE_FACEBOOK_APP_ID=
VITE_GITHUB_CLIENT_ID=
VITE_TWITTER_CLIENT_ID=
VITE_APPLE_CLIENT_ID=
VITE_LINKEDIN_CLIENT_ID=

# Social Media (Optional)
VITE_TWITTER_HANDLE=@medora_ai

# Contact Information
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_CONTACT_PHONE=******-123-4567

# Feature Flags
VITE_ENABLE_VOICE_CHAT=true
VITE_ENABLE_VIDEO_CALLS=true
VITE_ENABLE_AI_DIAGNOSIS=true
VITE_ENABLE_PAYMENTS=true

# External Services
VITE_STRIPE_PUBLISHABLE_KEY=
VITE_PAYSTACK_PUBLIC_KEY=

# CDN Configuration (Optional)
VITE_CDN_URL=
VITE_ASSETS_URL=
