/* Keyframe Animations */
@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
    40%, 43% { transform: translateY(-10px); }
    70% { transform: translateY(-5px); }
    90% { transform: translateY(-2px); }
}

@keyframes slideInLeft {
    0% { transform: translateX(-100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
    0% { transform: translateX(100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInUp {
    0% { transform: translateY(100%); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes slideInDown {
    0% { transform: translateY(-100%); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

@keyframes fadeInUp {
    0% { transform: translateY(30px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes fadeInDown {
    0% { transform: translateY(-30px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes zoomIn {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes zoomOut {
    0% { transform: scale(1.2); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes flipInX {
    0% { transform: perspective(400px) rotateX(90deg); opacity: 0; }
    40% { transform: perspective(400px) rotateX(-20deg); }
    60% { transform: perspective(400px) rotateX(10deg); opacity: 1; }
    80% { transform: perspective(400px) rotateX(-5deg); }
    100% { transform: perspective(400px) rotateX(0deg); opacity: 1; }
}

@keyframes flipInY {
    0% { transform: perspective(400px) rotateY(90deg); opacity: 0; }
    40% { transform: perspective(400px) rotateY(-20deg); }
    60% { transform: perspective(400px) rotateY(10deg); opacity: 1; }
    80% { transform: perspective(400px) rotateY(-5deg); }
    100% { transform: perspective(400px) rotateY(0deg); opacity: 1; }
}

@keyframes lightSpeedIn {
    0% { transform: translateX(100%) skewX(-30deg); opacity: 0; }
    60% { transform: translateX(-20%) skewX(20deg); opacity: 1; }
    80% { transform: translateX(0%) skewX(-5deg); opacity: 1; }
    100% { transform: translateX(0%) skewX(0deg); opacity: 1; }
}

@keyframes rollIn {
    0% { transform: translateX(-100%) rotate(-120deg); opacity: 0; }
    100% { transform: translateX(0%) rotate(0deg); opacity: 1; }
}

@keyframes rubberBand {
    0% { transform: scaleX(1); }
    30% { transform: scaleX(1.25) scaleY(0.75); }
    40% { transform: scaleX(0.75) scaleY(1.25); }
    50% { transform: scaleX(1.15) scaleY(0.85); }
    65% { transform: scaleX(0.95) scaleY(1.05); }
    75% { transform: scaleX(1.05) scaleY(0.95); }
    100% { transform: scaleX(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
    20%, 40%, 60%, 80% { transform: translateX(10px); }
}

@keyframes swing {
    20% { transform: rotate(15deg); }
    40% { transform: rotate(-10deg); }
    60% { transform: rotate(5deg); }
    80% { transform: rotate(-5deg); }
    100% { transform: rotate(0deg); }
}

@keyframes tada {
    0% { transform: scaleX(1); }
    10%, 20% { transform: scaleX(0.9) rotate(-3deg); }
    30%, 50%, 70%, 90% { transform: scaleX(1.1) rotate(3deg); }
    40%, 60%, 80% { transform: scaleX(1.1) rotate(-3deg); }
    100% { transform: scaleX(1) rotate(0); }
}

@keyframes wobble {
    0% { transform: translateX(0%); }
    15% { transform: translateX(-25%) rotate(-5deg); }
    30% { transform: translateX(20%) rotate(3deg); }
    45% { transform: translateX(-15%) rotate(-3deg); }
    60% { transform: translateX(10%) rotate(2deg); }
    75% { transform: translateX(-5%) rotate(-1deg); }
    100% { transform: translateX(0%); }
}

@keyframes jello {
    0%, 11.1%, 100% { transform: translateX(0); }
    22.2% { transform: skewX(-12.5deg) skewY(-12.5deg); }
    33.3% { transform: skewX(6.25deg) skewY(6.25deg); }
    44.4% { transform: skewX(-3.125deg) skewY(-3.125deg); }
    55.5% { transform: skewX(1.5625deg) skewY(1.5625deg); }
    66.6% { transform: skewX(-0.78125deg) skewY(-0.78125deg); }
    77.7% { transform: skewX(0.390625deg) skewY(0.390625deg); }
    88.8% { transform: skewX(-0.1953125deg) skewY(-0.1953125deg); }
}

@keyframes heartBeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.3); }
    28% { transform: scale(1); }
    42% { transform: scale(1.3); }
    70% { transform: scale(1); }
}

/* Animation Classes */
.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

.animate-slide-in-down {
    animation: slideInDown 0.6s ease-out;
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.animate-zoom-in {
    animation: zoomIn 0.6s ease-out;
}

.animate-zoom-out {
    animation: zoomOut 0.6s ease-out;
}

.animate-flip-in-x {
    animation: flipInX 0.75s ease-out;
}

.animate-flip-in-y {
    animation: flipInY 0.75s ease-out;
}

.animate-light-speed-in {
    animation: lightSpeedIn 0.75s ease-out;
}

.animate-roll-in {
    animation: rollIn 0.75s ease-out;
}

.animate-rubber-band {
    animation: rubberBand 1s ease-out;
}

.animate-shake {
    animation: shake 0.82s ease-out;
}

.animate-swing {
    animation: swing 1s ease-out;
}

.animate-tada {
    animation: tada 1s ease-out;
}

.animate-wobble {
    animation: wobble 1s ease-out;
}

.animate-jello {
    animation: jello 1s ease-out;
}

.animate-heart-beat {
    animation: heartBeat 1.3s ease-in-out infinite;
}

/* Hover Animations */
.hover-lift:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}

.hover-grow:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.hover-shrink:hover {
    transform: scale(0.95);
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
    transition: transform 0.3s ease;
}

.hover-skew:hover {
    transform: skew(-5deg);
    transition: transform 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
    transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: box-shadow 0.3s ease;
}

/* Loading Animations */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--bg-primary);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: rotate 1s linear infinite;
}

.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

.loading-pulse {
    background: linear-gradient(90deg, var(--bg-primary) 25%, var(--bg-secondary) 50%, var(--bg-primary) 75%);
    background-size: 200% 100%;
    animation: loading-pulse 1.5s infinite;
}

@keyframes loading-pulse {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Scroll Animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

.scroll-reveal-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.scroll-reveal-left.revealed {
    opacity: 1;
    transform: translateX(0);
}

.scroll-reveal-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.scroll-reveal-right.revealed {
    opacity: 1;
    transform: translateX(0);
}

/* Stagger Animations */
.stagger-animation > * {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }
.stagger-animation > *:nth-child(7) { animation-delay: 0.7s; }
.stagger-animation > *:nth-child(8) { animation-delay: 0.8s; }
.stagger-animation > *:nth-child(9) { animation-delay: 0.9s; }
.stagger-animation > *:nth-child(10) { animation-delay: 1.0s; }
