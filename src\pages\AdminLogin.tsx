import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/hooks/useAuth";
import {
  Mail,
  Lock,
  Eye,
  EyeOff,
  ArrowRight,
  AlertTriangle,
  CheckCircle,
  Shield,
  Settings,
  Users
} from "lucide-react";

const AdminLogin = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [localError, setLocalError] = useState("");
  const { login, loading, error: authError } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLocalError("");

    if (!email || !password) {
      setLocalError("Please fill in all fields");
      return;
    }

    const success = await login({ email, password });
    if (success) {
      // Check if user is admin after successful login
      const token = localStorage.getItem('authToken');
      if (token) {
        try {
          // Decode token to check role (basic check)
          const payload = JSON.parse(atob(token.split('.')[1]));
          if (payload.role === 'admin') {
            navigate("/admin");
          } else {
            setLocalError("Access denied. Admin privileges required.");
            // Logout non-admin user
            localStorage.removeItem('authToken');
          }
        } catch (error) {
          console.error('Token decode error:', error);
          navigate("/admin");
        }
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-[#ADF802]/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>

      <div className="w-full max-w-md relative z-10">
        {/* Logo and Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-[#ADF802] to-purple-500 blur-xl opacity-30 animate-pulse"></div>
              <div className="relative bg-white/5 backdrop-blur-sm rounded-2xl p-4 border border-white/10">
                <img 
                  src="/lovable-uploads/logo-png.png" 
                  alt="MEDORA Logo" 
                  className="h-12 w-12 object-contain mx-auto"
                />
              </div>
            </div>
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Admin Portal</h1>
          <p className="text-white/70">MEDORA Administrative Dashboard</p>
          
          {/* Admin Features Badge */}
          <div className="flex justify-center mt-4 space-x-2">
            <Badge className="bg-[#ADF802]/20 text-[#ADF802] border border-[#ADF802]/30">
              <Shield className="w-3 h-3 mr-1" />
              Secure Access
            </Badge>
            <Badge className="bg-purple-500/20 text-purple-300 border border-purple-500/30">
              <Settings className="w-3 h-3 mr-1" />
              Full Control
            </Badge>
          </div>
        </div>

        {/* Login Card */}
        <Card className="bg-white/5 backdrop-blur-sm border border-white/10 shadow-2xl">
          <CardHeader className="text-center">
            <CardTitle className="text-xl text-white">Administrator Login</CardTitle>
            <CardDescription className="text-white/70">
              Enter your admin credentials to access the dashboard
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email Field */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-white/90">Email Address</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-[#ADF802] focus:ring-[#ADF802]"
                    required
                  />
                </div>
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <Label htmlFor="password" className="text-white/90">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pl-10 pr-10 bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-[#ADF802] focus:ring-[#ADF802]"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/80"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              {/* Error Messages */}
              {(localError || authError) && (
                <div className="flex items-center space-x-2 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
                  <AlertTriangle className="w-4 h-4 text-red-400" />
                  <span className="text-red-300 text-sm">{localError || authError}</span>
                </div>
              )}

              {/* Login Button */}
              <Button
                type="submit"
                disabled={loading}
                className="w-full bg-gradient-to-r from-[#ADF802] to-[#84cc16] text-black hover:text-black font-semibold py-3 hover:scale-105 transition-all duration-300 shadow-lg shadow-[#ADF802]/25"
              >
                {loading ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black"></div>
                    <span>Signing in...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <span>Access Admin Dashboard</span>
                    <ArrowRight className="w-4 h-4" />
                  </div>
                )}
              </Button>
            </form>

            {/* Default Admin Credentials Info */}
            <div className="mt-6 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg">
              <div className="flex items-start space-x-2">
                <CheckCircle className="w-4 h-4 text-blue-400 mt-0.5" />
                <div className="text-sm">
                  <p className="text-blue-300 font-medium">Default Admin Credentials:</p>
                  <p className="text-blue-200 text-xs mt-1">Email: <EMAIL></p>
                  <p className="text-blue-200 text-xs">Password: Americana123456789@</p>
                </div>
              </div>
            </div>

            {/* Footer Links */}
            <div className="mt-6 text-center space-y-2">
              <Link 
                to="/login" 
                className="text-white/70 hover:text-[#ADF802] text-sm transition-colors"
              >
                Regular User Login
              </Link>
              <div className="text-white/50 text-xs">
                Need help? Contact system administrator
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Admin Features */}
        <div className="mt-8 grid grid-cols-3 gap-4 text-center">
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-3 border border-white/10">
            <Users className="w-6 h-6 text-[#ADF802] mx-auto mb-2" />
            <p className="text-white/70 text-xs">User Management</p>
          </div>
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-3 border border-white/10">
            <Settings className="w-6 h-6 text-purple-400 mx-auto mb-2" />
            <p className="text-white/70 text-xs">System Settings</p>
          </div>
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-3 border border-white/10">
            <Shield className="w-6 h-6 text-blue-400 mx-auto mb-2" />
            <p className="text-white/70 text-xs">Security Controls</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;
