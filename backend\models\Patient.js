const mongoose = require('mongoose');
const validator = require('validator');

const vitalSignsSchema = new mongoose.Schema({
  temperature: {
    value: Number,
    unit: {
      type: String,
      enum: ['celsius', 'fahrenheit'],
      default: 'celsius',
    },
  },
  bloodPressure: {
    systolic: Number,
    diastolic: Number,
    unit: {
      type: String,
      default: 'mmHg',
    },
  },
  heartRate: {
    value: Number,
    unit: {
      type: String,
      default: 'bpm',
    },
  },
  respiratoryRate: {
    value: Number,
    unit: {
      type: String,
      default: 'breaths/min',
    },
  },
  oxygenSaturation: {
    value: Number,
    unit: {
      type: String,
      default: '%',
    },
  },
  weight: {
    value: Number,
    unit: {
      type: String,
      enum: ['kg', 'lbs'],
      default: 'kg',
    },
  },
  height: {
    value: Number,
    unit: {
      type: String,
      enum: ['cm', 'inches'],
      default: 'cm',
    },
  },
  bmi: Number,
  recordedAt: {
    type: Date,
    default: Date.now,
  },
  recordedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
});

const labResultSchema = new mongoose.Schema({
  testName: {
    type: String,
    required: true,
  },
  testCode: String,
  category: {
    type: String,
    enum: [
      'blood_chemistry',
      'hematology',
      'immunology',
      'microbiology',
      'pathology',
      'radiology',
      'cardiology',
      'other',
    ],
    required: true,
  },
  results: [{
    parameter: String,
    value: String,
    unit: String,
    referenceRange: String,
    status: {
      type: String,
      enum: ['normal', 'abnormal', 'critical', 'pending'],
      default: 'normal',
    },
  }],
  orderedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  performedAt: Date,
  reportedAt: {
    type: Date,
    default: Date.now,
  },
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed', 'cancelled'],
    default: 'pending',
  },
  notes: String,
  attachments: [{
    filename: String,
    url: String,
    uploadedAt: {
      type: Date,
      default: Date.now,
    },
  }],
});

const medicationSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  genericName: String,
  dosage: {
    type: String,
    required: true,
  },
  frequency: {
    type: String,
    required: true,
  },
  route: {
    type: String,
    enum: ['oral', 'injection', 'topical', 'inhalation', 'other'],
    default: 'oral',
  },
  prescribedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  prescribedAt: {
    type: Date,
    default: Date.now,
  },
  startDate: {
    type: Date,
    required: true,
  },
  endDate: Date,
  isActive: {
    type: Boolean,
    default: true,
  },
  instructions: String,
  sideEffects: [String],
  interactions: [String],
});

const appointmentSchema = new mongoose.Schema({
  doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  scheduledAt: {
    type: Date,
    required: true,
  },
  duration: {
    type: Number,
    default: 30, // minutes
  },
  type: {
    type: String,
    enum: ['consultation', 'follow_up', 'emergency', 'routine_checkup', 'specialist'],
    required: true,
  },
  status: {
    type: String,
    enum: ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'],
    default: 'scheduled',
  },
  reason: String,
  notes: String,
  diagnosis: String,
  treatment: String,
  followUpRequired: Boolean,
  followUpDate: Date,
});

const patientSchema = new mongoose.Schema({
  // Link to User model for basic info
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
  },
  
  // Medical Record Number
  mrn: {
    type: String,
    unique: true,
    required: true,
  },
  
  // Insurance Information
  insurance: {
    provider: String,
    policyNumber: String,
    groupNumber: String,
    subscriberId: String,
    effectiveDate: Date,
    expirationDate: Date,
  },
  
  // Medical History
  medicalHistory: {
    allergies: [{
      allergen: String,
      reaction: String,
      severity: {
        type: String,
        enum: ['mild', 'moderate', 'severe', 'life_threatening'],
      },
      notes: String,
    }],
    chronicConditions: [{
      condition: String,
      diagnosedDate: Date,
      status: {
        type: String,
        enum: ['active', 'resolved', 'managed'],
        default: 'active',
      },
      notes: String,
    }],
    surgicalHistory: [{
      procedure: String,
      date: Date,
      surgeon: String,
      hospital: String,
      complications: String,
      notes: String,
    }],
    familyHistory: [{
      relationship: String,
      condition: String,
      ageAtDiagnosis: Number,
      notes: String,
    }],
    socialHistory: {
      smokingStatus: {
        type: String,
        enum: ['never', 'former', 'current'],
      },
      alcoholUse: {
        type: String,
        enum: ['none', 'occasional', 'moderate', 'heavy'],
      },
      drugUse: {
        type: String,
        enum: ['none', 'former', 'current'],
      },
      occupation: String,
      maritalStatus: String,
      exerciseFrequency: String,
    },
  },
  
  // Current Medications
  medications: [medicationSchema],
  
  // Vital Signs History
  vitalSigns: [vitalSignsSchema],
  
  // Lab Results
  labResults: [labResultSchema],
  
  // Appointments
  appointments: [appointmentSchema],
  
  // Primary Care Provider
  primaryCareProvider: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  
  // Care Team
  careTeam: [{
    provider: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    role: String,
    assignedAt: {
      type: Date,
      default: Date.now,
    },
  }],
  
  // Risk Factors
  riskFactors: [{
    factor: String,
    level: {
      type: String,
      enum: ['low', 'moderate', 'high'],
    },
    assessedAt: {
      type: Date,
      default: Date.now,
    },
    notes: String,
  }],
  
  // Clinical Notes
  clinicalNotes: [{
    note: String,
    type: {
      type: String,
      enum: ['progress', 'assessment', 'plan', 'discharge', 'other'],
    },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  }],
  
  // Emergency Contacts (additional to User model)
  emergencyContacts: [{
    name: String,
    relationship: String,
    phoneNumber: String,
    email: String,
    isPrimary: Boolean,
  }],
  
  // Consent and Preferences
  consents: {
    treatmentConsent: {
      given: Boolean,
      date: Date,
      witnessedBy: String,
    },
    dataSharing: {
      given: Boolean,
      date: Date,
      scope: [String],
    },
    researchParticipation: {
      given: Boolean,
      date: Date,
      studies: [String],
    },
  },
  
  // Status
  status: {
    type: String,
    enum: ['active', 'inactive', 'deceased', 'transferred'],
    default: 'active',
  },
  
  // Flags
  flags: [{
    type: {
      type: String,
      enum: ['allergy', 'infection_control', 'fall_risk', 'suicide_risk', 'other'],
    },
    description: String,
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
    },
    active: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  }],
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
patientSchema.index({ userId: 1 });
patientSchema.index({ mrn: 1 });
patientSchema.index({ 'appointments.scheduledAt': 1 });
patientSchema.index({ 'appointments.doctor': 1 });
patientSchema.index({ primaryCareProvider: 1 });
patientSchema.index({ status: 1 });
patientSchema.index({ createdAt: -1 });

// Virtual for current age
patientSchema.virtual('currentAge').get(function() {
  if (!this.populated('userId') || !this.userId.dateOfBirth) return null;
  const today = new Date();
  const birthDate = new Date(this.userId.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
});

// Virtual for latest vital signs
patientSchema.virtual('latestVitalSigns').get(function() {
  if (!this.vitalSigns || this.vitalSigns.length === 0) return null;
  return this.vitalSigns[this.vitalSigns.length - 1];
});

// Virtual for active medications
patientSchema.virtual('activeMedications').get(function() {
  return this.medications.filter(med => med.isActive);
});

// Pre-save middleware to generate MRN
patientSchema.pre('save', async function(next) {
  if (this.isNew && !this.mrn) {
    // Generate unique MRN
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    this.mrn = `MRN${timestamp}${random}`;
  }
  next();
});

module.exports = mongoose.model('Patient', patientSchema);