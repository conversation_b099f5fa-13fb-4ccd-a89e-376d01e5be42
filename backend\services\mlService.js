const axios = require('axios');
// Using simulated ML models instead of TensorFlow for compatibility
const logger = require('../utils/logger');

class MLService {
  constructor() {
    this.models = {
      symptomAnalyzer: null,
      riskAssessment: null,
      drugInteraction: null,
    };
    this.isInitialized = false;
    this.medicalKnowledgeBase = this.initializeMedicalKB();
  }

  // Initialize medical knowledge base
  initializeMedicalKB() {
    return {
      symptoms: {
        'chest pain': {
          conditions: [
            { name: 'Myocardial Infarction', probability: 0.15, urgency: 'emergency' },
            { name: '<PERSON><PERSON>', probability: 0.25, urgency: 'high' },
            { name: 'Gastroesophageal Reflux', probability: 0.30, urgency: 'medium' },
            { name: 'Musculoskeletal Pain', probability: 0.20, urgency: 'low' },
            { name: 'Anxiety', probability: 0.10, urgency: 'medium' },
          ],
        },
        'headache': {
          conditions: [
            { name: 'Tension Headache', probability: 0.60, urgency: 'low' },
            { name: 'Migraine', probability: 0.25, urgency: 'medium' },
            { name: 'Cluster Headache', probability: 0.05, urgency: 'medium' },
            { name: 'Sinus<PERSON>', probability: 0.08, urgency: 'low' },
            { name: 'Intracranial Pressure', probability: 0.02, urgency: 'emergency' },
          ],
        },
        'fever': {
          conditions: [
            { name: 'Viral Infection', probability: 0.50, urgency: 'low' },
            { name: 'Bacterial Infection', probability: 0.30, urgency: 'medium' },
            { name: 'Urinary Tract Infection', probability: 0.10, urgency: 'medium' },
            { name: 'Pneumonia', probability: 0.08, urgency: 'high' },
            { name: 'Sepsis', probability: 0.02, urgency: 'emergency' },
          ],
        },
        'shortness of breath': {
          conditions: [
            { name: 'Asthma', probability: 0.30, urgency: 'medium' },
            { name: 'Pneumonia', probability: 0.20, urgency: 'high' },
            { name: 'Heart Failure', probability: 0.15, urgency: 'high' },
            { name: 'Pulmonary Embolism', probability: 0.10, urgency: 'emergency' },
            { name: 'Anxiety', probability: 0.25, urgency: 'low' },
          ],
        },
        'abdominal pain': {
          conditions: [
            { name: 'Gastritis', probability: 0.35, urgency: 'low' },
            { name: 'Appendicitis', probability: 0.15, urgency: 'emergency' },
            { name: 'Gallbladder Disease', probability: 0.20, urgency: 'medium' },
            { name: 'Kidney Stones', probability: 0.15, urgency: 'high' },
            { name: 'Bowel Obstruction', probability: 0.15, urgency: 'emergency' },
          ],
        },
      },
      riskFactors: {
        age: {
          cardiovascular: { threshold: 65, multiplier: 1.5 },
          diabetes: { threshold: 45, multiplier: 1.3 },
          cancer: { threshold: 50, multiplier: 1.4 },
        },
        gender: {
          male: ['cardiovascular', 'stroke'],
          female: ['osteoporosis', 'autoimmune'],
        },
        lifestyle: {
          smoking: ['lung_cancer', 'copd', 'cardiovascular'],
          alcohol: ['liver_disease', 'pancreatitis'],
          sedentary: ['diabetes', 'cardiovascular', 'obesity'],
        },
      },
      drugInteractions: {
        warfarin: {
          dangerous: ['aspirin', 'ibuprofen', 'naproxen'],
          moderate: ['acetaminophen', 'omeprazole'],
        },
        metformin: {
          dangerous: ['contrast_dye'],
          moderate: ['alcohol', 'furosemide'],
        },
        digoxin: {
          dangerous: ['quinidine', 'verapamil'],
          moderate: ['thiazide', 'spironolactone'],
        },
      },
    };
  }

  // Enhanced symptom analysis with external data integration
  async analyzeSymptomWithExternalData(symptoms, patientData, userId) {
    try {
      const startTime = Date.now();

      // Get basic symptom analysis
      const basicAnalysis = await this.analyzeSymptoms({
        symptoms,
        patientAge: patientData.age,
        patientGender: patientData.gender,
        vitalSigns: patientData.vitalSigns,
        medicalHistory: patientData.medicalHistory,
      });

      // Get external medical data for top conditions
      const topConditions = basicAnalysis.predictions.slice(0, 3);
      const externalDataPromises = topConditions.map(async (condition) => {
        try {
          const scraperService = require('./scraperService');
          const scraperInstance = scraperService.getInstance();
          return await scraperInstance.scrapeComprehensiveMedicalDataWithStorage(
            condition.condition,
            userId
          );
        } catch (error) {
          logger.warn(`Failed to get external data for ${condition.condition}:`, error.message);
          return null;
        }
      });

      const externalDataResults = await Promise.allSettled(externalDataPromises);
      const externalData = externalDataResults
        .filter(result => result.status === 'fulfilled' && result.value)
        .map(result => result.value);

      // Enhance analysis with external data
      const enhancedAnalysis = {
        ...basicAnalysis,
        externalDataSources: externalData.length,
        enhancedRecommendations: this.generateEnhancedRecommendations(
          basicAnalysis.predictions,
          externalData
        ),
        researchEvidence: this.extractResearchEvidence(externalData),
        clinicalTrials: this.extractClinicalTrials(externalData),
        processingTime: Date.now() - startTime,
      };

      return enhancedAnalysis;
    } catch (error) {
      logger.error('Enhanced symptom analysis error:', error);
      // Fallback to basic analysis
      return this.analyzeSymptoms({
        symptoms,
        patientAge: patientData.age,
        patientGender: patientData.gender,
        vitalSigns: patientData.vitalSigns,
        medicalHistory: patientData.medicalHistory,
      });
    }
  }

  // Generate enhanced recommendations with external data
  generateEnhancedRecommendations(predictions, externalData) {
    const baseRecommendations = this.generateRecommendations(predictions, {});
    const enhancedRecommendations = [...baseRecommendations];

    // Add evidence-based recommendations from external data
    externalData.forEach(data => {
      if (data.comprehensiveData?.research) {
        data.comprehensiveData.research.forEach(research => {
          if (research.abstract && research.abstract.includes('treatment')) {
            enhancedRecommendations.push(
              `Recent research suggests: ${research.title.substring(0, 100)}...`
            );
          }
        });
      }

      if (data.comprehensiveData?.guidelines) {
        data.comprehensiveData.guidelines.forEach(guideline => {
          enhancedRecommendations.push(
            `Clinical guideline: ${guideline.title.substring(0, 100)}...`
          );
        });
      }
    });

    return enhancedRecommendations.slice(0, 10); // Limit to 10 recommendations
  }

  // Extract research evidence from external data
  extractResearchEvidence(externalData) {
    const evidence = [];

    externalData.forEach(data => {
      if (data.comprehensiveData?.research) {
        data.comprehensiveData.research.forEach(research => {
          evidence.push({
            title: research.title,
            journal: research.journal,
            abstract: research.abstract?.substring(0, 300),
            url: research.url,
            relevance: data.relevanceScore || 0.5,
          });
        });
      }
    });

    return evidence.sort((a, b) => b.relevance - a.relevance).slice(0, 5);
  }

  // Extract clinical trials from external data
  extractClinicalTrials(externalData) {
    const trials = [];

    externalData.forEach(data => {
      if (data.comprehensiveData?.clinicalTrials) {
        data.comprehensiveData.clinicalTrials.forEach(trial => {
          trials.push({
            title: trial.title,
            status: trial.status,
            phase: trial.phase,
            condition: trial.condition,
            intervention: trial.intervention,
            location: trial.location,
            url: trial.url,
          });
        });
      }
    });

    return trials.slice(0, 3);
  }

  // Initialize ML models
  async initialize() {
    try {
      logger.info('Initializing ML Service...');
      
      // In a real implementation, you would load pre-trained models
      // For now, we'll simulate model loading
      await this.loadSymptomAnalyzer();
      await this.loadRiskAssessmentModel();
      await this.loadDrugInteractionModel();
      
      this.isInitialized = true;
      logger.info('ML Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize ML Service:', error);
      throw error;
    }
  }

  async loadSymptomAnalyzer() {
    // Simulate loading a symptom analysis model
    // In production, this would load from a saved model file
    logger.info('Loading symptom analyzer model...');
    this.models.symptomAnalyzer = {
      loaded: true,
      version: '1.0.0',
      accuracy: 0.87,
    };
  }

  async loadRiskAssessmentModel() {
    logger.info('Loading risk assessment model...');
    this.models.riskAssessment = {
      loaded: true,
      version: '1.0.0',
      accuracy: 0.82,
    };
  }

  async loadDrugInteractionModel() {
    logger.info('Loading drug interaction model...');
    this.models.drugInteraction = {
      loaded: true,
      version: '1.0.0',
      accuracy: 0.95,
    };
  }

  // Analyze symptoms and provide differential diagnosis
  async analyzeSymptoms(data) {
    try {
      const startTime = Date.now();
      
      if (!this.isInitialized) {
        await this.initialize();
      }

      const { symptoms, patientAge, patientGender, vitalSigns, medicalHistory } = data;
      
      // Extract primary symptoms
      const primarySymptoms = symptoms.map(s => s.name.toLowerCase());
      
      // Get potential conditions based on symptoms
      const potentialConditions = this.getPotentialConditions(primarySymptoms);
      
      // Apply risk factors
      const adjustedConditions = this.applyRiskFactors(
        potentialConditions,
        { age: patientAge, gender: patientGender, medicalHistory }
      );
      
      // Generate differential diagnoses
      const differentialDiagnoses = this.generateDifferentialDiagnoses(
        adjustedConditions,
        symptoms
      );
      
      // Assess overall risk
      const riskAssessment = this.assessRisk(differentialDiagnoses, symptoms, {
        age: patientAge,
        gender: patientGender,
        vitalSigns,
      });
      
      // Generate recommendations
      const recommendations = this.generateRecommendations(
        differentialDiagnoses,
        riskAssessment
      );
      
      const processingTime = Date.now() - startTime;
      
      return {
        modelUsed: 'symptom_analyzer_v1.0',
        modelVersion: '1.0.0',
        analysisType: 'symptom_analysis',
        inputData: {
          symptoms,
          patientAge,
          patientGender,
          vitalSigns,
        },
        predictions: differentialDiagnoses,
        riskAssessment,
        recommendations,
        confidence: this.calculateOverallConfidence(differentialDiagnoses),
        processingTime,
        dataQuality: this.assessDataQuality(data),
      };
    } catch (error) {
      logger.error('Symptom analysis error:', error);
      throw new Error('Failed to analyze symptoms');
    }
  }

  // Comprehensive analysis including all available data
  async comprehensiveAnalysis(data) {
    try {
      const startTime = Date.now();
      
      const {
        symptoms,
        physicalExamination,
        diagnosticTests,
        patientAge,
        patientGender,
        vitalSigns,
        medicalHistory,
        currentMedications,
        existingDiagnosis,
      } = data;
      
      // Run symptom analysis
      const symptomAnalysis = await this.analyzeSymptoms({
        symptoms,
        patientAge,
        patientGender,
        vitalSigns,
        medicalHistory,
      });
      
      // Analyze drug interactions
      const drugInteractions = currentMedications
        ? this.analyzeDrugInteractions(currentMedications)
        : null;
      
      // Enhanced risk assessment with all data
      const enhancedRisk = this.enhancedRiskAssessment({
        symptoms,
        physicalExamination,
        diagnosticTests,
        patientAge,
        patientGender,
        vitalSigns,
        medicalHistory,
        drugInteractions,
      });
      
      // Generate comprehensive recommendations
      const comprehensiveRecommendations = this.generateComprehensiveRecommendations({
        symptomAnalysis,
        enhancedRisk,
        drugInteractions,
        existingDiagnosis,
        diagnosticTests,
      });
      
      const processingTime = Date.now() - startTime;
      
      return {
        modelUsed: 'comprehensive_analyzer_v1.0',
        modelVersion: '1.0.0',
        analysisType: 'comprehensive',
        inputData: data,
        predictions: symptomAnalysis.predictions,
        riskAssessment: enhancedRisk,
        recommendations: comprehensiveRecommendations,
        drugInteractions,
        confidence: this.calculateOverallConfidence(symptomAnalysis.predictions),
        processingTime,
        dataQuality: this.assessDataQuality(data),
      };
    } catch (error) {
      logger.error('Comprehensive analysis error:', error);
      throw new Error('Failed to perform comprehensive analysis');
    }
  }

  // Get potential conditions based on symptoms
  getPotentialConditions(symptoms) {
    const conditionMap = new Map();
    
    symptoms.forEach(symptom => {
      const symptomData = this.medicalKnowledgeBase.symptoms[symptom];
      if (symptomData) {
        symptomData.conditions.forEach(condition => {
          if (conditionMap.has(condition.name)) {
            const existing = conditionMap.get(condition.name);
            existing.probability = Math.min(1.0, existing.probability + condition.probability * 0.3);
            existing.urgency = this.getHigherUrgency(existing.urgency, condition.urgency);
          } else {
            conditionMap.set(condition.name, { ...condition });
          }
        });
      }
    });
    
    return Array.from(conditionMap.values());
  }

  // Apply risk factors to adjust probabilities
  applyRiskFactors(conditions, patientData) {
    const { age, gender, medicalHistory } = patientData;
    
    return conditions.map(condition => {
      let adjustedProbability = condition.probability;
      
      // Age-based adjustments
      if (age && this.medicalKnowledgeBase.riskFactors.age[condition.name.toLowerCase()]) {
        const ageRisk = this.medicalKnowledgeBase.riskFactors.age[condition.name.toLowerCase()];
        if (age >= ageRisk.threshold) {
          adjustedProbability *= ageRisk.multiplier;
        }
      }
      
      // Gender-based adjustments
      if (gender && this.medicalKnowledgeBase.riskFactors.gender[gender]) {
        const genderRisks = this.medicalKnowledgeBase.riskFactors.gender[gender];
        if (genderRisks.includes(condition.name.toLowerCase())) {
          adjustedProbability *= 1.2;
        }
      }
      
      // Medical history adjustments
      if (medicalHistory?.chronicConditions) {
        medicalHistory.chronicConditions.forEach(chronic => {
          if (this.isRelatedCondition(chronic.condition, condition.name)) {
            adjustedProbability *= 1.3;
          }
        });
      }
      
      return {
        ...condition,
        probability: Math.min(1.0, adjustedProbability),
      };
    });
  }

  // Generate differential diagnoses
  generateDifferentialDiagnoses(conditions, symptoms) {
    return conditions
      .sort((a, b) => b.probability - a.probability)
      .slice(0, 5)
      .map(condition => ({
        condition: condition.name,
        icdCode: this.getIcdCode(condition.name),
        probability: Math.round(condition.probability * 100),
        confidence: this.getConfidenceLevel(condition.probability),
        supportingEvidence: this.getSupportingEvidence(condition.name, symptoms),
        contradictingEvidence: this.getContradictingEvidence(condition.name, symptoms),
        recommendedTests: this.getRecommendedTests(condition.name),
        urgency: condition.urgency,
      }));
  }

  // Assess overall risk
  assessRisk(diagnoses, symptoms, patientData) {
    const highestUrgency = diagnoses.reduce((max, diagnosis) => {
      const urgencyLevels = { low: 1, medium: 2, high: 3, emergency: 4 };
      return urgencyLevels[diagnosis.urgency] > urgencyLevels[max] ? diagnosis.urgency : max;
    }, 'low');
    
    const riskFactors = this.identifyRiskFactors(symptoms, patientData);
    
    return {
      overallRisk: highestUrgency,
      riskFactors,
    };
  }

  // Generate recommendations
  generateRecommendations(diagnoses, riskAssessment) {
    const immediateActions = [];
    const diagnosticTests = [];
    const referrals = [];
    
    // Emergency actions
    if (riskAssessment.overallRisk === 'emergency') {
      immediateActions.push('Immediate emergency department evaluation');
      immediateActions.push('Continuous monitoring of vital signs');
    }
    
    // Diagnostic tests based on top diagnoses
    diagnoses.slice(0, 3).forEach(diagnosis => {
      diagnosis.recommendedTests.forEach(test => {
        if (!diagnosticTests.find(dt => dt.test === test)) {
          diagnosticTests.push({
            test,
            priority: diagnosis.urgency,
            reasoning: `To evaluate for ${diagnosis.condition}`,
          });
        }
      });
    });
    
    // Referrals based on conditions
    const specialtyMap = {
      'Myocardial Infarction': 'cardiology',
      'Angina': 'cardiology',
      'Pneumonia': 'pulmonology',
      'Appendicitis': 'surgery',
    };
    
    diagnoses.forEach(diagnosis => {
      const specialty = specialtyMap[diagnosis.condition];
      if (specialty && diagnosis.urgency !== 'low') {
        referrals.push({
          specialty,
          urgency: diagnosis.urgency === 'emergency' ? 'emergency' : 'urgent',
          reason: `Evaluation and management of ${diagnosis.condition}`,
        });
      }
    });
    
    return {
      immediateActions,
      diagnosticTests,
      referrals,
      followUp: {
        timeframe: riskAssessment.overallRisk === 'high' ? '24-48 hours' : '1-2 weeks',
        instructions: 'Monitor symptoms and return if worsening',
      },
    };
  }

  // Analyze drug interactions
  analyzeDrugInteractions(medications) {
    const interactions = [];
    
    for (let i = 0; i < medications.length; i++) {
      for (let j = i + 1; j < medications.length; j++) {
        const drug1 = medications[i].name.toLowerCase();
        const drug2 = medications[j].name.toLowerCase();
        
        const interaction = this.checkDrugInteraction(drug1, drug2);
        if (interaction) {
          interactions.push({
            drug1: medications[i].name,
            drug2: medications[j].name,
            severity: interaction.severity,
            description: interaction.description,
            recommendation: interaction.recommendation,
          });
        }
      }
    }
    
    return interactions;
  }

  // Enhanced risk assessment
  enhancedRiskAssessment(data) {
    const baseRisk = this.assessRisk([], data.symptoms, data);
    
    // Additional risk factors from physical exam and tests
    const additionalRisks = [];
    
    if (data.vitalSigns) {
      if (data.vitalSigns.bloodPressure?.systolic > 180) {
        additionalRisks.push({
          factor: 'Severe Hypertension',
          impact: 'high',
          description: 'Systolic BP > 180 mmHg indicates hypertensive crisis risk',
        });
      }
      
      if (data.vitalSigns.heartRate?.value > 120) {
        additionalRisks.push({
          factor: 'Tachycardia',
          impact: 'medium',
          description: 'Heart rate > 120 bpm may indicate underlying pathology',
        });
      }
    }
    
    return {
      ...baseRisk,
      riskFactors: [...baseRisk.riskFactors, ...additionalRisks],
    };
  }

  // Generate comprehensive recommendations
  generateComprehensiveRecommendations(data) {
    const baseRecommendations = this.generateRecommendations(
      data.symptomAnalysis.predictions,
      data.enhancedRisk
    );
    
    // Add drug interaction recommendations
    if (data.drugInteractions && data.drugInteractions.length > 0) {
      baseRecommendations.immediateActions.push(
        'Review medication interactions with pharmacist'
      );
    }
    
    return baseRecommendations;
  }

  // Helper methods
  getHigherUrgency(urgency1, urgency2) {
    const levels = { low: 1, medium: 2, high: 3, emergency: 4 };
    return levels[urgency1] > levels[urgency2] ? urgency1 : urgency2;
  }

  getConfidenceLevel(probability) {
    if (probability >= 0.7) return 'high';
    if (probability >= 0.4) return 'medium';
    return 'low';
  }

  getIcdCode(condition) {
    const icdCodes = {
      'Myocardial Infarction': 'I21.9',
      'Angina': 'I20.9',
      'Pneumonia': 'J18.9',
      'Appendicitis': 'K37',
      'Gastritis': 'K29.7',
    };
    return icdCodes[condition] || 'Unknown';
  }

  getSupportingEvidence(condition, symptoms) {
    // Simplified evidence mapping
    return symptoms.map(s => s.name).slice(0, 3);
  }

  getContradictingEvidence(condition, symptoms) {
    // Simplified - in real implementation, this would be more sophisticated
    return [];
  }

  getRecommendedTests(condition) {
    const testMap = {
      'Myocardial Infarction': ['ECG', 'Troponin', 'Chest X-ray'],
      'Pneumonia': ['Chest X-ray', 'CBC', 'Blood cultures'],
      'Appendicitis': ['CT abdomen', 'CBC', 'Urinalysis'],
    };
    return testMap[condition] || ['Basic metabolic panel'];
  }

  identifyRiskFactors(symptoms, patientData) {
    const factors = [];
    
    if (patientData.age > 65) {
      factors.push({
        factor: 'Advanced Age',
        impact: 'medium',
        description: 'Age > 65 increases risk for various conditions',
      });
    }
    
    return factors;
  }

  checkDrugInteraction(drug1, drug2) {
    const interactions = this.medicalKnowledgeBase.drugInteractions;
    
    if (interactions[drug1]?.dangerous?.includes(drug2)) {
      return {
        severity: 'dangerous',
        description: `Dangerous interaction between ${drug1} and ${drug2}`,
        recommendation: 'Avoid combination or monitor closely',
      };
    }
    
    if (interactions[drug1]?.moderate?.includes(drug2)) {
      return {
        severity: 'moderate',
        description: `Moderate interaction between ${drug1} and ${drug2}`,
        recommendation: 'Monitor for side effects',
      };
    }
    
    return null;
  }

  isRelatedCondition(condition1, condition2) {
    // Simplified relationship checking
    const related = {
      diabetes: ['cardiovascular', 'kidney'],
      hypertension: ['cardiovascular', 'stroke'],
    };
    
    return related[condition1.toLowerCase()]?.some(r => 
      condition2.toLowerCase().includes(r)
    ) || false;
  }

  calculateOverallConfidence(diagnoses) {
    if (!diagnoses || diagnoses.length === 0) return 0;
    
    const topDiagnosis = diagnoses[0];
    return topDiagnosis.probability;
  }

  assessDataQuality(data) {
    let completeness = 0;
    let totalFields = 0;
    
    // Check data completeness
    const fields = ['symptoms', 'patientAge', 'patientGender', 'vitalSigns'];
    fields.forEach(field => {
      totalFields++;
      if (data[field]) completeness++;
    });
    
    return {
      completeness: Math.round((completeness / totalFields) * 100),
      reliability: 85, // Simplified reliability score
      issues: data.symptoms?.length < 2 ? ['Insufficient symptom data'] : [],
    };
  }
}

// Export singleton instance
module.exports = new MLService();