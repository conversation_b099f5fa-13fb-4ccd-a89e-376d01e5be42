const mongoose = require('mongoose');

const PaymentSchema = new mongoose.Schema(
  {
    provider: { type: String, enum: ['paystack', 'flutterwave'], required: true },
    status: { type: String },
    email: { type: String },
    amount: { type: Number },
    currency: { type: String },
    reference: { type: String },
    tx_ref: { type: String },
    raw: {},
  },
  { timestamps: true }
);

module.exports = mongoose.model('Payment', PaymentSchema);

