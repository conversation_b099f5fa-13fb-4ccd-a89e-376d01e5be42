import { useState, useEffect, useCallback } from 'react';
import { mlService, MLAnalysisRequest, MLAnalysisResult, MLModel, MLAnalysisHistory } from '../services/mlService';

export const useMLAnalysis = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentAnalysis, setCurrentAnalysis] = useState<MLAnalysisResult | null>(null);

  const createAnalysis = async (analysisData: MLAnalysisRequest): Promise<MLAnalysisResult | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await mlService.createAnalysis(analysisData);
      
      if (response.success && response.data) {
        setCurrentAnalysis(response.data);
        return response.data;
      } else {
        setError(response.error || 'Failed to create ML analysis');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create ML analysis');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const deleteAnalysis = async (analysisId: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await mlService.deleteAnalysis(analysisId);
      
      if (response.success) {
        if (currentAnalysis?._id === analysisId) {
          setCurrentAnalysis(null);
        }
        return true;
      } else {
        setError(response.error || 'Failed to delete analysis');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete analysis');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    currentAnalysis,
    createAnalysis,
    deleteAnalysis,
    setCurrentAnalysis
  };
};

export const useMLAnalysisById = (analysisId: string | null) => {
  const [analysis, setAnalysis] = useState<MLAnalysisResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalysis = useCallback(async () => {
    if (!analysisId) {
      setAnalysis(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await mlService.getAnalysis(analysisId);
      
      if (response.success && response.data) {
        setAnalysis(response.data);
      } else {
        setError(response.error || 'Failed to fetch ML analysis');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch ML analysis');
    } finally {
      setLoading(false);
    }
  }, [analysisId]);

  useEffect(() => {
    fetchAnalysis();
  }, [fetchAnalysis]);

  return {
    analysis,
    loading,
    error,
    refetch: fetchAnalysis
  };
};

export const useMLAnalysisHistory = (patientId?: string, page: number = 1, limit: number = 10, analysisType?: string) => {
  const [history, setHistory] = useState<MLAnalysisResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const fetchHistory = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      let response;
      if (patientId) {
        response = await mlService.getPatientAnalyses(patientId, page, limit);
      } else {
        response = await mlService.getAllAnalyses(page, limit, analysisType);
      }
      
      if (response.success && response.data) {
        setHistory(response.data.analyses);
        setTotal(response.data.total);
        setTotalPages(Math.ceil(response.data.total / limit));
      } else {
        setError(response.error || 'Failed to fetch ML analysis history');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch ML analysis history');
    } finally {
      setLoading(false);
    }
  }, [patientId, page, limit, analysisType]);

  useEffect(() => {
    fetchHistory();
  }, [fetchHistory]);

  return {
    history,
    loading,
    error,
    total,
    totalPages,
    refetch: fetchHistory
  };
};

export const useMLModels = () => {
  const [models, setModels] = useState<MLModel[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchModels = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await mlService.getAvailableModels();
      
      if (response.success && response.data) {
        setModels(response.data);
      } else {
        setError(response.error || 'Failed to fetch ML models');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch ML models');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchModels();
  }, [fetchModels]);

  return {
    models,
    loading,
    error,
    refetch: fetchModels
  };
};

export const useMLModel = (modelId: string | null) => {
  const [model, setModel] = useState<MLModel | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchModel = useCallback(async () => {
    if (!modelId) {
      setModel(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await mlService.getModelDetails(modelId);
      
      if (response.success && response.data) {
        setModel(response.data);
      } else {
        setError(response.error || 'Failed to fetch ML model details');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch ML model details');
    } finally {
      setLoading(false);
    }
  }, [modelId]);

  useEffect(() => {
    fetchModel();
  }, [fetchModel]);

  return {
    model,
    loading,
    error,
    refetch: fetchModel
  };
};

export const useSymptomPrediction = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [predictions, setPredictions] = useState<unknown>(null);

  const predictSymptoms = async (symptoms: string[]) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await mlService.predictSymptoms(symptoms);
      
      if (response.success && response.data) {
        setPredictions(response.data);
        return response.data;
      } else {
        setError(response.error || 'Failed to predict symptoms');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to predict symptoms');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    predictions,
    predictSymptoms,
    setPredictions
  };
};

export const useRiskAssessment = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [assessment, setAssessment] = useState<unknown>(null);

  const assessRisk = async (patientData: unknown) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await mlService.assessRisk(patientData);
      
      if (response.success && response.data) {
        setAssessment(response.data);
        return response.data;
      } else {
        setError(response.error || 'Failed to assess risk');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to assess risk');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    assessment,
    assessRisk,
    setAssessment
  };
};

export const useDrugInteractionCheck = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [interactions, setInteractions] = useState<unknown>(null);

  const checkInteractions = async (medications: string[]) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await mlService.checkDrugInteractions(medications);
      
      if (response.success && response.data) {
        setInteractions(response.data);
        return response.data;
      } else {
        setError(response.error || 'Failed to check drug interactions');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to check drug interactions');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    interactions,
    checkInteractions,
    setInteractions
  };
};

export const useMLTraining = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [trainingJob, setTrainingJob] = useState<unknown>(null);
  const [trainingStatus, setTrainingStatus] = useState<unknown>(null);

  const startTraining = async (modelType: string, trainingData: unknown[], options?: unknown) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await mlService.trainModel(modelType, trainingData, options);
      
      if (response.success && response.data) {
        setTrainingJob(response.data);
        return response.data;
      } else {
        setError(response.error || 'Failed to start training');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start training');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const checkTrainingStatus = async (jobId: string) => {
    try {
      const response = await mlService.getTrainingStatus(jobId);
      
      if (response.success && response.data) {
        setTrainingStatus(response.data);
        return response.data;
      } else {
        setError(response.error || 'Failed to check training status');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to check training status');
      return null;
    }
  };

  return {
    loading,
    error,
    trainingJob,
    trainingStatus,
    startTraining,
    checkTrainingStatus,
    setTrainingJob,
    setTrainingStatus
  };
};

export const useMLStatistics = () => {
  const [statistics, setStatistics] = useState<unknown>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStatistics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await mlService.getMLStatistics();
      
      if (response.success && response.data) {
        setStatistics(response.data);
      } else {
        setError(response.error || 'Failed to fetch ML statistics');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch ML statistics');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  return {
    statistics,
    loading,
    error,
    refetch: fetchStatistics
  };
};