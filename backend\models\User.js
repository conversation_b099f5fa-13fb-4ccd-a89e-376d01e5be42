const mongoose = require('mongoose');

const UserSchema = new mongoose.Schema({
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
  },
  password: {
    type: String,
    required: function() {
      // Password not required for social auth users
      return !this.socialAuth || Object.keys(this.socialAuth).length === 0;
    },
    minlength: 6,
    select: false, // Don't include password in queries by default
  },
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters'],
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters'],
  },
  role: {
    type: String,
    required: [true, 'Role is required'],
    enum: {
      values: ['doctor', 'nurse', 'admin', 'patient'],
      message: 'Role must be either doctor, nurse, admin, or patient',
    },
  },
  specialty: {
    type: String,
    required: function() {
      return this.role === 'doctor';
    },
    enum: {
      values: [
        'cardiology',
        'dermatology',
        'endocrinology',
        'gastroenterology',
        'neurology',
        'oncology',
        'orthopedics',
        'pediatrics',
        'psychiatry',
        'radiology',
        'surgery',
        'urology',
        'general_practice',
        'emergency_medicine',
        'internal_medicine',
        'family_medicine',
      ],
      message: 'Please select a valid medical specialty',
    },
  },
  licenseNumber: {
    type: String,
    required: function() {
      return ['doctor', 'nurse'].includes(this.role);
    },
    unique: true,
    sparse: true, // Allow null values but ensure uniqueness when present
  },
  phoneNumber: {
    type: String,
    validator: {
      validator: function(v) {
        return !v || validator.isMobilePhone(v);
      },
      message: 'Please provide a valid phone number',
    },
  },
  dateOfBirth: {
    type: Date,
    required: false, // We'll validate this in the route
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'other', 'prefer_not_to_say'],
  },
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: {
      type: String,
      default: 'United States',
    },
  },
  emergencyContact: {
    name: String,
    relationship: String,
    phoneNumber: String,
  },
  medicalHistory: {
    allergies: [String],
    chronicConditions: [String],
    currentMedications: [{
      name: String,
      dosage: String,
      frequency: String,
      prescribedBy: String,
      startDate: Date,
    }],
    surgicalHistory: [{
      procedure: String,
      date: Date,
      surgeon: String,
      notes: String,
    }],
  },
  preferences: {
    language: {
      type: String,
      default: 'en',
    },
    timezone: {
      type: String,
      default: 'America/New_York',
    },
    notifications: {
      email: {
        type: Boolean,
        default: true,
      },
      sms: {
        type: Boolean,
        default: false,
      },
      push: {
        type: Boolean,
        default: true,
      },
    },
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  verificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date,
  lastLogin: Date,
  loginAttempts: {
    type: Number,
    default: 0,
  },
  lockUntil: Date,

  // Social Authentication
  socialAuth: {
    google: {
      id: String,
      email: String,
      verified: Boolean,
    },
    facebook: {
      id: String,
      email: String,
    },
    github: {
      id: String,
      username: String,
      email: String,
    },
    twitter: {
      id: String,
      username: String,
    },
    linkedin: {
      id: String,
      email: String,
    },
    apple: {
      id: String,
      email: String,
    },
  },

  // Profile information from social providers
  profilePicture: {
    url: String,
    provider: String, // 'google', 'facebook', 'github', etc.
  },

  // Track registration method
  registrationMethod: {
    type: String,
    enum: ['email', 'google', 'facebook', 'github', 'twitter', 'linkedin', 'apple'],
    default: 'email',
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Virtual for full name
UserSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for account lock status
UserSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Indexes
UserSchema.index({ email: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ specialty: 1 });
UserSchema.index({ licenseNumber: 1 });
UserSchema.index({ createdAt: -1 });

// Methods
UserSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 },
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  
  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }
  
  return this.updateOne(updates);
};

UserSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
  });
};

module.exports = mongoose.model('User', UserSchema);