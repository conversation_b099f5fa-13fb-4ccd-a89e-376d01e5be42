const express = require('express');
const { body, query, validationResult } = require('express-validator');
const mlService = require('../services/mlService');
const { protect, restrictTo, requireDoctor } = require('../middleware/auth');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const Patient = require('../models/Patient');
const Diagnosis = require('../models/Diagnosis');

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, errors.array()));
  }
  next();
};

// Apply authentication to all routes
router.use(protect);
router.use(restrictTo('doctor', 'nurse', 'admin', 'medical_staff'));

// @route   POST /api/ml/analyze-symptoms
// @desc    Analyze symptoms using ML models
// @access  Private (Medical Staff)
router.post('/analyze-symptoms', [
  body('symptoms')
    .isArray({ min: 1 })
    .withMessage('Symptoms must be a non-empty array'),
  body('symptoms.*')
    .isString()
    .isLength({ min: 2, max: 100 })
    .withMessage('Each symptom must be a string between 2 and 100 characters'),
  body('patientAge')
    .optional()
    .isInt({ min: 0, max: 150 })
    .withMessage('Patient age must be between 0 and 150'),
  body('patientGender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('Patient gender must be male, female, or other'),
  body('medicalHistory')
    .optional()
    .isArray()
    .withMessage('Medical history must be an array'),
  body('severity')
    .optional()
    .isIn(['mild', 'moderate', 'severe', 'critical'])
    .withMessage('Severity must be mild, moderate, severe, or critical'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { symptoms, patientAge, patientGender, medicalHistory = [], severity = 'moderate' } = req.body;
    
    logger.info(`User ${req.user.id} analyzing symptoms: ${symptoms.join(', ')}`);
    
    const analysis = await mlService.analyzeSymptoms({
      symptoms,
      patientAge,
      patientGender,
      medicalHistory,
      severity,
    });
    
    res.status(200).json({
      status: 'success',
      data: {
        analysis,
        analyzedAt: new Date().toISOString(),
        analyzedBy: req.user.id,
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   POST /api/ml/risk-assessment
// @desc    Perform risk assessment for a patient
// @access  Private (Medical Staff)
router.post('/risk-assessment', [
  body('patientId')
    .optional()
    .isMongoId()
    .withMessage('Patient ID must be a valid MongoDB ObjectId'),
  body('patientData')
    .optional()
    .isObject()
    .withMessage('Patient data must be an object'),
  body('riskFactors')
    .isArray({ min: 1 })
    .withMessage('Risk factors must be a non-empty array'),
  body('riskFactors.*')
    .isString()
    .isLength({ min: 2, max: 100 })
    .withMessage('Each risk factor must be a string between 2 and 100 characters'),
  body('condition')
    .isString()
    .isLength({ min: 2, max: 100 })
    .withMessage('Condition must be a string between 2 and 100 characters'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { patientId, patientData, riskFactors, condition } = req.body;
    
    let patient = null;
    if (patientId) {
      patient = await Patient.findById(patientId);
      if (!patient) {
        return next(new AppError('Patient not found', 404));
      }
    }
    
    const assessmentData = patient ? {
      age: patient.age,
      gender: patient.gender,
      medicalHistory: patient.medicalHistory,
      riskFactors: patient.riskFactors,
      ...patientData,
    } : patientData;
    
    logger.info(`User ${req.user.id} performing risk assessment for condition: ${condition}`);
    
    const riskAssessment = await mlService.assessRisk({
      patientData: assessmentData,
      riskFactors,
      condition,
    });
    
    res.status(200).json({
      status: 'success',
      data: {
        riskAssessment,
        patientId: patientId || null,
        assessedAt: new Date().toISOString(),
        assessedBy: req.user.id,
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   POST /api/ml/drug-interactions
// @desc    Check for drug interactions
// @access  Private (Medical Staff)
router.post('/drug-interactions', [
  body('medications')
    .isArray({ min: 2 })
    .withMessage('At least 2 medications are required for interaction analysis'),
  body('medications.*')
    .isString()
    .isLength({ min: 2, max: 100 })
    .withMessage('Each medication must be a string between 2 and 100 characters'),
  body('patientId')
    .optional()
    .isMongoId()
    .withMessage('Patient ID must be a valid MongoDB ObjectId'),
  body('patientConditions')
    .optional()
    .isArray()
    .withMessage('Patient conditions must be an array'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { medications, patientId, patientConditions = [] } = req.body;
    
    let patient = null;
    if (patientId) {
      patient = await Patient.findById(patientId);
      if (!patient) {
        return next(new AppError('Patient not found', 404));
      }
    }
    
    const conditions = patient ? 
      [...patient.medicalHistory.map(h => h.condition), ...patientConditions] : 
      patientConditions;
    
    logger.info(`User ${req.user.id} checking drug interactions for: ${medications.join(', ')}`);
    
    const interactions = await mlService.checkDrugInteractions({
      medications,
      patientConditions: conditions,
      patientAge: patient?.age,
    });
    
    res.status(200).json({
      status: 'success',
      data: {
        interactions,
        medications,
        patientId: patientId || null,
        checkedAt: new Date().toISOString(),
        checkedBy: req.user.id,
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   POST /api/ml/treatment-recommendations
// @desc    Get AI-powered treatment recommendations
// @access  Private (Doctors only)
router.post('/treatment-recommendations', [
  requireDoctor,
  body('diagnosis')
    .isString()
    .isLength({ min: 2, max: 200 })
    .withMessage('Diagnosis must be a string between 2 and 200 characters'),
  body('symptoms')
    .isArray({ min: 1 })
    .withMessage('Symptoms must be a non-empty array'),
  body('patientProfile')
    .isObject()
    .withMessage('Patient profile must be an object'),
  body('patientProfile.age')
    .isInt({ min: 0, max: 150 })
    .withMessage('Patient age must be between 0 and 150'),
  body('patientProfile.gender')
    .isIn(['male', 'female', 'other'])
    .withMessage('Patient gender must be male, female, or other'),
  body('severity')
    .optional()
    .isIn(['mild', 'moderate', 'severe', 'critical'])
    .withMessage('Severity must be mild, moderate, severe, or critical'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { diagnosis, symptoms, patientProfile, severity = 'moderate' } = req.body;
    
    logger.info(`Doctor ${req.user.id} requesting treatment recommendations for: ${diagnosis}`);
    
    const recommendations = await mlService.generateTreatmentRecommendations({
      diagnosis,
      symptoms,
      patientProfile,
      severity,
    });
    
    res.status(200).json({
      status: 'success',
      data: {
        recommendations,
        diagnosis,
        generatedAt: new Date().toISOString(),
        generatedBy: req.user.id,
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   POST /api/ml/predict-outcomes
// @desc    Predict patient outcomes using ML models
// @access  Private (Doctors only)
router.post('/predict-outcomes', [
  requireDoctor,
  body('patientId')
    .isMongoId()
    .withMessage('Patient ID must be a valid MongoDB ObjectId'),
  body('treatmentPlan')
    .isObject()
    .withMessage('Treatment plan must be an object'),
  body('timeframe')
    .optional()
    .isIn(['1_week', '1_month', '3_months', '6_months', '1_year'])
    .withMessage('Timeframe must be 1_week, 1_month, 3_months, 6_months, or 1_year'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { patientId, treatmentPlan, timeframe = '1_month' } = req.body;
    
    const patient = await Patient.findById(patientId);
    if (!patient) {
      return next(new AppError('Patient not found', 404));
    }
    
    // Get recent diagnoses for the patient
    const recentDiagnoses = await Diagnosis.find({ patient: patientId })
      .sort({ createdAt: -1 })
      .limit(5);
    
    logger.info(`Doctor ${req.user.id} predicting outcomes for patient: ${patientId}`);
    
    const predictions = await mlService.predictOutcomes({
      patientData: {
        age: patient.age,
        gender: patient.gender,
        medicalHistory: patient.medicalHistory,
        vitalSigns: patient.latestVitalSigns,
        medications: patient.activeMedications,
      },
      treatmentPlan,
      recentDiagnoses: recentDiagnoses.map(d => ({
        diagnosis: d.primaryDiagnosis,
        severity: d.severity,
        date: d.createdAt,
      })),
      timeframe,
    });
    
    res.status(200).json({
      status: 'success',
      data: {
        predictions,
        patientId,
        timeframe,
        predictedAt: new Date().toISOString(),
        predictedBy: req.user.id,
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   POST /api/ml/analyze-diagnosis
// @desc    Analyze an existing diagnosis using ML
// @access  Private (Medical Staff)
router.post('/analyze-diagnosis/:diagnosisId', [
  body('includeAlternatives')
    .optional()
    .isBoolean()
    .withMessage('Include alternatives must be a boolean'),
  body('confidenceThreshold')
    .optional()
    .isFloat({ min: 0, max: 1 })
    .withMessage('Confidence threshold must be between 0 and 1'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { diagnosisId } = req.params;
    const { includeAlternatives = true, confidenceThreshold = 0.7 } = req.body;
    
    const diagnosis = await Diagnosis.findById(diagnosisId).populate('patient');
    if (!diagnosis) {
      return next(new AppError('Diagnosis not found', 404));
    }
    
    logger.info(`User ${req.user.id} analyzing diagnosis: ${diagnosisId}`);
    
    const analysis = await mlService.analyzeDiagnosis({
      diagnosis: {
        symptoms: diagnosis.symptoms,
        primaryDiagnosis: diagnosis.primaryDiagnosis,
        chiefComplaint: diagnosis.chiefComplaint,
        historyOfPresentIllness: diagnosis.historyOfPresentIllness,
        physicalExamination: diagnosis.physicalExamination,
      },
      patientData: {
        age: diagnosis.patient.age,
        gender: diagnosis.patient.gender,
        medicalHistory: diagnosis.patient.medicalHistory,
      },
      options: {
        includeAlternatives,
        confidenceThreshold,
      },
    });
    
    res.status(200).json({
      status: 'success',
      data: {
        analysis,
        diagnosisId,
        analyzedAt: new Date().toISOString(),
        analyzedBy: req.user.id,
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   GET /api/ml/models/status
// @desc    Get ML models status and performance metrics
// @access  Private (Admin only)
router.get('/models/status', [
  restrictTo('admin'),
], async (req, res, next) => {
  try {
    logger.info(`Admin ${req.user.id} checking ML models status`);
    
    const modelsStatus = await mlService.getModelsStatus();
    
    res.status(200).json({
      status: 'success',
      data: {
        modelsStatus,
        checkedAt: new Date().toISOString(),
        checkedBy: req.user.id,
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   POST /api/ml/models/retrain
// @desc    Trigger model retraining (admin only)
// @access  Private (Admin only)
router.post('/models/retrain', [
  restrictTo('admin'),
  body('modelType')
    .isIn(['symptom_analysis', 'risk_assessment', 'drug_interactions', 'outcome_prediction'])
    .withMessage('Invalid model type'),
  body('trainingData')
    .optional()
    .isObject()
    .withMessage('Training data must be an object'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { modelType, trainingData } = req.body;
    
    logger.info(`Admin ${req.user.id} triggering retraining for model: ${modelType}`);
    
    const retrainResult = await mlService.retrainModel(modelType, trainingData);
    
    res.status(200).json({
      status: 'success',
      data: {
        retrainResult,
        modelType,
        triggeredAt: new Date().toISOString(),
        triggeredBy: req.user.id,
      },
    });
  } catch (error) {
    next(error);
  }
});

// @route   GET /api/ml/analytics
// @desc    Get ML usage analytics
// @access  Private (Admin only)
router.get('/analytics', [
  restrictTo('admin'),
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date'),
  query('modelType')
    .optional()
    .isIn(['symptom_analysis', 'risk_assessment', 'drug_interactions', 'outcome_prediction'])
    .withMessage('Invalid model type'),
  handleValidationErrors,
], async (req, res, next) => {
  try {
    const { startDate, endDate, modelType } = req.query;
    
    logger.info(`Admin ${req.user.id} requesting ML analytics`);
    
    const analytics = await mlService.getAnalytics({
      startDate: startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: endDate ? new Date(endDate) : new Date(),
      modelType,
    });
    
    res.status(200).json({
      status: 'success',
      data: {
        analytics,
        generatedAt: new Date().toISOString(),
        generatedBy: req.user.id,
      },
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;