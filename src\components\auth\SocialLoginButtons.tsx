import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Github, Mail, Chrome, Facebook, Apple, Twitter, Linkedin } from 'lucide-react';
import { toast } from 'sonner';
import { socialAuthService } from '@/services/socialAuthService';

interface SocialLoginButtonsProps {
  mode?: 'login' | 'register';
  onError?: (error: string) => void;
  onLoading?: (loading: boolean) => void;
}

interface OAuthProvider {
  name: string;
  icon: React.ReactNode;
  color: string;
  enabled: boolean;
}

export const SocialLoginButtons: React.FC<SocialLoginButtonsProps> = ({
  mode = 'login',
  onError,
  onLoading
}) => {
  const [availableProviders, setAvailableProviders] = useState<string[]>([]);
  const [loading, setLoading] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);

  // Fetch available OAuth providers from backend
  useEffect(() => {
    const fetchProviders = async () => {
      try {
        setIsInitializing(true);
        const response = await socialAuthService.getAvailableProviders();

        if (response.success && response.providers) {
          setAvailableProviders(response.providers);
        } else {
          console.warn('No OAuth providers available:', response.error);
          onError?.(response.error || 'No social login providers available');
        }
      } catch (error) {
        console.error('Failed to fetch OAuth providers:', error);
        onError?.('Failed to load social login options');
      } finally {
        setIsInitializing(false);
      }
    };

    fetchProviders();
  }, [onError]);

  const handleSocialLogin = async (provider: string) => {
    try {
      setLoading(provider);
      onLoading?.(true);

      // Show loading toast
      toast.loading(`Connecting to ${provider}...`, {
        id: `${provider}-auth`
      });

      // Initiate OAuth flow
      await socialAuthService.initiateOAuth(provider, mode);

    } catch (error) {
      console.error(`${provider} ${mode} error:`, error);
      const errorMessage = error instanceof Error ? error.message : `Failed to ${mode} with ${provider}`;

      toast.error(errorMessage, {
        id: `${provider}-auth`
      });

      onError?.(errorMessage);
      setLoading(null);
      onLoading?.(false);
    }
  };

  // Define all possible OAuth providers
  const allProviders: Record<string, OAuthProvider> = {
    google: {
      name: 'Google',
      icon: <Chrome className="h-4 w-4" />,
      color: '#4285F4',
      enabled: availableProviders.includes('google')
    },
    github: {
      name: 'GitHub',
      icon: <Github className="h-4 w-4" />,
      color: '#333',
      enabled: availableProviders.includes('github')
    },
    facebook: {
      name: 'Facebook',
      icon: <Facebook className="h-4 w-4" />,
      color: '#1877F2',
      enabled: availableProviders.includes('facebook')
    },
    twitter: {
      name: 'Twitter',
      icon: <Twitter className="h-4 w-4" />,
      color: '#1DA1F2',
      enabled: availableProviders.includes('twitter')
    },
    linkedin: {
      name: 'LinkedIn',
      icon: <Linkedin className="h-4 w-4" />,
      color: '#0A66C2',
      enabled: availableProviders.includes('linkedin')
    },
    apple: {
      name: 'Apple',
      icon: <Apple className="h-4 w-4" />,
      color: '#000',
      enabled: availableProviders.includes('apple')
    }
  };

  // Filter enabled providers
  const enabledProviders = Object.entries(allProviders).filter(([_, provider]) => provider.enabled);

  // Show loading state while initializing
  if (isInitializing) {
    return (
      <div className="space-y-3">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-border" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              Loading social options...
            </span>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-3">
          {[1, 2].map((i) => (
            <Button
              key={i}
              variant="outline"
              className="neu-flat"
              disabled
            >
              <div className="h-4 w-4 mr-2 animate-pulse bg-gray-300 rounded" />
              <div className="h-4 w-16 animate-pulse bg-gray-300 rounded" />
            </Button>
          ))}
        </div>
      </div>
    );
  }

  // Don't render if no providers are available
  if (enabledProviders.length === 0) {
    return null;
  }

  return (
    <div className="space-y-3">
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-border" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or {mode === 'login' ? 'sign in' : 'sign up'} with
          </span>
        </div>
      </div>

      {/* Primary providers (Google and GitHub) */}
      <div className="grid grid-cols-2 gap-3">
        {enabledProviders.slice(0, 2).map(([key, provider]) => (
          <Button
            key={key}
            variant="outline"
            className="neu-flat hover:neu-raised transition-all duration-200 relative"
            onClick={() => handleSocialLogin(key)}
            disabled={loading === key || !!loading}
          >
            {loading === key ? (
              <>
                <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                Connecting...
              </>
            ) : (
              <>
                <span className="mr-2">{provider.icon}</span>
                {provider.name}
              </>
            )}
          </Button>
        ))}
      </div>

      {/* Additional providers */}
      {enabledProviders.length > 2 && (
        <div className="grid grid-cols-3 gap-2">
          {enabledProviders.slice(2).map(([key, provider]) => (
            <Button
              key={key}
              variant="outline"
              size="sm"
              className="neu-flat hover:neu-raised transition-all duration-200"
              onClick={() => handleSocialLogin(key)}
              disabled={loading === key || !!loading}
              title={`${mode === 'login' ? 'Sign in' : 'Sign up'} with ${provider.name}`}
            >
              {loading === key ? (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
              ) : (
                provider.icon
              )}
            </Button>
          ))}
        </div>
      )}

      {/* Debug info in development */}
      {import.meta.env.DEV && enabledProviders.length === 0 && (
        <div className="text-xs text-muted-foreground text-center p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded">
          No OAuth providers configured. Add credentials to enable social login.
        </div>
      )}
    </div>
  );
};