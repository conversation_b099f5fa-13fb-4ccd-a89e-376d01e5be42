import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, 
  Stethoscope, 
  Users, 
  FileText, 
  Search, 
  Upload,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Activity
} from 'lucide-react';
import { toast } from 'sonner';

import enhancedAIService, { PatientData, ConsultationResponse, MultiAgentConsultationResponse } from '@/services/enhancedAIService';

interface EnhancedAIConsultationProps {
  patientId?: string;
  onConsultationComplete?: (consultation: any) => void;
}

const EnhancedAIConsultation: React.FC<EnhancedAIConsultationProps> = ({
  patientId,
  onConsultationComplete
}) => {
  const [activeTab, setActiveTab] = useState('consultation');
  const [isLoading, setIsLoading] = useState(false);
  const [consultationProgress, setConsultationProgress] = useState(0);
  
  // Patient data form state
  const [patientData, setPatientData] = useState<PatientData>({
    patientId: patientId || '',
    chiefComplaint: '',
    symptoms: [],
    age: 0,
    gender: 'male',
    medicalHistory: [],
    medications: [],
    allergies: [],
    vitalSigns: {}
  });

  // Results state
  const [consultationResult, setConsultationResult] = useState<ConsultationResponse | null>(null);
  const [multiAgentResult, setMultiAgentResult] = useState<MultiAgentConsultationResponse | null>(null);
  const [literatureResults, setLiteratureResults] = useState<any>(null);
  const [realtimeResults, setRealtimeResults] = useState<any>(null);

  // Form helpers
  const [currentSymptom, setCurrentSymptom] = useState('');
  const [currentMedication, setCurrentMedication] = useState('');
  const [currentAllergy, setCurrentAllergy] = useState('');
  const [currentHistory, setCurrentHistory] = useState('');

  const addSymptom = () => {
    if (currentSymptom.trim()) {
      setPatientData(prev => ({
        ...prev,
        symptoms: [...prev.symptoms, currentSymptom.trim()]
      }));
      setCurrentSymptom('');
    }
  };

  const removeSymptom = (index: number) => {
    setPatientData(prev => ({
      ...prev,
      symptoms: prev.symptoms.filter((_, i) => i !== index)
    }));
  };

  const addMedication = () => {
    if (currentMedication.trim()) {
      setPatientData(prev => ({
        ...prev,
        medications: [...(prev.medications || []), currentMedication.trim()]
      }));
      setCurrentMedication('');
    }
  };

  const addAllergy = () => {
    if (currentAllergy.trim()) {
      setPatientData(prev => ({
        ...prev,
        allergies: [...(prev.allergies || []), currentAllergy.trim()]
      }));
      setCurrentAllergy('');
    }
  };

  const addHistory = () => {
    if (currentHistory.trim()) {
      setPatientData(prev => ({
        ...prev,
        medicalHistory: [...(prev.medicalHistory || []), currentHistory.trim()]
      }));
      setCurrentHistory('');
    }
  };

  const handleVitalSignChange = (field: string, value: string) => {
    setPatientData(prev => ({
      ...prev,
      vitalSigns: {
        ...prev.vitalSigns,
        [field]: value ? parseFloat(value) : undefined
      }
    }));
  };

  const validateForm = (): boolean => {
    if (!patientData.chiefComplaint.trim()) {
      toast.error('Chief complaint is required');
      return false;
    }
    if (patientData.symptoms.length === 0) {
      toast.error('At least one symptom is required');
      return false;
    }
    if (patientData.age <= 0 || patientData.age > 150) {
      toast.error('Please enter a valid age');
      return false;
    }
    return true;
  };

  const simulateProgress = () => {
    setConsultationProgress(0);
    const interval = setInterval(() => {
      setConsultationProgress(prev => {
        if (prev >= 95) {
          clearInterval(interval);
          return 95;
        }
        return prev + Math.random() * 15;
      });
    }, 500);
    return interval;
  };

  const handleExperiencedDoctorConsultation = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    const progressInterval = simulateProgress();

    try {
      const response = await enhancedAIService.conductConsultation(patientData, 'comprehensive');
      
      if (response.success && response.data) {
        setConsultationResult(response.data);
        setConsultationProgress(100);
        toast.success('Consultation completed successfully');
        onConsultationComplete?.(response.data);
      } else {
        throw new Error(response.error || 'Consultation failed');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to conduct consultation');
    } finally {
      clearInterval(progressInterval);
      setIsLoading(false);
      setTimeout(() => setConsultationProgress(0), 2000);
    }
  };

  const handleMultiAgentConsultation = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    const progressInterval = simulateProgress();

    try {
      const response = await enhancedAIService.conductMultiAgentConsultation(patientData, 'comprehensive');
      
      if (response.success && response.data) {
        setMultiAgentResult(response.data);
        setConsultationProgress(100);
        toast.success('Multi-agent consultation completed successfully');
        onConsultationComplete?.(response.data);
      } else {
        throw new Error(response.error || 'Multi-agent consultation failed');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to conduct multi-agent consultation');
    } finally {
      clearInterval(progressInterval);
      setIsLoading(false);
      setTimeout(() => setConsultationProgress(0), 2000);
    }
  };

  const handleLiteratureSearch = async () => {
    if (!patientData.chiefComplaint.trim()) {
      toast.error('Please enter a chief complaint to search');
      return;
    }

    setIsLoading(true);
    try {
      const response = await enhancedAIService.searchMedicalLiterature(
        `${patientData.chiefComplaint} ${patientData.symptoms.join(' ')}`,
        { maxResults: 10 }
      );
      
      if (response.success && response.data) {
        setLiteratureResults(response.data);
        toast.success('Literature search completed');
      } else {
        throw new Error(response.error || 'Literature search failed');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to search literature');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRealtimeSearch = async () => {
    if (!patientData.chiefComplaint.trim()) {
      toast.error('Please enter a chief complaint to search');
      return;
    }

    setIsLoading(true);
    try {
      const response = await enhancedAIService.getRealTimeMedicalInfo(
        patientData.chiefComplaint,
        { maxResults: 5 }
      );
      
      if (response.success && response.data) {
        setRealtimeResults(response.data);
        toast.success('Real-time search completed');
      } else {
        throw new Error(response.error || 'Real-time search failed');
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to search real-time information');
    } finally {
      setIsLoading(false);
    }
  };

  const renderConsultationForm = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="age">Age</Label>
          <Input
            id="age"
            type="number"
            value={patientData.age || ''}
            onChange={(e) => setPatientData(prev => ({ ...prev, age: parseInt(e.target.value) || 0 }))}
            placeholder="Patient age"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="gender">Gender</Label>
          <Select value={patientData.gender} onValueChange={(value: any) => setPatientData(prev => ({ ...prev, gender: value }))}>
            <SelectTrigger>
              <SelectValue placeholder="Select gender" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="male">Male</SelectItem>
              <SelectItem value="female">Female</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="chiefComplaint">Chief Complaint</Label>
        <Textarea
          id="chiefComplaint"
          value={patientData.chiefComplaint}
          onChange={(e) => setPatientData(prev => ({ ...prev, chiefComplaint: e.target.value }))}
          placeholder="Describe the main reason for the visit..."
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label>Symptoms</Label>
        <div className="flex gap-2">
          <Input
            value={currentSymptom}
            onChange={(e) => setCurrentSymptom(e.target.value)}
            placeholder="Add a symptom..."
            onKeyPress={(e) => e.key === 'Enter' && addSymptom()}
          />
          <Button onClick={addSymptom} variant="outline">Add</Button>
        </div>
        <div className="flex flex-wrap gap-2 mt-2">
          {patientData.symptoms.map((symptom, index) => (
            <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => removeSymptom(index)}>
              {symptom} ×
            </Badge>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Medications</Label>
          <div className="flex gap-2">
            <Input
              value={currentMedication}
              onChange={(e) => setCurrentMedication(e.target.value)}
              placeholder="Add medication..."
              onKeyPress={(e) => e.key === 'Enter' && addMedication()}
            />
            <Button onClick={addMedication} variant="outline" size="sm">Add</Button>
          </div>
          <div className="flex flex-wrap gap-1">
            {patientData.medications?.map((med, index) => (
              <Badge key={index} variant="outline" className="text-xs">{med}</Badge>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <Label>Allergies</Label>
          <div className="flex gap-2">
            <Input
              value={currentAllergy}
              onChange={(e) => setCurrentAllergy(e.target.value)}
              placeholder="Add allergy..."
              onKeyPress={(e) => e.key === 'Enter' && addAllergy()}
            />
            <Button onClick={addAllergy} variant="outline" size="sm">Add</Button>
          </div>
          <div className="flex flex-wrap gap-1">
            {patientData.allergies?.map((allergy, index) => (
              <Badge key={index} variant="destructive" className="text-xs">{allergy}</Badge>
            ))}
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Label>Medical History</Label>
        <div className="flex gap-2">
          <Input
            value={currentHistory}
            onChange={(e) => setCurrentHistory(e.target.value)}
            placeholder="Add medical history..."
            onKeyPress={(e) => e.key === 'Enter' && addHistory()}
          />
          <Button onClick={addHistory} variant="outline" size="sm">Add</Button>
        </div>
        <div className="flex flex-wrap gap-1">
          {patientData.medicalHistory?.map((history, index) => (
            <Badge key={index} variant="secondary" className="text-xs">{history}</Badge>
          ))}
        </div>
      </div>

      <div className="space-y-4">
        <Label>Vital Signs</Label>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="space-y-1">
            <Label className="text-xs">Systolic BP</Label>
            <Input
              type="number"
              placeholder="mmHg"
              value={patientData.vitalSigns?.systolicBP || ''}
              onChange={(e) => handleVitalSignChange('systolicBP', e.target.value)}
            />
          </div>
          <div className="space-y-1">
            <Label className="text-xs">Diastolic BP</Label>
            <Input
              type="number"
              placeholder="mmHg"
              value={patientData.vitalSigns?.diastolicBP || ''}
              onChange={(e) => handleVitalSignChange('diastolicBP', e.target.value)}
            />
          </div>
          <div className="space-y-1">
            <Label className="text-xs">Heart Rate</Label>
            <Input
              type="number"
              placeholder="bpm"
              value={patientData.vitalSigns?.heartRate || ''}
              onChange={(e) => handleVitalSignChange('heartRate', e.target.value)}
            />
          </div>
          <div className="space-y-1">
            <Label className="text-xs">Temperature</Label>
            <Input
              type="number"
              step="0.1"
              placeholder="°F"
              value={patientData.vitalSigns?.temperature || ''}
              onChange={(e) => handleVitalSignChange('temperature', e.target.value)}
            />
          </div>
          <div className="space-y-1">
            <Label className="text-xs">O2 Saturation</Label>
            <Input
              type="number"
              placeholder="%"
              value={patientData.vitalSigns?.oxygenSaturation || ''}
              onChange={(e) => handleVitalSignChange('oxygenSaturation', e.target.value)}
            />
          </div>
          <div className="space-y-1">
            <Label className="text-xs">Respiratory Rate</Label>
            <Input
              type="number"
              placeholder="breaths/min"
              value={patientData.vitalSigns?.respiratoryRate || ''}
              onChange={(e) => handleVitalSignChange('respiratoryRate', e.target.value)}
            />
          </div>
        </div>
      </div>

      {isLoading && consultationProgress > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Consultation in progress...</span>
            <span>{Math.round(consultationProgress)}%</span>
          </div>
          <Progress value={consultationProgress} className="w-full" />
        </div>
      )}

      <div className="flex gap-4">
        <Button 
          onClick={handleExperiencedDoctorConsultation}
          disabled={isLoading}
          className="flex-1"
        >
          <Brain className="w-4 h-4 mr-2" />
          {isLoading ? 'Consulting...' : 'Experienced Doctor Consultation'}
        </Button>
        <Button 
          onClick={handleMultiAgentConsultation}
          disabled={isLoading}
          variant="outline"
          className="flex-1"
        >
          <Users className="w-4 h-4 mr-2" />
          {isLoading ? 'Consulting...' : 'Multi-Agent Consultation'}
        </Button>
      </div>
    </div>
  );

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-bruno">
            <Stethoscope className="w-6 h-6" />
            Enhanced AI Medical Consultation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="consultation">Consultation</TabsTrigger>
              <TabsTrigger value="results">Results</TabsTrigger>
              <TabsTrigger value="literature">Literature</TabsTrigger>
              <TabsTrigger value="realtime">Real-time Info</TabsTrigger>
            </TabsList>

            <TabsContent value="consultation" className="mt-6">
              {renderConsultationForm()}
            </TabsContent>

            <TabsContent value="results" className="mt-6">
              {/* Results will be rendered here */}
              <div className="text-center text-muted-foreground">
                Consultation results will appear here after running a consultation.
              </div>
            </TabsContent>

            <TabsContent value="literature" className="mt-6">
              <div className="space-y-4">
                <Button onClick={handleLiteratureSearch} disabled={isLoading}>
                  <Search className="w-4 h-4 mr-2" />
                  Search Medical Literature
                </Button>
                {/* Literature results will be rendered here */}
              </div>
            </TabsContent>

            <TabsContent value="realtime" className="mt-6">
              <div className="space-y-4">
                <Button onClick={handleRealtimeSearch} disabled={isLoading}>
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Get Real-time Medical Information
                </Button>
                {/* Real-time results will be rendered here */}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedAIConsultation;
