const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { AppError } = require('../middleware/errorHandler');
const { protect } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET || 'fallback_secret', {
    expiresIn: process.env.JWT_EXPIRE || '7d',
  });
};

// Validation middleware
const validateRegistration = [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  body('firstName').trim().isLength({ min: 1 }).withMessage('First name is required'),
  body('lastName').trim().isLength({ min: 1 }).withMessage('Last name is required'),
  body('role').isIn(['doctor', 'nurse', 'admin', 'patient']).withMessage('Invalid role'),
  body('dateOfBirth').optional().isISO8601().withMessage('Invalid date format'),
  body('gender').optional().isIn(['male', 'female', 'other', 'prefer_not_to_say']).withMessage('Invalid gender'),
  body('phoneNumber').optional().isMobilePhone().withMessage('Invalid phone number'),
  body('specialty').optional().isString(),
  body('licenseNumber').optional().isString(),
];

const validateLogin = [
  body('email').isEmail().normalizeEmail(),
  body('password').exists().withMessage('Password is required'),
];

// @route   POST /api/auth/register
// @desc    Register a new user
// @access  Public
router.post('/register', validateRegistration, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400));
    }

    const { email, password, firstName, lastName, role, specialty, licenseNumber, phoneNumber, dateOfBirth, gender } = req.body;

    console.log('🔗 Registration request data:', {
      email,
      firstName,
      lastName,
      role,
      phoneNumber,
      dateOfBirth,
      gender,
      specialty,
      licenseNumber
    });

    // Manual validation for role-specific fields
    if (role === 'patient' && !dateOfBirth) {
      return res.status(400).json({
        status: 'error',
        message: 'Date of birth is required for patients'
      });
    }

    if (role === 'doctor' && !specialty) {
      return res.status(400).json({
        status: 'error',
        message: 'Medical specialty is required for doctors'
      });
    }

    if ((role === 'doctor' || role === 'nurse') && !licenseNumber) {
      return res.status(400).json({
        status: 'error',
        message: 'License number is required for medical professionals'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return next(new AppError('User already exists with this email', 400));
    }

    // Hash password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user data object
    const userData = {
      email,
      password: hashedPassword,
      firstName,
      lastName,
      role
    };

    // Add optional fields based on role
    if (phoneNumber) userData.phoneNumber = phoneNumber;

    if (role === 'patient') {
      if (dateOfBirth) userData.dateOfBirth = new Date(dateOfBirth);
      if (gender) userData.gender = gender;
    }

    if (role === 'doctor') {
      if (specialty) userData.specialty = specialty;
      if (licenseNumber) userData.licenseNumber = licenseNumber;
    }

    if (role === 'nurse') {
      if (licenseNumber) userData.licenseNumber = licenseNumber;
    }

    console.log('🔗 Creating user with data:', userData);

    const user = new User(userData);

    await user.save();

    // Generate token
    const token = generateToken(user._id);

    logger.info(`New user registered: ${email} with role: ${role}`);

    res.status(201).json({
      status: 'success',
      token,
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          specialty: user.specialty,
        },
      },
    });
  } catch (error) {
    logger.error('Registration error:', error);
    next(error);
  }
});

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', validateLogin, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400));
    }

    const { email, password } = req.body;

    // Find user and include password for comparison
    const user = await User.findOne({ email }).select('+password');
    if (!user) {
      return next(new AppError('Invalid credentials', 401));
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return next(new AppError('Invalid credentials', 401));
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate token
    const token = generateToken(user._id);

    logger.info(`User logged in: ${email}`);

    res.json({
      status: 'success',
      token,
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          specialty: user.specialty,
          lastLogin: user.lastLogin,
        },
      },
    });
  } catch (error) {
    logger.error('Login error:', error);
    next(error);
  }
});

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', protect, async (req, res, next) => {
  try {
    // This route requires authentication middleware
    const user = await User.findById(req.user.id);
    if (!user) {
      return next(new AppError('User not found', 404));
    }

    res.json({
      status: 'success',
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          specialty: user.specialty,
          createdAt: user.createdAt,
          lastLogin: user.lastLogin,
        },
      },
    });
  } catch (error) {
    logger.error('Get user error:', error);
    next(error);
  }
});

// @route   POST /api/auth/logout
// @desc    Logout user (client-side token removal)
// @access  Private
router.post('/logout', protect, (req, res) => {
  logger.info(`User logged out: ${req.user?.email || 'Unknown'}`);
  res.json({
    status: 'success',
    message: 'Logged out successfully',
  });
});

// @route   POST /api/auth/refresh
// @desc    Refresh JWT token
// @access  Private
router.post('/refresh', protect, async (req, res, next) => {
  try {
    // Generate new token
    const token = generateToken(req.user.id);
    
    logger.info(`Token refreshed for user: ${req.user.email}`);
    
    res.json({
      status: 'success',
      data: {
        token
      }
    });
  } catch (error) {
    logger.error('Token refresh error:', error);
    next(error);
  }
});

// @route   POST /api/auth/forgot-password
// @desc    Send password reset email
// @access  Public
router.post('/forgot-password', async (req, res, next) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return next(new AppError('Email is required', 400));
    }
    
    const user = await User.findOne({ email });
    if (!user) {
      // Don't reveal if user exists for security
      return res.json({
        status: 'success',
        message: 'If an account with that email exists, a password reset link has been sent.'
      });
    }
    
    // In a real app, you would send an email here
    logger.info(`Password reset requested for: ${email}`);
    
    res.json({
      status: 'success',
      message: 'If an account with that email exists, a password reset link has been sent.'
    });
  } catch (error) {
    logger.error('Forgot password error:', error);
    next(error);
  }
});

// @route   POST /api/auth/reset-password
// @desc    Reset password
// @access  Public
router.post('/reset-password', async (req, res, next) => {
  try {
    const { email, newPassword } = req.body;
    
    if (!email || !newPassword) {
      return next(new AppError('Email and new password are required', 400));
    }
    
    if (newPassword.length < 6) {
      return next(new AppError('Password must be at least 6 characters', 400));
    }
    
    const user = await User.findOne({ email });
    if (!user) {
      return next(new AppError('Invalid reset request', 400));
    }
    
    // Hash new password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    user.password = hashedPassword;
    await user.save();
    
    logger.info(`Password reset completed for: ${email}`);
    
    res.json({
      status: 'success',
      message: 'Password has been reset successfully'
    });
  } catch (error) {
    logger.error('Reset password error:', error);
    next(error);
  }
});

// @route   PUT /api/auth/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', protect, async (req, res, next) => {
  try {
    const { firstName, lastName, phoneNumber, address } = req.body;
    
    const user = await User.findById(req.user.id);
    if (!user) {
      return next(new AppError('User not found', 404));
    }
    
    // Update allowed fields
    if (firstName) user.firstName = firstName;
    if (lastName) user.lastName = lastName;
    if (phoneNumber) user.phoneNumber = phoneNumber;
    if (address) user.address = { ...user.address, ...address };
    
    await user.save();
    
    logger.info(`Profile updated for user: ${user.email}`);
    
    res.json({
      status: 'success',
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          phoneNumber: user.phoneNumber,
          address: user.address
        }
      }
    });
  } catch (error) {
    logger.error('Profile update error:', error);
    next(error);
  }
});

// @route   PUT /api/auth/change-password
// @desc    Change user password
// @access  Private
router.put('/change-password', protect, async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;
    
    if (!currentPassword || !newPassword) {
      return next(new AppError('Current password and new password are required', 400));
    }
    
    if (newPassword.length < 6) {
      return next(new AppError('New password must be at least 6 characters', 400));
    }
    
    const user = await User.findById(req.user.id).select('+password');
    if (!user) {
      return next(new AppError('User not found', 404));
    }
    
    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      return next(new AppError('Current password is incorrect', 400));
    }
    
    // Hash new password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    user.password = hashedPassword;
    await user.save();
    
    logger.info(`Password changed for user: ${user.email}`);
    
    res.json({
      status: 'success',
      message: 'Password changed successfully'
    });
  } catch (error) {
    logger.error('Change password error:', error);
    next(error);
  }
});

module.exports = router;