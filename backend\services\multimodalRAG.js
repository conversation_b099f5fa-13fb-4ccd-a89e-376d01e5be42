const axios = require('axios');
const logger = require('../utils/logger');
const { OpenAI } = require('openai');
const { ChromaClient } = require('chromadb');
const sharp = require('sharp');
const pdf = require('pdf-parse');
const mammoth = require('mammoth');

/**
 * Multimodal RAG System for Medical AI Assistant
 * Handles text, images, documents, and structured medical data
 */
class MultimodalRAGSystem {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    
    this.chromaClient = new ChromaClient({
      path: process.env.CHROMA_DB_PATH || 'http://localhost:8000'
    });
    
    this.collections = {};
    this.isInitialized = false;
    
    this.initialize();
  }

  async initialize() {
    try {
      // Initialize different collections for different modalities
      await this.initializeCollections();
      
      // Load pre-existing medical knowledge
      await this.loadMedicalKnowledge();
      
      this.isInitialized = true;
      logger.info('Multimodal RAG System initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Multimodal RAG System:', error);
    }
  }

  async initializeCollections() {
    // Text-based medical literature
    this.collections.medicalTexts = await this.chromaClient.getOrCreateCollection({
      name: 'medical_texts',
      metadata: { 
        description: 'Medical literature, guidelines, and textbooks',
        modality: 'text'
      }
    });

    // Medical images and radiology
    this.collections.medicalImages = await this.chromaClient.getOrCreateCollection({
      name: 'medical_images',
      metadata: { 
        description: 'Medical images, X-rays, CT scans, MRIs with descriptions',
        modality: 'image'
      }
    });

    // Clinical documents
    this.collections.clinicalDocs = await this.chromaClient.getOrCreateCollection({
      name: 'clinical_documents',
      metadata: { 
        description: 'Clinical notes, discharge summaries, case reports',
        modality: 'document'
      }
    });

    // Drug information and interactions
    this.collections.drugDatabase = await this.chromaClient.getOrCreateCollection({
      name: 'drug_database',
      metadata: { 
        description: 'Drug information, interactions, contraindications',
        modality: 'structured'
      }
    });

    // Laboratory values and reference ranges
    this.collections.labValues = await this.chromaClient.getOrCreateCollection({
      name: 'lab_values',
      metadata: { 
        description: 'Laboratory reference ranges and interpretations',
        modality: 'structured'
      }
    });

    logger.info('All RAG collections initialized');
  }

  /**
   * Process and store medical text documents
   */
  async processTextDocument(document, metadata = {}) {
    try {
      let text = '';
      
      // Handle different document types
      if (document.type === 'pdf') {
        const pdfData = await pdf(document.buffer);
        text = pdfData.text;
      } else if (document.type === 'docx') {
        const docxData = await mammoth.extractRawText({ buffer: document.buffer });
        text = docxData.value;
      } else if (document.type === 'txt') {
        text = document.buffer.toString('utf-8');
      }

      // Chunk the text for better retrieval
      const chunks = this.chunkText(text, 1000, 200); // 1000 chars with 200 overlap
      
      // Generate embeddings and store
      const embeddings = await this.generateTextEmbeddings(chunks);
      
      const documents = chunks.map((chunk, index) => ({
        id: `${metadata.id || Date.now()}_chunk_${index}`,
        document: chunk,
        metadata: {
          ...metadata,
          chunk_index: index,
          total_chunks: chunks.length,
          document_type: document.type,
          processed_at: new Date().toISOString()
        }
      }));

      await this.collections.medicalTexts.add({
        ids: documents.map(doc => doc.id),
        documents: documents.map(doc => doc.document),
        metadatas: documents.map(doc => doc.metadata),
        embeddings: embeddings
      });

      logger.info(`Processed and stored text document: ${metadata.title || 'Unknown'}`);
      return { success: true, chunks: chunks.length };
    } catch (error) {
      logger.error('Error processing text document:', error);
      throw error;
    }
  }

  /**
   * Process and store medical images
   */
  async processImageDocument(image, metadata = {}) {
    try {
      // Process image with Sharp for optimization
      const processedImage = await sharp(image.buffer)
        .resize(512, 512, { fit: 'inside', withoutEnlargement: true })
        .jpeg({ quality: 80 })
        .toBuffer();

      // Generate image description using GPT-4 Vision
      const imageDescription = await this.generateImageDescription(processedImage, metadata);
      
      // Generate embeddings for the description
      const embedding = await this.generateTextEmbeddings([imageDescription]);
      
      // Store image metadata and description
      await this.collections.medicalImages.add({
        ids: [metadata.id || `img_${Date.now()}`],
        documents: [imageDescription],
        metadatas: [{
          ...metadata,
          image_type: metadata.imageType || 'unknown',
          processed_at: new Date().toISOString(),
          has_image_data: true
        }],
        embeddings: embedding
      });

      logger.info(`Processed and stored medical image: ${metadata.title || 'Unknown'}`);
      return { success: true, description: imageDescription };
    } catch (error) {
      logger.error('Error processing image document:', error);
      throw error;
    }
  }

  /**
   * Generate image description using GPT-4 Vision
   */
  async generateImageDescription(imageBuffer, metadata = {}) {
    try {
      const base64Image = imageBuffer.toString('base64');
      
      const response = await this.openai.chat.completions.create({
        model: "gpt-4-vision-preview",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `As an experienced radiologist with 25 years of experience, analyze this medical image. Provide a detailed description including:
                1. Image type and quality
                2. Anatomical structures visible
                3. Any abnormalities or pathological findings
                4. Clinical significance
                5. Differential diagnoses if applicable
                
                Context: ${metadata.context || 'Medical imaging study'}`
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/jpeg;base64,${base64Image}`
                }
              }
            ]
          }
        ],
        max_tokens: 1000
      });

      return response.choices[0].message.content;
    } catch (error) {
      logger.error('Error generating image description:', error);
      return 'Unable to generate image description';
    }
  }

  /**
   * Retrieve relevant information using multimodal query
   */
  async retrieveRelevantInformation(query, options = {}) {
    try {
      const {
        includeTexts = true,
        includeImages = false,
        includeDrugs = true,
        includeLabs = true,
        maxResults = 10
      } = options;

      const results = {
        texts: [],
        images: [],
        drugs: [],
        labs: [],
        combined_score: 0
      };

      // Generate query embedding
      const queryEmbedding = await this.generateTextEmbeddings([query]);

      // Search text collection
      if (includeTexts) {
        const textResults = await this.collections.medicalTexts.query({
          queryEmbeddings: queryEmbedding,
          nResults: maxResults,
          include: ['documents', 'metadatas', 'distances']
        });
        
        results.texts = this.formatResults(textResults, 'text');
      }

      // Search image collection
      if (includeImages) {
        const imageResults = await this.collections.medicalImages.query({
          queryEmbeddings: queryEmbedding,
          nResults: Math.min(maxResults, 5), // Fewer image results
          include: ['documents', 'metadatas', 'distances']
        });
        
        results.images = this.formatResults(imageResults, 'image');
      }

      // Search drug database
      if (includeDrugs) {
        const drugResults = await this.collections.drugDatabase.query({
          queryEmbeddings: queryEmbedding,
          nResults: maxResults,
          include: ['documents', 'metadatas', 'distances']
        });
        
        results.drugs = this.formatResults(drugResults, 'drug');
      }

      // Search lab values
      if (includeLabs) {
        const labResults = await this.collections.labValues.query({
          queryEmbeddings: queryEmbedding,
          nResults: maxResults,
          include: ['documents', 'metadatas', 'distances']
        });
        
        results.labs = this.formatResults(labResults, 'lab');
      }

      // Calculate combined relevance score
      results.combined_score = this.calculateCombinedScore(results);

      logger.info(`Retrieved multimodal information for query: ${query.substring(0, 50)}...`);
      return results;
    } catch (error) {
      logger.error('Error retrieving multimodal information:', error);
      throw error;
    }
  }

  /**
   * Generate embeddings for text
   */
  async generateTextEmbeddings(texts) {
    try {
      const response = await this.openai.embeddings.create({
        model: "text-embedding-ada-002",
        input: texts
      });

      return response.data.map(item => item.embedding);
    } catch (error) {
      logger.error('Error generating text embeddings:', error);
      throw error;
    }
  }

  /**
   * Chunk text into smaller pieces for better retrieval
   */
  chunkText(text, chunkSize = 1000, overlap = 200) {
    const chunks = [];
    let start = 0;
    
    while (start < text.length) {
      const end = Math.min(start + chunkSize, text.length);
      const chunk = text.slice(start, end);
      
      // Try to break at sentence boundaries
      if (end < text.length) {
        const lastPeriod = chunk.lastIndexOf('.');
        const lastNewline = chunk.lastIndexOf('\n');
        const breakPoint = Math.max(lastPeriod, lastNewline);
        
        if (breakPoint > start + chunkSize * 0.5) {
          chunks.push(text.slice(start, start + breakPoint + 1));
          start = start + breakPoint + 1 - overlap;
        } else {
          chunks.push(chunk);
          start = end - overlap;
        }
      } else {
        chunks.push(chunk);
        break;
      }
    }
    
    return chunks.filter(chunk => chunk.trim().length > 0);
  }

  /**
   * Format search results for consistent output
   */
  formatResults(searchResults, type) {
    if (!searchResults.documents || !searchResults.documents[0]) {
      return [];
    }

    return searchResults.documents[0].map((doc, index) => ({
      content: doc,
      metadata: searchResults.metadatas[0][index],
      relevance_score: 1 - searchResults.distances[0][index], // Convert distance to similarity
      type: type
    }));
  }

  /**
   * Calculate combined relevance score across modalities
   */
  calculateCombinedScore(results) {
    let totalScore = 0;
    let totalItems = 0;

    Object.values(results).forEach(modalityResults => {
      if (Array.isArray(modalityResults)) {
        modalityResults.forEach(result => {
          totalScore += result.relevance_score || 0;
          totalItems++;
        });
      }
    });

    return totalItems > 0 ? totalScore / totalItems : 0;
  }

  /**
   * Load pre-existing medical knowledge into the system
   */
  async loadMedicalKnowledge() {
    try {
      // Load drug interaction database
      await this.loadDrugInteractions();
      
      // Load laboratory reference ranges
      await this.loadLabReferenceRanges();
      
      // Load clinical guidelines
      await this.loadClinicalGuidelines();
      
      logger.info('Medical knowledge loaded into RAG system');
    } catch (error) {
      logger.error('Error loading medical knowledge:', error);
    }
  }

  async loadDrugInteractions() {
    // Implementation for loading drug interactions
    // This would typically load from FDA databases or medical APIs
    logger.info('Drug interactions loaded');
  }

  async loadLabReferenceRanges() {
    // Implementation for loading lab reference ranges
    logger.info('Lab reference ranges loaded');
  }

  async loadClinicalGuidelines() {
    // Implementation for loading clinical guidelines
    logger.info('Clinical guidelines loaded');
  }
}

module.exports = MultimodalRAGSystem;
