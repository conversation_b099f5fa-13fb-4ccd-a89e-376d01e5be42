import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Settings, ToggleLeft, ToggleRight, Plus, Edit, Trash2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';

const AdminPaymentGateways = () => {
  const [gateways, setGateways] = useState([
    {
      id: 1,
      name: 'Stripe',
      type: 'Credit Card',
      status: 'active',
      fees: '2.9% + $0.30',
      monthlyVolume: '$45,230',
      enabled: true
    },
    {
      id: 2,
      name: 'PayPal',
      type: 'Digital Wallet',
      status: 'active',
      fees: '3.49% + $0.49',
      monthlyVolume: '$12,450',
      enabled: true
    },
    {
      id: 3,
      name: 'Square',
      type: 'Credit Card',
      status: 'inactive',
      fees: '2.6% + $0.10',
      monthlyVolume: '$0',
      enabled: false
    }
  ]);

  const toggleGateway = (id: number) => {
    setGateways(gateways.map(gateway => 
      gateway.id === id 
        ? { ...gateway, enabled: !gateway.enabled, status: gateway.enabled ? 'inactive' : 'active' }
        : gateway
    ));
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold text-foreground font-unica">PAYMENT GATEWAYS</h1>
          <p className="text-muted-foreground mt-2">Configure and manage payment processing systems</p>
        </div>
        <Button className="h-12">
          <Plus className="h-5 w-5 mr-2" />
          Add Gateway
        </Button>
      </div>

      {/* Gateway Overview */}
      <div className="grid md:grid-cols-4 gap-6">
        <Card className="p-6 neu-raised">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-muted-foreground">Total Volume</h3>
            <CreditCard className="h-5 w-5 text-primary" />
          </div>
          <p className="text-3xl font-bold text-primary">$57,680</p>
          <p className="text-sm text-emerald-400 mt-2">+12.5% this month</p>
        </Card>
        
        <Card className="p-6 neu-raised">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-muted-foreground">Active Gateways</h3>
            <ToggleRight className="h-5 w-5 text-emerald-500" />
          </div>
          <p className="text-3xl font-bold text-foreground">2</p>
          <p className="text-sm text-muted-foreground mt-2">of 3 configured</p>
        </Card>
        
        <Card className="p-6 neu-raised">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-muted-foreground">Avg. Processing Fee</h3>
            <Settings className="h-5 w-5 text-amber-500" />
          </div>
          <p className="text-3xl font-bold text-foreground">3.2%</p>
          <p className="text-sm text-muted-foreground mt-2">across all gateways</p>
        </Card>
        
        <Card className="p-6 neu-raised">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-muted-foreground">Success Rate</h3>
            <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
          </div>
          <p className="text-3xl font-bold text-foreground">98.7%</p>
          <p className="text-sm text-emerald-400 mt-2">+0.3% improvement</p>
        </Card>
      </div>

      {/* Gateway Configuration */}
      <Card className="p-8 neu-raised">
        <h2 className="text-2xl font-semibold mb-6">Gateway Configuration</h2>
        
        <div className="space-y-6">
          {gateways.map((gateway) => (
            <div key={gateway.id} className="p-6 border border-border rounded-lg neu-flat">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                    <CreditCard className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{gateway.name}</h3>
                    <p className="text-sm text-muted-foreground">{gateway.type}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Badge className={
                    gateway.status === 'active' 
                      ? 'bg-emerald-500/20 text-emerald-400' 
                      : 'bg-gray-500/20 text-gray-400'
                  }>
                    {gateway.status}
                  </Badge>
                  <Switch 
                    checked={gateway.enabled}
                    onCheckedChange={() => toggleGateway(gateway.id)}
                  />
                </div>
              </div>
              
              <div className="grid md:grid-cols-4 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">Processing Fees</h4>
                  <p className="font-semibold">{gateway.fees}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">Monthly Volume</h4>
                  <p className="font-semibold">{gateway.monthlyVolume}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">API Status</h4>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${gateway.enabled ? 'bg-emerald-500' : 'bg-gray-500'}`}></div>
                    <span className="text-sm">{gateway.enabled ? 'Connected' : 'Disconnected'}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    <Edit className="h-4 w-4 mr-1" />
                    Configure
                  </Button>
                  <Button variant="outline" size="sm" className="text-red-400 border-red-400/50">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              {gateway.enabled && (
                <div className="mt-6 pt-4 border-t border-border">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">API Key</label>
                      <Input 
                        type="password" 
                        placeholder="••••••••••••••••••••"
                        className="neu-inset"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Secret Key</label>
                      <Input 
                        type="password" 
                        placeholder="••••••••••••••••••••"
                        className="neu-inset"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* Gateway Settings */}
      <Card className="p-8 neu-raised">
        <h2 className="text-2xl font-semibold mb-6">Global Settings</h2>
        
        <div className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">Default Currency</label>
              <select className="w-full h-12 bg-background border border-border rounded-lg px-3 neu-inset">
                <option>USD - US Dollar</option>
                <option>EUR - Euro</option>
                <option>GBP - British Pound</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Minimum Payment Amount</label>
              <Input placeholder="$10.00" className="h-12 neu-inset" />
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border border-border rounded-lg neu-flat">
              <div>
                <h3 className="font-medium">Auto-retry Failed Payments</h3>
                <p className="text-sm text-muted-foreground">Automatically retry failed payments after 24 hours</p>
              </div>
              <Switch />
            </div>
            
            <div className="flex items-center justify-between p-4 border border-border rounded-lg neu-flat">
              <div>
                <h3 className="font-medium">Send Payment Confirmations</h3>
                <p className="text-sm text-muted-foreground">Email receipts to customers automatically</p>
              </div>
              <Switch defaultChecked />
            </div>
            
            <div className="flex items-center justify-between p-4 border border-border rounded-lg neu-flat">
              <div>
                <h3 className="font-medium">Fraud Detection</h3>
                <p className="text-sm text-muted-foreground">Enable advanced fraud protection</p>
              </div>
              <Switch defaultChecked />
            </div>
          </div>
          
          <div className="pt-6">
            <Button className="w-full h-12">Save Gateway Settings</Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AdminPaymentGateways;