import React from 'react';
import { cn } from '@/lib/utils';
import { Loader2, Brain, Activity } from 'lucide-react';

// Main Loading Spinner Component
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'medical' | 'ai' | 'minimal';
  className?: string;
  text?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  className,
  text
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const variants = {
    default: (
      <Loader2 className={cn('animate-spin text-primary', sizeClasses[size], className)} />
    ),
    medical: (
      <div className={cn('relative', sizeClasses[size])}>
        <Activity className={cn('animate-pulse text-green-500', sizeClasses[size])} />
        <div className="absolute inset-0 animate-spin">
          <div className="w-full h-full border-2 border-transparent border-t-green-500 rounded-full"></div>
        </div>
      </div>
    ),
    ai: (
      <div className={cn('relative', sizeClasses[size])}>
        <Brain className={cn('animate-pulse text-[#ADF802]', sizeClasses[size])} />
        <div className="absolute inset-0 animate-spin">
          <div className="w-full h-full border-2 border-transparent border-t-[#ADF802] rounded-full"></div>
        </div>
      </div>
    ),
    minimal: (
      <div className={cn('animate-spin rounded-full border-2 border-gray-300 border-t-primary', sizeClasses[size], className)}></div>
    )
  };

  return (
    <div className="flex flex-col items-center gap-2">
      {variants[variant]}
      {text && (
        <p className="text-sm text-muted-foreground animate-pulse">{text}</p>
      )}
    </div>
  );
};

// Page Preloader Component
interface PagePreloaderProps {
  isLoading: boolean;
  variant?: 'medical' | 'ai' | 'default';
}

export const PagePreloader: React.FC<PagePreloaderProps> = ({
  isLoading,
  variant = 'ai'
}) => {
  if (!isLoading) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          {variant === 'ai' && (
            <>
              <div className="w-16 h-16 border-4 border-[#ADF802]/20 rounded-full animate-spin">
                <div className="w-full h-full border-4 border-transparent border-t-[#ADF802] rounded-full animate-spin"></div>
              </div>
              <Brain className="absolute inset-0 m-auto w-8 h-8 text-[#ADF802] animate-pulse" />
            </>
          )}
          {variant === 'medical' && (
            <>
              <div className="w-16 h-16 border-4 border-green-500/20 rounded-full animate-spin">
                <div className="w-full h-full border-4 border-transparent border-t-green-500 rounded-full animate-spin"></div>
              </div>
              <Activity className="absolute inset-0 m-auto w-8 h-8 text-green-500 animate-pulse" />
            </>
          )}
          {variant === 'default' && (
            <Loader2 className="w-16 h-16 text-primary animate-spin" />
          )}
        </div>
        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold">Loading MEDORA</h3>
          <p className="text-sm text-muted-foreground">Initializing AI Medical Assistant...</p>
        </div>
      </div>
    </div>
  );
};

// Skeleton Loading Components
export const SkeletonCard = ({ className }: { className?: string }) => (
  <div className={cn('animate-pulse', className)}>
    <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-48 w-full"></div>
    <div className="mt-4 space-y-2">
      <div className="bg-gray-200 dark:bg-gray-700 h-4 rounded w-3/4"></div>
      <div className="bg-gray-200 dark:bg-gray-700 h-4 rounded w-1/2"></div>
    </div>
  </div>
);

export const SkeletonText = ({ lines = 3, className }: { lines?: number; className?: string }) => (
  <div className={cn('animate-pulse space-y-2', className)}>
    {Array.from({ length: lines }).map((_, i) => (
      <div
        key={i}
        className={cn(
          'bg-gray-200 dark:bg-gray-700 h-4 rounded',
          i === lines - 1 ? 'w-3/4' : 'w-full'
        )}
      ></div>
    ))}
  </div>
);

export const SkeletonAvatar = ({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };

  return (
    <div className={cn('animate-pulse bg-gray-200 dark:bg-gray-700 rounded-full', sizeClasses[size])}></div>
  );
};

// Loading Button Component
interface LoadingButtonProps {
  isLoading: boolean;
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'medical' | 'ai';
  [key: string]: any;
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  isLoading,
  children,
  className,
  variant = 'default',
  ...props
}) => {
  const getLoadingIcon = () => {
    switch (variant) {
      case 'medical':
        return <Activity className="w-4 h-4 mr-2 animate-pulse" />;
      case 'ai':
        return <img src="/lovable-uploads/logo-png.png" alt="MEDORA AI" className="w-4 h-4 mr-2 animate-pulse object-contain" />;
      default:
        return <Loader2 className="w-4 h-4 mr-2 animate-spin" />;
    }
  };

  return (
    <button
      className={cn(
        'inline-flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed',
        className
      )}
      disabled={isLoading}
      {...props}
    >
      {isLoading && getLoadingIcon()}
      {children}
    </button>
  );
};

// Loading Overlay Component
interface LoadingOverlayProps {
  isLoading: boolean;
  children: React.ReactNode;
  variant?: 'default' | 'medical' | 'ai';
  text?: string;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  children,
  variant = 'default',
  text = 'Loading...'
}) => {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-10">
          <LoadingSpinner variant={variant} text={text} size="lg" />
        </div>
      )}
    </div>
  );
};

// Pulse Animation Component
export const PulseLoader = ({ className }: { className?: string }) => (
  <div className={cn('flex space-x-1', className)}>
    {[0, 1, 2].map((i) => (
      <div
        key={i}
        className="w-2 h-2 bg-primary rounded-full animate-pulse"
        style={{ animationDelay: `${i * 0.2}s` }}
      ></div>
    ))}
  </div>
);

// Typing Indicator (for chat/AI responses)
export const TypingIndicator = ({ className }: { className?: string }) => (
  <div className={cn('flex items-center space-x-2 p-3 bg-muted rounded-lg', className)}>
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className="w-2 h-2 bg-primary rounded-full animate-bounce"
          style={{ animationDelay: `${i * 0.1}s` }}
        ></div>
      ))}
    </div>
    <span className="text-sm text-muted-foreground">AI is thinking...</span>
  </div>
);
