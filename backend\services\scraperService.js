const { chromium, firefox, webkit } = require('playwright');
const axios = require('axios');
const cheerio = require('cheerio');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');
const ScrapedData = require('../models/ScrapedData');

class ScraperService {
  constructor() {
    this.browser = null;
    this.context = null;
    this.isInitialized = false;
    this.rateLimits = new Map();
    this.userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    ];
  }

  // Initialize browser and context
  async initialize() {
    try {
      if (this.isInitialized) return;

      logger.info('Initializing Playwright scraper service...');
      
      const config = require('../config');
      this.browser = await chromium.launch({
        headless: config.server.isProduction,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
        ],
      });

      this.context = await this.browser.newContext({
        userAgent: this.getRandomUserAgent(),
        viewport: { width: 1920, height: 1080 },
        locale: 'en-US',
        timezoneId: 'America/New_York',
      });

      this.isInitialized = true;
      logger.info('Playwright scraper service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize scraper service:', error);
      throw new AppError('Failed to initialize web scraper', 500);
    }
  }

  // Clean up resources
  async cleanup() {
    try {
      if (this.context) {
        await this.context.close();
        this.context = null;
      }
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
      this.isInitialized = false;
      logger.info('Scraper service cleaned up successfully');
    } catch (error) {
      logger.error('Error during scraper cleanup:', error);
    }
  }

  // Get random user agent
  getRandomUserAgent() {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  // Rate limiting check
  async checkRateLimit(domain) {
    const now = Date.now();
    const limit = this.rateLimits.get(domain);
    
    if (limit && now - limit < 2000) { // 2 second delay between requests
      const waitTime = 2000 - (now - limit);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.rateLimits.set(domain, now);
  }

  // Scrape medical information from PubMed
  async scrapePubMed(query, limit = 10) {
    try {
      await this.initialize();
      await this.checkRateLimit('pubmed.ncbi.nlm.nih.gov');

      const page = await this.context.newPage();
      
      try {
        const searchUrl = `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(query)}&size=${limit}`;
        
        await page.goto(searchUrl, { waitUntil: 'networkidle' });
        await page.waitForSelector('.docsum-wrap', { timeout: 10000 });

        const articles = await page.evaluate(() => {
          const results = [];
          const articles = document.querySelectorAll('.docsum-wrap');
          
          articles.forEach((article, index) => {
            if (index >= 10) return; // Limit results
            
            const titleElement = article.querySelector('.docsum-title a');
            const authorsElement = article.querySelector('.docsum-authors');
            const journalElement = article.querySelector('.docsum-journal-citation');
            const abstractElement = article.querySelector('.docsum-snippet');
            const pmidElement = article.querySelector('[data-article-id]');
            
            if (titleElement) {
              results.push({
                title: titleElement.textContent.trim(),
                authors: authorsElement ? authorsElement.textContent.trim() : '',
                journal: journalElement ? journalElement.textContent.trim() : '',
                abstract: abstractElement ? abstractElement.textContent.trim() : '',
                pmid: pmidElement ? pmidElement.getAttribute('data-article-id') : '',
                url: titleElement.href,
                source: 'PubMed',
                scrapedAt: new Date().toISOString(),
              });
            }
          });
          
          return results;
        });

        logger.info(`Scraped ${articles.length} articles from PubMed for query: ${query}`);
        return articles;
      } finally {
        await page.close();
      }
    } catch (error) {
      logger.error('PubMed scraping error:', error);
      throw new AppError('Failed to scrape PubMed data', 500);
    }
  }

  // Scrape drug information from Drugs.com
  async scrapeDrugInfo(drugName) {
    try {
      await this.initialize();
      await this.checkRateLimit('www.drugs.com');

      const page = await this.context.newPage();
      
      try {
        const searchUrl = `https://www.drugs.com/search.php?searchterm=${encodeURIComponent(drugName)}`;
        
        await page.goto(searchUrl, { waitUntil: 'networkidle' });
        
        // Try to find the first drug result
        const firstResult = await page.$('.ddc-search-result h3 a');
        if (!firstResult) {
          throw new AppError('Drug not found', 404);
        }
        
        await firstResult.click();
        await page.waitForLoadState('networkidle');

        const drugInfo = await page.evaluate(() => {
          const getTextContent = (selector) => {
            const element = document.querySelector(selector);
            return element ? element.textContent.trim() : '';
          };
          
          const getSectionContent = (sectionTitle) => {
            const headings = Array.from(document.querySelectorAll('h2, h3'));
            const targetHeading = headings.find(h => 
              h.textContent.toLowerCase().includes(sectionTitle.toLowerCase())
            );
            
            if (!targetHeading) return '';
            
            let content = '';
            let nextElement = targetHeading.nextElementSibling;
            
            while (nextElement && !['H1', 'H2', 'H3'].includes(nextElement.tagName)) {
              content += nextElement.textContent + ' ';
              nextElement = nextElement.nextElementSibling;
            }
            
            return content.trim();
          };
          
          return {
            name: getTextContent('h1'),
            genericName: getTextContent('.ddc-generic-name'),
            description: getSectionContent('what is'),
            uses: getSectionContent('uses'),
            sideEffects: getSectionContent('side effects'),
            warnings: getSectionContent('warnings'),
            dosage: getSectionContent('dosage'),
            interactions: getSectionContent('interactions'),
            source: 'Drugs.com',
            scrapedAt: new Date().toISOString(),
          };
        });

        logger.info(`Scraped drug information for: ${drugName}`);
        return drugInfo;
      } finally {
        await page.close();
      }
    } catch (error) {
      logger.error('Drug info scraping error:', error);
      throw new AppError('Failed to scrape drug information', 500);
    }
  }

  // Scrape medical news from reliable sources
  async scrapeMedicalNews(topic, limit = 5) {
    try {
      await this.initialize();
      
      const sources = [
        {
          name: 'Medical News Today',
          baseUrl: 'https://www.medicalnewstoday.com',
          searchPath: '/search',
        },
        {
          name: 'WebMD News',
          baseUrl: 'https://www.webmd.com',
          searchPath: '/search/search_results/default.aspx',
        },
      ];

      const allNews = [];
      
      for (const source of sources) {
        try {
          await this.checkRateLimit(new URL(source.baseUrl).hostname);
          const news = await this.scrapeNewsFromSource(source, topic, Math.ceil(limit / sources.length));
          allNews.push(...news);
        } catch (error) {
          logger.warn(`Failed to scrape from ${source.name}:`, error.message);
        }
      }

      // Sort by date and limit results
      const sortedNews = allNews
        .sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))
        .slice(0, limit);

      logger.info(`Scraped ${sortedNews.length} medical news articles for topic: ${topic}`);
      return sortedNews;
    } catch (error) {
      logger.error('Medical news scraping error:', error);
      throw new AppError('Failed to scrape medical news', 500);
    }
  }

  // Helper method to scrape news from a specific source
  async scrapeNewsFromSource(source, topic, limit) {
    const page = await this.context.newPage();
    
    try {
      const searchUrl = `${source.baseUrl}${source.searchPath}?q=${encodeURIComponent(topic)}`;
      
      await page.goto(searchUrl, { waitUntil: 'networkidle' });
      
      // Wait for search results
      await page.waitForTimeout(2000);

      const articles = await page.evaluate((sourceName, articleLimit) => {
        const results = [];
        let articleElements = [];
        
        // Different selectors for different sources
        if (sourceName === 'Medical News Today') {
          articleElements = document.querySelectorAll('.css-1c7z4va, .search-result');
        } else if (sourceName === 'WebMD News') {
          articleElements = document.querySelectorAll('.search-result, .article-card');
        } else {
          // Generic fallback
          articleElements = document.querySelectorAll('article, .article, .news-item');
        }
        
        Array.from(articleElements).slice(0, articleLimit).forEach(article => {
          const titleElement = article.querySelector('h1, h2, h3, .title, .headline');
          const linkElement = article.querySelector('a');
          const summaryElement = article.querySelector('.summary, .excerpt, .description, p');
          const dateElement = article.querySelector('.date, .published, time');
          
          if (titleElement && linkElement) {
            results.push({
              title: titleElement.textContent.trim(),
              summary: summaryElement ? summaryElement.textContent.trim().substring(0, 200) : '',
              url: linkElement.href.startsWith('http') ? linkElement.href : `${window.location.origin}${linkElement.href}`,
              publishedAt: dateElement ? dateElement.textContent.trim() : new Date().toISOString(),
              source: sourceName,
              scrapedAt: new Date().toISOString(),
            });
          }
        });
        
        return results;
      }, source.name, limit);

      return articles;
    } finally {
      await page.close();
    }
  }

  // Scrape disease information from reliable medical sources
  async scrapeDiseasInfo(diseaseName) {
    try {
      await this.initialize();
      await this.checkRateLimit('www.mayoclinic.org');

      const page = await this.context.newPage();
      
      try {
        const searchUrl = `https://www.mayoclinic.org/search/search-results?q=${encodeURIComponent(diseaseName)}`;
        
        await page.goto(searchUrl, { waitUntil: 'networkidle' });
        
        // Click on the first relevant result
        const firstResult = await page.$('.search-result h3 a');
        if (!firstResult) {
          throw new AppError('Disease information not found', 404);
        }
        
        await firstResult.click();
        await page.waitForLoadState('networkidle');

        const diseaseInfo = await page.evaluate(() => {
          const getSectionText = (headingText) => {
            const headings = Array.from(document.querySelectorAll('h2, h3'));
            const targetHeading = headings.find(h => 
              h.textContent.toLowerCase().includes(headingText.toLowerCase())
            );
            
            if (!targetHeading) return '';
            
            let content = '';
            let nextElement = targetHeading.nextElementSibling;
            
            while (nextElement && !['H1', 'H2', 'H3'].includes(nextElement.tagName)) {
              if (nextElement.tagName === 'P' || nextElement.tagName === 'UL') {
                content += nextElement.textContent + ' ';
              }
              nextElement = nextElement.nextElementSibling;
            }
            
            return content.trim();
          };
          
          return {
            name: document.querySelector('h1')?.textContent.trim() || '',
            overview: getSectionText('overview') || getSectionText('definition'),
            symptoms: getSectionText('symptoms'),
            causes: getSectionText('causes'),
            riskFactors: getSectionText('risk factors'),
            complications: getSectionText('complications'),
            prevention: getSectionText('prevention'),
            diagnosis: getSectionText('diagnosis'),
            treatment: getSectionText('treatment'),
            source: 'Mayo Clinic',
            url: window.location.href,
            scrapedAt: new Date().toISOString(),
          };
        });

        logger.info(`Scraped disease information for: ${diseaseName}`);
        return diseaseInfo;
      } finally {
        await page.close();
      }
    } catch (error) {
      logger.error('Disease info scraping error:', error);
      throw new AppError('Failed to scrape disease information', 500);
    }
  }

  // Scrape clinical trial information
  async scrapeClinicalTrials(condition, limit = 5) {
    try {
      await this.initialize();
      await this.checkRateLimit('clinicaltrials.gov');

      const page = await this.context.newPage();
      
      try {
        const searchUrl = `https://clinicaltrials.gov/search?cond=${encodeURIComponent(condition)}&limit=${limit}`;
        
        await page.goto(searchUrl, { waitUntil: 'networkidle' });
        await page.waitForSelector('.ct-search-results', { timeout: 10000 });

        const trials = await page.evaluate(() => {
          const results = [];
          const trialElements = document.querySelectorAll('.ct-search-result');
          
          trialElements.forEach(trial => {
            const titleElement = trial.querySelector('.ct-search-result-title a');
            const statusElement = trial.querySelector('.ct-search-result-status');
            const conditionElement = trial.querySelector('.ct-search-result-condition');
            const locationElement = trial.querySelector('.ct-search-result-location');
            const sponsorElement = trial.querySelector('.ct-search-result-sponsor');
            
            if (titleElement) {
              results.push({
                title: titleElement.textContent.trim(),
                status: statusElement ? statusElement.textContent.trim() : '',
                condition: conditionElement ? conditionElement.textContent.trim() : '',
                location: locationElement ? locationElement.textContent.trim() : '',
                sponsor: sponsorElement ? sponsorElement.textContent.trim() : '',
                url: titleElement.href,
                source: 'ClinicalTrials.gov',
                scrapedAt: new Date().toISOString(),
              });
            }
          });
          
          return results;
        });

        logger.info(`Scraped ${trials.length} clinical trials for condition: ${condition}`);
        return trials;
      } finally {
        await page.close();
      }
    } catch (error) {
      logger.error('Clinical trials scraping error:', error);
      throw new AppError('Failed to scrape clinical trials data', 500);
    }
  }

  // Scrape medical guidelines and protocols
  async scrapeMedicalGuidelines(topic) {
    try {
      await this.initialize();
      
      // Use axios for simpler HTTP requests for guidelines
      const response = await axios.get(
        `https://www.guidelines.gov/search?q=${encodeURIComponent(topic)}`,
        {
          headers: {
            'User-Agent': this.getRandomUserAgent(),
          },
          timeout: 10000,
        }
      );

      const $ = cheerio.load(response.data);
      const guidelines = [];

      $('.search-result').each((index, element) => {
        if (index >= 5) return false; // Limit to 5 results
        
        const title = $(element).find('h3 a').text().trim();
        const summary = $(element).find('.summary').text().trim();
        const url = $(element).find('h3 a').attr('href');
        const organization = $(element).find('.organization').text().trim();
        
        if (title) {
          guidelines.push({
            title,
            summary,
            url: url?.startsWith('http') ? url : `https://www.guidelines.gov${url}`,
            organization,
            source: 'Guidelines.gov',
            scrapedAt: new Date().toISOString(),
          });
        }
      });

      logger.info(`Scraped ${guidelines.length} medical guidelines for topic: ${topic}`);
      return guidelines;
    } catch (error) {
      logger.error('Medical guidelines scraping error:', error);
      // Return empty array instead of throwing error for guidelines
      return [];
    }
  }

  // Comprehensive medical data scraping
  async scrapeComprehensiveMedicalData(query) {
    try {
      const results = await Promise.allSettled([
        this.scrapePubMed(query, 5),
        this.scrapeMedicalNews(query, 3),
        this.scrapeDiseasInfo(query),
        this.scrapeClinicalTrials(query, 3),
        this.scrapeMedicalGuidelines(query),
      ]);

      const [pubmedResult, newsResult, diseaseResult, trialsResult, guidelinesResult] = results;

      return {
        query,
        scrapedAt: new Date().toISOString(),
        data: {
          research: pubmedResult.status === 'fulfilled' ? pubmedResult.value : [],
          news: newsResult.status === 'fulfilled' ? newsResult.value : [],
          diseaseInfo: diseaseResult.status === 'fulfilled' ? diseaseResult.value : null,
          clinicalTrials: trialsResult.status === 'fulfilled' ? trialsResult.value : [],
          guidelines: guidelinesResult.status === 'fulfilled' ? guidelinesResult.value : [],
        },
        errors: results
          .filter(result => result.status === 'rejected')
          .map(result => result.reason.message),
      };
    } catch (error) {
      logger.error('Comprehensive scraping error:', error);
      throw new AppError('Failed to scrape comprehensive medical data', 500);
    }
  }

  // Store scraped data in database
  async storeScrapedData(query, dataType, source, data, userId, processingTime = 0) {
    try {
      const scrapedData = new ScrapedData({
        query,
        dataType,
        source,
        scrapedBy: userId,
        processingTime,
        status: 'completed',
      });

      // Store data based on type
      switch (dataType) {
        case 'pubmed':
          scrapedData.pubmedArticles = data;
          break;
        case 'medical_news':
          scrapedData.medicalNews = data;
          break;
        case 'disease_info':
          scrapedData.diseaseInfo = data;
          break;
        case 'clinical_trials':
          scrapedData.clinicalTrials = data;
          break;
        case 'guidelines':
          scrapedData.guidelines = data;
          break;
        case 'comprehensive':
          scrapedData.comprehensiveData = data;
          break;
        default:
          throw new Error(`Unknown data type: ${dataType}`);
      }

      // Calculate quality and relevance scores
      scrapedData.qualityScore = this.calculateQualityScore(data, dataType);
      scrapedData.relevanceScore = this.calculateRelevanceScore(query, data, dataType);

      await scrapedData.save();
      logger.info(`Scraped data stored in database`, {
        query,
        dataType,
        source,
        userId,
        dataId: scrapedData._id,
      });

      return scrapedData;
    } catch (error) {
      logger.error('Error storing scraped data:', error);
      throw error;
    }
  }

  // Calculate quality score for scraped data
  calculateQualityScore(data, dataType) {
    let score = 0.5; // Base score

    if (!data || (Array.isArray(data) && data.length === 0)) {
      return 0.1;
    }

    switch (dataType) {
      case 'pubmed':
        if (Array.isArray(data)) {
          const avgLength = data.reduce((sum, item) => sum + (item.abstract?.length || 0), 0) / data.length;
          score = Math.min(1, 0.3 + (avgLength / 1000) * 0.7);
        }
        break;
      case 'disease_info':
        if (data.overview && data.symptoms && data.treatment) {
          score = 0.9;
        } else if (data.overview || data.symptoms) {
          score = 0.7;
        }
        break;
      case 'medical_news':
        if (Array.isArray(data)) {
          const hasContent = data.filter(item => item.content && item.content.length > 100).length;
          score = 0.3 + (hasContent / data.length) * 0.7;
        }
        break;
      default:
        score = 0.6;
    }

    return Math.max(0.1, Math.min(1, score));
  }

  // Calculate relevance score based on query match
  calculateRelevanceScore(query, data, dataType) {
    const queryTerms = query.toLowerCase().split(/\s+/);
    let relevanceSum = 0;
    let itemCount = 0;

    const calculateTextRelevance = (text) => {
      if (!text) return 0;
      const lowerText = text.toLowerCase();
      const matches = queryTerms.filter(term => lowerText.includes(term)).length;
      return matches / queryTerms.length;
    };

    if (Array.isArray(data)) {
      data.forEach(item => {
        let itemRelevance = 0;
        if (item.title) itemRelevance += calculateTextRelevance(item.title) * 0.4;
        if (item.abstract || item.summary) itemRelevance += calculateTextRelevance(item.abstract || item.summary) * 0.6;
        relevanceSum += itemRelevance;
        itemCount++;
      });
    } else if (data && typeof data === 'object') {
      let itemRelevance = 0;
      if (data.name || data.title) itemRelevance += calculateTextRelevance(data.name || data.title) * 0.3;
      if (data.overview || data.description) itemRelevance += calculateTextRelevance(data.overview || data.description) * 0.7;
      relevanceSum = itemRelevance;
      itemCount = 1;
    }

    return itemCount > 0 ? Math.min(1, relevanceSum / itemCount) : 0.5;
  }

  // Enhanced comprehensive scraping with caching
  async scrapeComprehensiveMedicalDataWithStorage(query, userId) {
    const startTime = Date.now();

    try {
      // Check for existing fresh data
      const existingData = await ScrapedData.findFreshData(query, 'comprehensive', 7);
      if (existingData) {
        logger.info(`Using cached comprehensive data for query: ${query}`);
        await existingData.incrementAccess();
        return existingData;
      }

      // Scrape new data
      const scrapingResult = await this.scrapeComprehensiveMedicalData(query);
      const processingTime = Date.now() - startTime;

      // Store in database
      const storedData = await this.storeScrapedData(
        query,
        'comprehensive',
        'multiple_sources',
        scrapingResult.data,
        userId,
        processingTime
      );

      logger.info(`Comprehensive medical data scraped and stored for query: ${query}`, {
        researchCount: scrapingResult.data.research.length,
        newsCount: scrapingResult.data.news.length,
        hasDisease: !!scrapingResult.data.diseaseInfo,
        trialsCount: scrapingResult.data.clinicalTrials.length,
        guidelinesCount: scrapingResult.data.guidelines.length,
        errorCount: scrapingResult.errors.length,
        processingTime,
        dataId: storedData._id,
      });

      return storedData;
    } catch (error) {
      logger.error('Enhanced comprehensive scraping error:', error);
      throw new AppError('Failed to scrape and store comprehensive medical data', 500);
    }
  }
}

// Export singleton instance
module.exports = new ScraperService();

// Graceful shutdown
process.on('SIGINT', async () => {
  const scraperService = require('./scraperService');
  await scraperService.cleanup();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  const scraperService = require('./scraperService');
  await scraperService.cleanup();
  process.exit(0);
});