# MEDORA AI Backend API Documentation

## Base URL
```
Production: https://medora-backend-production.up.railway.app
Development: http://localhost:5000
```

## Authentication

All protected endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Response Format

### Success Response
```json
{
  "success": true,
  "data": {},
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

## Authentication Endpoints

### Register User
```http
POST /api/auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "role": "patient"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "firstName": "<PERSON>",
      "lastName": "Doe",
      "role": "patient"
    },
    "token": "jwt_token"
  }
}
```

### Login User
```http
POST /api/auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Logout User
```http
POST /api/auth/logout
```

**Headers:** `Authorization: Bearer <token>`

## AI & Medical Endpoints

### Enhanced AI Consultation
```http
POST /api/enhanced-ai/consultation
```

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "patientData": {
    "symptoms": ["headache", "fever", "fatigue"],
    "medicalHistory": ["hypertension"],
    "currentMedications": ["lisinopril"],
    "age": 45,
    "gender": "male",
    "vitalSigns": {
      "bloodPressure": "140/90",
      "heartRate": 80,
      "temperature": 101.2
    }
  },
  "consultationType": "comprehensive"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "consultation": {
      "id": "consultation_id",
      "diagnosis": {
        "primary": "Viral infection",
        "differential": ["Flu", "Common cold", "COVID-19"],
        "confidence": 0.85
      },
      "recommendations": [
        "Rest and hydration",
        "Monitor temperature",
        "Consider COVID-19 test"
      ],
      "followUp": "3-5 days",
      "urgency": "low"
    }
  }
}
```

### Multi-Agent Consultation
```http
POST /api/enhanced-ai/multi-agent-consultation
```

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "patientData": {
    "symptoms": ["chest pain", "shortness of breath"],
    "age": 55,
    "gender": "male",
    "medicalHistory": ["diabetes", "smoking"]
  },
  "consultationType": "emergency"
}
```

### AI Chat
```http
POST /api/ai/chat
```

**Request Body:**
```json
{
  "message": "What are the symptoms of diabetes?",
  "context": "general_inquiry"
}
```

### Medical Data Scraping
```http
POST /api/scraping/comprehensive
```

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "query": "diabetes treatment guidelines",
  "sources": ["pubmed", "who", "fda"],
  "limit": 10
}
```

## Patient Management

### List Patients
```http
GET /api/patients
```

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `search` (optional): Search term

### Create Patient
```http
POST /api/patients
```

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "dateOfBirth": "1980-05-15",
  "gender": "female",
  "phone": "+**********",
  "address": {
    "street": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zipCode": "10001",
    "country": "USA"
  },
  "emergencyContact": {
    "name": "John Smith",
    "relationship": "spouse",
    "phone": "+**********"
  }
}
```

### Get Patient Details
```http
GET /api/patients/:id
```

**Headers:** `Authorization: Bearer <token>`

### Update Patient
```http
PUT /api/patients/:id
```

**Headers:** `Authorization: Bearer <token>`

### Delete Patient
```http
DELETE /api/patients/:id
```

**Headers:** `Authorization: Bearer <token>`

## Payment Endpoints

### Initialize Payment
```http
POST /api/payments/initialize
```

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "amount": 5000,
  "currency": "NGN",
  "email": "<EMAIL>",
  "plan": "professional",
  "callback_url": "https://yoursite.com/callback"
}
```

### Verify Payment
```http
POST /api/payments/verify
```

**Request Body:**
```json
{
  "reference": "payment_reference"
}
```

### Payment History
```http
GET /api/payments/history
```

**Headers:** `Authorization: Bearer <token>`

## ML Analysis

### Analyze Data
```http
POST /api/ml/analyze
```

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "data": {
    "symptoms": ["fever", "cough", "fatigue"],
    "vitalSigns": {
      "temperature": 101.5,
      "heartRate": 95,
      "bloodPressure": "120/80"
    }
  },
  "analysisType": "symptom_analysis"
}
```

## System Endpoints

### Health Check
```http
GET /api/health
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "uptime": 3600,
    "version": "1.0.0"
  }
}
```

### System Settings
```http
GET /api/settings
```

**Headers:** `Authorization: Bearer <token>` (Admin only)

## Error Codes

| Code | Description |
|------|-------------|
| `AUTH_REQUIRED` | Authentication required |
| `INVALID_TOKEN` | Invalid or expired token |
| `INSUFFICIENT_PERMISSIONS` | User lacks required permissions |
| `VALIDATION_ERROR` | Request validation failed |
| `RESOURCE_NOT_FOUND` | Requested resource not found |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `INTERNAL_ERROR` | Internal server error |

## Rate Limiting

- **General API**: 100 requests per hour
- **AI Endpoints**: 50 requests per hour
- **Authentication**: 10 requests per minute

## Webhooks

### Payment Webhook
```http
POST /api/webhooks/payment
```

Receives payment notifications from payment providers.

### System Webhook
```http
POST /api/webhooks/system
```

Receives system notifications and alerts.

## SDK Examples

### JavaScript/Node.js
```javascript
const axios = require('axios');

const api = axios.create({
  baseURL: 'https://medora-backend-production.up.railway.app',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

// AI Consultation
const consultation = await api.post('/api/enhanced-ai/consultation', {
  patientData: {
    symptoms: ['headache', 'fever'],
    age: 30,
    gender: 'female'
  }
});
```

### Python
```python
import requests

headers = {
    'Authorization': f'Bearer {token}',
    'Content-Type': 'application/json'
}

response = requests.post(
    'https://medora-backend-production.up.railway.app/api/enhanced-ai/consultation',
    headers=headers,
    json={
        'patientData': {
            'symptoms': ['headache', 'fever'],
            'age': 30,
            'gender': 'female'
        }
    }
)
```
