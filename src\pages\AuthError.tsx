import React from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { <PERSON><PERSON>ircle, ArrowLeft, RefreshCw } from 'lucide-react';

const AuthError: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  const errorMessage = searchParams.get('message') || 'Authentication failed';

  const handleRetry = () => {
    navigate('/login');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <Card className="w-full max-w-md card-gradient neu-raised">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <img 
              src="/lovable-uploads/logo-png.png" 
              alt="MEDORA Logo" 
              className="h-10 w-10 object-contain"
            />
            <span className="text-2xl font-bold font-unica" style={{color: '#9ACD32'}}>
              MEDORA
            </span>
          </div>
        </CardHeader>
        
        <CardContent className="flex flex-col items-center space-y-4">
          <XCircle className="h-16 w-16 text-red-500" />
          <h2 className="text-xl font-semibold text-red-600">
            Authentication Failed
          </h2>
          <p className="text-center text-muted-foreground">
            {errorMessage}
          </p>
          <div className="text-sm text-muted-foreground text-center">
            <p>This could happen if:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>You denied permission to the application</li>
              <li>There was a network connection issue</li>
              <li>The authentication service is temporarily unavailable</li>
            </ul>
          </div>
        </CardContent>
        
        <CardFooter className="flex flex-col space-y-3">
          <Button 
            onClick={handleRetry}
            className="w-full btn-3d"
            style={{backgroundColor: '#9ACD32', color: '#000000'}}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
          <Button 
            onClick={handleGoBack}
            variant="outline"
            className="w-full"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default AuthError;
