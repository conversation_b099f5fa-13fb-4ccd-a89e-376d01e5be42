import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CalendarDateRangePicker } from '@/components/ui/date-range-picker';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell,
  AreaChart,
  Area
} from 'recharts';
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  CreditCard, 
  ArrowUpRight, 
  ArrowDownRight,
  Download,
  Calendar,
  Target,
  Activity
} from 'lucide-react';

const AnalyticsReporting = () => {
  const [dateRange, setDateRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  // Mock data
  const kpiData = {
    totalRevenue: { value: 142500, change: 12.5, trend: 'up' },
    activeUsers: { value: 1247, change: 8.2, trend: 'up' },
    conversionRate: { value: 3.4, change: -2.1, trend: 'down' },
    avgRevenuePerUser: { value: 114.32, change: 15.7, trend: 'up' }
  };

  const revenueData = [
    { date: '2024-01-01', revenue: 12000, users: 890, conversions: 45 },
    { date: '2024-01-02', revenue: 15000, users: 920, conversions: 52 },
    { date: '2024-01-03', revenue: 11000, users: 880, conversions: 38 },
    { date: '2024-01-04', revenue: 18000, users: 1100, conversions: 67 },
    { date: '2024-01-05', revenue: 16000, users: 950, conversions: 55 },
    { date: '2024-01-06', revenue: 19000, users: 1200, conversions: 72 },
    { date: '2024-01-07', revenue: 22000, users: 1300, conversions: 84 }
  ];

  const userActivityData = [
    { hour: '00:00', users: 45 },
    { hour: '02:00', users: 32 },
    { hour: '04:00', users: 28 },
    { hour: '06:00', users: 54 },
    { hour: '08:00', users: 89 },
    { hour: '10:00', users: 156 },
    { hour: '12:00', users: 234 },
    { hour: '14:00', users: 198 },
    { hour: '16:00', users: 176 },
    { hour: '18:00', users: 145 },
    { hour: '20:00', users: 98 },
    { hour: '22:00', users: 67 }
  ];

  const subscriptionBreakdown = [
    { name: 'Starter', value: 245, color: '#8884d8', revenue: 0 },
    { name: 'Professional', value: 456, color: '#82ca9d', revenue: 45144 },
    { name: 'Enterprise', value: 190, color: '#ffc658', revenue: 56810 }
  ];

  const topCountries = [
    { country: 'United States', users: 456, revenue: 45670 },
    { country: 'United Kingdom', users: 234, revenue: 23450 },
    { country: 'Canada', users: 178, revenue: 17800 },
    { country: 'Australia', users: 123, revenue: 12300 },
    { country: 'Germany', users: 98, revenue: 9800 }
  ];

  const churnAnalysis = [
    { month: 'Jan', churn: 2.1, newSignups: 145, netGrowth: 12.3 },
    { month: 'Feb', churn: 1.8, newSignups: 167, netGrowth: 15.2 },
    { month: 'Mar', churn: 2.4, newSignups: 123, netGrowth: 8.7 },
    { month: 'Apr', churn: 1.9, newSignups: 189, netGrowth: 18.1 },
    { month: 'May', churn: 2.2, newSignups: 156, netGrowth: 11.4 },
    { month: 'Jun', churn: 1.6, newSignups: 198, netGrowth: 21.3 }
  ];

  const KPICard = ({ title, value, change, trend, icon: Icon, format = 'number' }: unknown) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {format === 'currency' && '$'}
          {format === 'percentage' && value.toFixed(1) + '%'}
          {format === 'number' && value.toLocaleString()}
          {format === 'currency' && value.toLocaleString()}
        </div>
        <div className="flex items-center space-x-1 text-xs">
          {trend === 'up' ? (
            <ArrowUpRight className="h-3 w-3 text-green-500" />
          ) : (
            <ArrowDownRight className="h-3 w-3 text-red-500" />
          )}
          <span className={trend === 'up' ? 'text-green-500' : 'text-red-500'}>
            {Math.abs(change)}%
          </span>
          <span className="text-muted-foreground">from last period</span>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Analytics & Reporting</h1>
            <p className="text-muted-foreground">Track performance and gain insights into your business</p>
          </div>
          <div className="flex items-center gap-4">
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1d">Last 24 hours</SelectItem>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 3 months</SelectItem>
                <SelectItem value="365d">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <KPICard
            title="Total Revenue"
            value={kpiData.totalRevenue.value}
            change={kpiData.totalRevenue.change}
            trend={kpiData.totalRevenue.trend}
            icon={DollarSign}
            format="currency"
          />
          <KPICard
            title="Active Users"
            value={kpiData.activeUsers.value}
            change={kpiData.activeUsers.change}
            trend={kpiData.activeUsers.trend}
            icon={Users}
            format="number"
          />
          <KPICard
            title="Conversion Rate"
            value={kpiData.conversionRate.value}
            change={kpiData.conversionRate.change}
            trend={kpiData.conversionRate.trend}
            icon={Target}
            format="percentage"
          />
          <KPICard
            title="Avg Revenue Per User"
            value={kpiData.avgRevenuePerUser.value}
            change={kpiData.avgRevenuePerUser.change}
            trend={kpiData.avgRevenuePerUser.trend}
            icon={CreditCard}
            format="currency"
          />
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="revenue">Revenue</TabsTrigger>
            <TabsTrigger value="users">User Analytics</TabsTrigger>
            <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
            <TabsTrigger value="geography">Geography</TabsTrigger>
            <TabsTrigger value="retention">Retention</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Trend</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Area 
                        type="monotone" 
                        dataKey="revenue" 
                        stroke="hsl(var(--primary))" 
                        fill="hsl(var(--primary))"
                        fillOpacity={0.3}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>User Activity (24h)</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={userActivityData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="users" fill="hsl(var(--primary))" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="revenue" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="revenue" 
                      stroke="hsl(var(--primary))" 
                      strokeWidth={3}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>User Growth</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Line 
                        type="monotone" 
                        dataKey="users" 
                        stroke="hsl(var(--emerald))" 
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Conversion Tracking</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="conversions" fill="hsl(var(--orange))" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="subscriptions" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Subscription Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={subscriptionBreakdown}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label
                      >
                        {subscriptionBreakdown.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Revenue by Plan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {subscriptionBreakdown.map((plan) => (
                      <div key={plan.name} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: plan.color }}
                          />
                          <span className="font-medium">{plan.name}</span>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">${plan.revenue.toLocaleString()}</p>
                          <p className="text-sm text-muted-foreground">{plan.value} users</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="geography" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Countries by Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topCountries.map((country, index) => (
                    <div key={country.country} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline">#{index + 1}</Badge>
                        <span className="font-medium">{country.country}</span>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">${country.revenue.toLocaleString()}</p>
                        <p className="text-sm text-muted-foreground">{country.users} users</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="retention" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Churn Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={churnAnalysis}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="churn" 
                      stroke="hsl(var(--destructive))" 
                      strokeWidth={2}
                      name="Churn Rate (%)"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="netGrowth" 
                      stroke="hsl(var(--emerald))" 
                      strokeWidth={2}
                      name="Net Growth (%)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AnalyticsReporting;