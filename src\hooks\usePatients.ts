import { useState, useEffect, useCallback } from 'react';
import { patientService, Patient, CreatePatientData, UpdatePatientData, PatientList } from '../services/patientService';

export const usePatients = (page: number = 1, limit: number = 10, searchTerm?: string) => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const fetchPatients = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await patientService.getAllPatients(page, limit, searchTerm);
      
      if (response.success && response.data) {
        setPatients(response.data.patients);
        setTotal(response.data.total);
        setTotalPages(Math.ceil(response.data.total / limit));
      } else {
        setError(response.error || 'Failed to fetch patients');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch patients');
    } finally {
      setLoading(false);
    }
  }, [page, limit, searchTerm]);

  useEffect(() => {
    fetchPatients();
  }, [fetchPatients]);

  const createPatient = async (patientData: CreatePatientData): Promise<Patient | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await patientService.createPatient(patientData);
      
      if (response.success && response.data) {
        await fetchPatients(); // Refresh the list
        return response.data;
      } else {
        setError(response.error || 'Failed to create patient');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create patient');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updatePatient = async (patientId: string, patientData: UpdatePatientData): Promise<Patient | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await patientService.updatePatient(patientId, patientData);
      
      if (response.success && response.data) {
        await fetchPatients(); // Refresh the list
        return response.data;
      } else {
        setError(response.error || 'Failed to update patient');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update patient');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const deletePatient = async (patientId: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await patientService.deletePatient(patientId);
      
      if (response.success) {
        await fetchPatients(); // Refresh the list
        return true;
      } else {
        setError(response.error || 'Failed to delete patient');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete patient');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    patients,
    loading,
    error,
    total,
    totalPages,
    createPatient,
    updatePatient,
    deletePatient,
    refetch: fetchPatients
  };
};

export const usePatient = (patientId: string | null) => {
  const [patient, setPatient] = useState<Patient | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPatient = useCallback(async () => {
    if (!patientId) {
      setPatient(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await patientService.getPatient(patientId);
      
      if (response.success && response.data) {
        setPatient(response.data);
      } else {
        setError(response.error || 'Failed to fetch patient');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch patient');
    } finally {
      setLoading(false);
    }
  }, [patientId]);

  useEffect(() => {
    fetchPatient();
  }, [fetchPatient]);

  const addVitalSigns = async (vitalSigns: unknown): Promise<boolean> => {
    if (!patientId) return false;

    try {
      setLoading(true);
      setError(null);
      
      const response = await patientService.addVitalSigns(patientId, vitalSigns);
      
      if (response.success) {
        await fetchPatient(); // Refresh patient data
        return true;
      } else {
        setError(response.error || 'Failed to add vital signs');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add vital signs');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const addMedication = async (medication: unknown): Promise<boolean> => {
    if (!patientId) return false;

    try {
      setLoading(true);
      setError(null);
      
      const response = await patientService.addMedication(patientId, medication);
      
      if (response.success) {
        await fetchPatient(); // Refresh patient data
        return true;
      } else {
        setError(response.error || 'Failed to add medication');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add medication');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const addLabResult = async (labResult: unknown): Promise<boolean> => {
    if (!patientId) return false;

    try {
      setLoading(true);
      setError(null);
      
      const response = await patientService.addLabResult(patientId, labResult);
      
      if (response.success) {
        await fetchPatient(); // Refresh patient data
        return true;
      } else {
        setError(response.error || 'Failed to add lab result');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add lab result');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    patient,
    loading,
    error,
    addVitalSigns,
    addMedication,
    addLabResult,
    refetch: fetchPatient
  };
};

export const usePatientStatistics = () => {
  const [statistics, setStatistics] = useState<unknown>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStatistics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await patientService.getPatientStatistics();
      
      if (response.success && response.data) {
        setStatistics(response.data);
      } else {
        setError(response.error || 'Failed to fetch statistics');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch statistics');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  return {
    statistics,
    loading,
    error,
    refetch: fetchStatistics
  };
};