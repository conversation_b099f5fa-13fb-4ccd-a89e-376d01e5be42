import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: string;
  author?: string;
  siteName?: string;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title = "MEDORA - AI-Powered Medical Assistant Platform",
  description = "Revolutionary AI-powered medical assistant platform providing intelligent healthcare solutions, patient management, doctor consultations, and medical diagnosis assistance.",
  keywords = [
    "AI medical assistant",
    "healthcare platform",
    "medical diagnosis",
    "telemedicine",
    "patient management",
    "doctor consultation",
    "medical AI",
    "healthcare technology",
    "digital health",
    "medical records",
    "health monitoring",
    "medical analytics",
    "healthcare automation",
    "clinical decision support",
    "medical chatbot",
    "health tech",
    "medical software",
    "healthcare solutions",
    "patient care",
    "medical innovation"
  ],
  image = "/lovable-uploads/logo-png.png",
  url = "https://medoraai.me",
  type = "website",
  author = "MEDORA Team",
  siteName = "MEDORA"
}) => {
  const fullTitle = title.includes('MEDORA') ? title : `${title} | MEDORA`;
  const fullUrl = url.startsWith('http') ? url : `https://medoraai.me${url}`;
  const fullImage = image.startsWith('http') ? image : `https://medoraai.me${image}`;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <meta name="author" content={author} />
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="theme-color" content="#3B82F6" />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImage} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImage} />
      <meta name="twitter:site" content="@medora_ai" />
      <meta name="twitter:creator" content="@medora_ai" />
      
      {/* Additional SEO Meta Tags */}
      <meta name="application-name" content={siteName} />
      <meta name="apple-mobile-web-app-title" content={siteName} />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="mobile-web-app-capable" content="yes" />
      
      {/* Favicon and Icons */}
      <link rel="icon" type="image/png" href="/lovable-uploads/logo-png.png" />
      <link rel="apple-touch-icon" href="/lovable-uploads/logo-png.png" />
      <link rel="shortcut icon" href="/lovable-uploads/logo-png.png" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={fullUrl} />
      
      {/* Preconnect for Performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* JSON-LD Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": siteName,
          "description": description,
          "url": fullUrl,
          "image": fullImage,
          "author": {
            "@type": "Organization",
            "name": author
          },
          "applicationCategory": "HealthApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          }
        })}
      </script>
    </Helmet>
  );
};

export default SEOHead;
