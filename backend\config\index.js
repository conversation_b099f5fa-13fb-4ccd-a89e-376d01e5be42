/**
 * MEDORA Backend Configuration
 * Centralized configuration management to replace process.env references
 */

require('dotenv').config({ path: require('path').join(__dirname, '..', '.env') });

// Configuration object with all environment variables and defaults
const config = {
  // Server Configuration
  server: {
    port: process.env.PORT || 5000,
    nodeEnv: process.env.NODE_ENV || 'development',
    frontendUrl: process.env.FRONTEND_URL || 'https://medora-main-kdc735go4-bmds-projects-6efc3abf.vercel.app',
    isProduction: (process.env.NODE_ENV || 'development') === 'production',
    isDevelopment: (process.env.NODE_ENV || 'development') === 'development'
  },

  // Database Configuration
  database: {
    mongoUri: process.env.MONGODB_URI || (
      (process.env.NODE_ENV || 'development') === 'production'
        ? 'mongodb+srv://medora:<Americana123456789@>@medora.rauhtwy.mongodb.net/medora?retryWrites=true&w=majority'
        : 'mongodb://localhost:27017/medora'
    ),
    dbName: process.env.DB_NAME || 'medora',
    redisUrl: process.env.REDIS_URL || 'redis://localhost:6379'
  },

  // Authentication & Security
  auth: {
    jwtSecret: process.env.JWT_SECRET || 'medoraunrbun76by57vb3JWT45t75867654',
    sessionSecret: process.env.SESSION_SECRET || process.env.JWT_SECRET || 'medora-session-secret',
    jwtExpire: process.env.JWT_EXPIRE || '7d',
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12
  },

  // CORS Configuration
  cors: {
    allowedOrigins: process.env.ALLOWED_ORIGINS ? 
      process.env.ALLOWED_ORIGINS.split(',').map(url => url.trim()) : [],
    corsOrigin: process.env.CORS_ORIGIN || '',
    defaultOrigins: [
      // Local development
      'http://localhost:1200',
      'http://localhost:1201',
      'http://localhost:3000',
      'http://localhost:5173',
      'http://127.0.0.1:1200',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:5173',
      
      // Production domains
      'https://www.medoraai.me',
      'https://medoraai.me',
      
      // Backend Vercel deployments
      'https://medora-ai-backend.vercel.app',
      'https://medora-ai-backend-git-main-obibiifeanyi.vercel.app',
      'https://medora-ai-backend-obibiifeanyi.vercel.app',
      
      // Frontend Vercel deployments
      'https://medora-main-kdc735go4-bmds-projects-6efc3abf.vercel.app',
      'https://medora-main.vercel.app'
    ]
  },

  // AI Services Configuration
  ai: {
    openaiApiKey: process.env.OPENAI_API_KEY || '',
    openaiModel: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
    openaiMaxTokens: parseInt(process.env.OPENAI_MAX_TOKENS) || 500,
    openaiTemperature: parseFloat(process.env.OPENAI_TEMPERATURE) || 0.7,
    anthropicApiKey: process.env.ANTHROPIC_API_KEY || '',
    googleAiApiKey: process.env.GOOGLE_AI_API_KEY || '',
    
    // Feature flags
    enableAiChat: process.env.ENABLE_AI_CHAT === 'true',
    enableMlAnalysis: process.env.ENABLE_ML_ANALYSIS === 'true',
    enableVoiceFeatures: process.env.ENABLE_VOICE_FEATURES === 'true'
  },

  // Payment Gateways
  payments: {
    paystack: {
      secretKey: process.env.PAYSTACK_SECRET_KEY || '',
      publicKey: process.env.PAYSTACK_PUBLIC_KEY || ''
    },
    flutterwave: {
      secretKey: process.env.FLW_SECRET_KEY || '',
      publicKey: process.env.FLW_PUBLIC_KEY || '',
      secretHash: process.env.FLW_SECRET_HASH || ''
    }
  },

  // Email Configuration
  email: {
    smtpHost: process.env.SMTP_HOST || 'smtp.gmail.com',
    smtpPort: parseInt(process.env.SMTP_PORT) || 587,
    smtpUser: process.env.SMTP_USER || '',
    smtpPass: process.env.SMTP_PASS || '',
    fromEmail: process.env.FROM_EMAIL || '<EMAIL>',
    fromName: process.env.FROM_NAME || 'MEDORA System'
  },

  // OAuth Configuration
  oauth: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || ''
    },
    facebook: {
      appId: process.env.FACEBOOK_APP_ID || '',
      appSecret: process.env.FACEBOOK_APP_SECRET || ''
    },
    github: {
      clientId: process.env.GITHUB_CLIENT_ID || '',
      clientSecret: process.env.GITHUB_CLIENT_SECRET || ''
    },
    twitter: {
      clientId: process.env.TWITTER_CLIENT_ID || '',
      clientSecret: process.env.TWITTER_CLIENT_SECRET || ''
    },
    apple: {
      clientId: process.env.APPLE_CLIENT_ID || '',
      privateKey: process.env.APPLE_PRIVATE_KEY || '',
      keyId: process.env.APPLE_KEY_ID || '',
      teamId: process.env.APPLE_TEAM_ID || ''
    },
    linkedin: {
      clientId: process.env.LINKEDIN_CLIENT_ID || '',
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET || ''
    },
    callbackBaseUrl: process.env.OAUTH_CALLBACK_BASE_URL || 'https://medora-ai-backend.vercel.app/api/auth'
  },

  // File Upload
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10485760, // 10MB
    uploadPath: process.env.UPLOAD_PATH || './uploads'
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/medora.log'
  },

  // Rate Limiting
  rateLimit: {
    window: parseInt(process.env.RATE_LIMIT_WINDOW) || 900000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX) || 100
  },

  // Security
  security: {
    helmetCsp: process.env.HELMET_CSP === 'true'
  },

  // Web Scraping
  scraping: {
    delay: parseInt(process.env.SCRAPE_DELAY) || 1000,
    maxConcurrent: parseInt(process.env.MAX_CONCURRENT_SCRAPES) || 5,
    userAgent: process.env.USER_AGENT || 'MEDORA-Bot/1.0'
  },

  // Medical APIs
  medicalApis: {
    fdaApiKey: process.env.FDA_API_KEY || '',
    pubmedApiKey: process.env.PUBMED_API_KEY || '',
    drugsApiKey: process.env.DRUGS_API_KEY || ''
  },

  // Machine Learning
  ml: {
    modelPath: process.env.ML_MODEL_PATH || './models',
    tensorflowBackend: process.env.TENSORFLOW_BACKEND || 'cpu'
  },

  // Webhooks
  webhooks: {
    slackUrl: process.env.SLACK_WEBHOOK_URL || '',
    discordUrl: process.env.DISCORD_WEBHOOK_URL || ''
  }
};

// Helper functions
config.getAllowedOrigins = function() {
  const origins = [
    ...this.cors.defaultOrigins,
    this.server.frontendUrl,
    this.cors.corsOrigin,
    ...this.cors.allowedOrigins
  ].filter(Boolean);
  
  // Remove duplicates and normalize URLs
  return [...new Set(origins.map(url => url.replace(/\/$/, '')))];
};

config.isOpenAiConfigured = function() {
  return !!this.ai.openaiApiKey;
};

config.isPaystackConfigured = function() {
  return !!this.payments.paystack.secretKey;
};

config.isFlutterwaveConfigured = function() {
  return !!this.payments.flutterwave.secretKey;
};

config.isMongoConfigured = function() {
  return this.database.mongoUri && this.database.mongoUri !== 'mongodb://localhost:27017/medora';
};

// Validation function
config.validate = function() {
  const errors = [];
  
  if (!this.auth.jwtSecret || this.auth.jwtSecret === 'medoraunrbun76by57vb3JWT45t75867654') {
    errors.push('JWT_SECRET should be set to a secure random value');
  }
  
  if (this.server.isProduction && !this.isMongoConfigured()) {
    console.warn('Warning: MongoDB not configured for production environment');
  }
  
  return errors;
};

// Initialize and validate configuration
const validationErrors = config.validate();
if (validationErrors.length > 0) {
  console.warn('Configuration warnings:', validationErrors);
}

module.exports = config;
