import React, { useState, useEffect } from 'react';
import { Search, Plus, Eye, Edit, Trash2, Filter, Download, Calendar, Phone, Mail, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface Patient {
  id: string;
  mrn: string;
  firstName: string;
  lastName: string;
  email: string;
  dateOfBirth: string;
  gender: string;
  phoneNumber: string;
  status: 'active' | 'inactive' | 'deceased' | 'transferred';
  primaryCareProvider?: {
    firstName: string;
    lastName: string;
    specialty: string;
  };
  insurance?: {
    provider: string;
    policyNumber: string;
  };
  medicalHistory?: {
    allergies: Array<{
      allergen: string;
      reaction: string;
      severity: string;
    }>;
    chronicConditions: Array<{
      condition: string;
      diagnosedDate: string;
      status: string;
    }>;
  };
  emergencyContacts?: Array<{
    name: string;
    relationship: string;
    phoneNumber: string;
    email: string;
    isPrimary: boolean;
  }>;
  flags?: Array<{
    type: string;
    description: string;
    severity: string;
    active: boolean;
  }>;
  createdAt: string;
  updatedAt: string;
}

interface PatientManagementProps {
  onPatientSelect?: (patient: Patient) => void;
}

const PatientManagement: React.FC<PatientManagementProps> = ({ onPatientSelect }) => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch patients from API
  useEffect(() => {
    const fetchPatients = async () => {
      try {
        setLoading(true);
        // TODO: Replace with actual API call
        // const response = await apiClient.get('/patients');
        // setPatients(response.data);

        // For now, show empty state
        setPatients([]);
        setTotalPages(1);
      } catch (error) {
        console.error('Error fetching patients:', error);
        setPatients([]);
        setTotalPages(1);
      } finally {
        setLoading(false);
      }
    };

    fetchPatients();
  }, []);

  const filteredPatients = patients.filter(patient => {
    const matchesSearch = 
      patient.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.mrn.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || patient.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'bg-green-500/20 text-green-300 border-green-500/50',
      inactive: 'bg-gray-500/20 text-gray-300 border-gray-500/50',
      deceased: 'bg-red-500/20 text-red-300 border-red-500/50',
      transferred: 'bg-blue-500/20 text-blue-300 border-blue-500/50'
    };
    return variants[status as keyof typeof variants] || variants.active;
  };

  const getSeverityBadge = (severity: string) => {
    const variants = {
      low: 'bg-yellow-500/20 text-yellow-300 border-yellow-500/50',
      moderate: 'bg-orange-500/20 text-orange-300 border-orange-500/50',
      high: 'bg-red-500/20 text-red-300 border-red-500/50',
      critical: 'bg-red-600/30 text-red-200 border-red-600/60'
    };
    return variants[severity as keyof typeof variants] || variants.low;
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const handlePatientClick = (patient: Patient) => {
    setSelectedPatient(patient);
    if (onPatientSelect) {
      onPatientSelect(patient);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{borderBottomColor: '#9ACD32'}}></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">Patient Management</h2>
          <p className="text-gray-400">Manage patient records and medical information</p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={() => setShowCreateDialog(true)}
            style={{backgroundColor: '#9ACD32', color: '#000000'}}
            className="hover:opacity-90"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Patient
          </Button>
          <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search patients by name, email, or MRN..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-gray-800/50 border-gray-600 text-white placeholder-gray-400"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48 bg-gray-800/50 border-gray-600 text-white">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent className="bg-gray-800 border-gray-600">
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="transferred">Transferred</SelectItem>
            <SelectItem value="deceased">Deceased</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Patient List */}
      <div className="grid gap-4">
        {filteredPatients.map((patient) => (
          <Card key={patient.id} className="bg-gray-800/50 border-gray-700 hover:bg-gray-800/70 transition-colors cursor-pointer"
                onClick={() => handlePatientClick(patient)}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center text-white font-semibold" style={{backgroundColor: '#9ACD32'}}>
                    {patient.firstName[0]}{patient.lastName[0]}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      {patient.firstName} {patient.lastName}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      <span>MRN: {patient.mrn}</span>
                      <span>Age: {calculateAge(patient.dateOfBirth)}</span>
                      <span className="capitalize">{patient.gender}</span>
                      {patient.primaryCareProvider && (
                        <span>PCP: Dr. {patient.primaryCareProvider.lastName}</span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {patient.flags && patient.flags.length > 0 && (
                    <div className="flex space-x-1">
                      {patient.flags.filter(flag => flag.active).map((flag, index) => (
                        <Badge key={index} className={`${getSeverityBadge(flag.severity)} text-xs`}>
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          {flag.type}
                        </Badge>
                      ))}
                    </div>
                  )}
                  <Badge className={getStatusBadge(patient.status)}>
                    {patient.status}
                  </Badge>
                  <div className="flex space-x-1">
                    <Button size="sm" variant="ghost" className="text-gray-400 hover:text-white">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost" className="text-gray-400 hover:text-white">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost" className="text-gray-400 hover:text-red-400">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredPatients.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-400">No patients found matching your criteria.</p>
        </div>
      )}

      {/* Patient Details Dialog */}
      {selectedPatient && (
        <Dialog open={!!selectedPatient} onOpenChange={() => setSelectedPatient(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-gray-900 border-gray-700">
            <DialogHeader>
              <DialogTitle className="text-white text-xl">
                {selectedPatient.firstName} {selectedPatient.lastName} - Medical Record
              </DialogTitle>
            </DialogHeader>
            
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4 bg-gray-800">
                <TabsTrigger value="overview" className="text-gray-300 data-[state=active]:text-white">Overview</TabsTrigger>
                <TabsTrigger value="medical" className="text-gray-300 data-[state=active]:text-white">Medical History</TabsTrigger>
                <TabsTrigger value="contacts" className="text-gray-300 data-[state=active]:text-white">Contacts</TabsTrigger>
                <TabsTrigger value="insurance" className="text-gray-300 data-[state=active]:text-white">Insurance</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-gray-300">Medical Record Number</Label>
                    <p className="text-white font-mono">{selectedPatient.mrn}</p>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-gray-300">Date of Birth</Label>
                    <p className="text-white">{new Date(selectedPatient.dateOfBirth).toLocaleDateString()}</p>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-gray-300">Email</Label>
                    <p className="text-white">{selectedPatient.email}</p>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-gray-300">Phone</Label>
                    <p className="text-white">{selectedPatient.phoneNumber}</p>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-gray-300">Status</Label>
                    <Badge className={getStatusBadge(selectedPatient.status)}>
                      {selectedPatient.status}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-gray-300">Age</Label>
                    <p className="text-white">{calculateAge(selectedPatient.dateOfBirth)} years</p>
                  </div>
                </div>
                
                {selectedPatient.primaryCareProvider && (
                  <div className="space-y-2">
                    <Label className="text-gray-300">Primary Care Provider</Label>
                    <p className="text-white">
                      {selectedPatient.primaryCareProvider.firstName} {selectedPatient.primaryCareProvider.lastName}
                      <span className="text-gray-400 ml-2">({selectedPatient.primaryCareProvider.specialty})</span>
                    </p>
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="medical" className="space-y-4">
                {selectedPatient.medicalHistory?.allergies && selectedPatient.medicalHistory.allergies.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-gray-300">Allergies</Label>
                    <div className="space-y-2">
                      {selectedPatient.medicalHistory.allergies.map((allergy, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                          <div>
                            <p className="text-white font-medium">{allergy.allergen}</p>
                            <p className="text-gray-400 text-sm">{allergy.reaction}</p>
                          </div>
                          <Badge className={getSeverityBadge(allergy.severity)}>
                            {allergy.severity}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {selectedPatient.medicalHistory?.chronicConditions && selectedPatient.medicalHistory.chronicConditions.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-gray-300">Chronic Conditions</Label>
                    <div className="space-y-2">
                      {selectedPatient.medicalHistory.chronicConditions.map((condition, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                          <div>
                            <p className="text-white font-medium">{condition.condition}</p>
                            <p className="text-gray-400 text-sm">
                              Diagnosed: {new Date(condition.diagnosedDate).toLocaleDateString()}
                            </p>
                          </div>
                          <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/50">
                            {condition.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {selectedPatient.flags && selectedPatient.flags.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-gray-300">Medical Flags</Label>
                    <div className="space-y-2">
                      {selectedPatient.flags.filter(flag => flag.active).map((flag, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                          <div className="flex items-center space-x-2">
                            <AlertTriangle className="h-4 w-4 text-yellow-500" />
                            <div>
                              <p className="text-white font-medium capitalize">{flag.type}</p>
                              <p className="text-gray-400 text-sm">{flag.description}</p>
                            </div>
                          </div>
                          <Badge className={getSeverityBadge(flag.severity)}>
                            {flag.severity}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="contacts" className="space-y-4">
                {selectedPatient.emergencyContacts && selectedPatient.emergencyContacts.length > 0 ? (
                  <div className="space-y-4">
                    {selectedPatient.emergencyContacts.map((contact, index) => (
                      <div key={index} className="p-4 bg-gray-800 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-white font-medium">{contact.name}</h4>
                          {contact.isPrimary && (
                            <Badge className="bg-green-500/20 text-green-300 border-green-500/50">
                              Primary
                            </Badge>
                          )}
                        </div>
                        <div className="space-y-1 text-sm">
                          <p className="text-gray-400">Relationship: <span className="text-white">{contact.relationship}</span></p>
                          <div className="flex items-center space-x-2 text-gray-400">
                            <Phone className="h-3 w-3" />
                            <span className="text-white">{contact.phoneNumber}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-gray-400">
                            <Mail className="h-3 w-3" />
                            <span className="text-white">{contact.email}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-400">No emergency contacts on file.</p>
                )}
              </TabsContent>
              
              <TabsContent value="insurance" className="space-y-4">
                {selectedPatient.insurance ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="text-gray-300">Insurance Provider</Label>
                        <p className="text-white">{selectedPatient.insurance.provider}</p>
                      </div>
                      <div className="space-y-2">
                        <Label className="text-gray-300">Policy Number</Label>
                        <p className="text-white font-mono">{selectedPatient.insurance.policyNumber}</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-400">No insurance information on file.</p>
                )}
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>
      )}

      {/* Create Patient Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl bg-gray-900 border-gray-700">
          <DialogHeader>
            <DialogTitle className="text-white">Add New Patient</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-gray-300">First Name</Label>
                <Input className="bg-gray-800 border-gray-600 text-white" placeholder="Enter first name" />
              </div>
              <div className="space-y-2">
                <Label className="text-gray-300">Last Name</Label>
                <Input className="bg-gray-800 border-gray-600 text-white" placeholder="Enter last name" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-gray-300">Email</Label>
                <Input type="email" className="bg-gray-800 border-gray-600 text-white" placeholder="Enter email" />
              </div>
              <div className="space-y-2">
                <Label className="text-gray-300">Phone Number</Label>
                <Input className="bg-gray-800 border-gray-600 text-white" placeholder="Enter phone number" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-gray-300">Date of Birth</Label>
                <Input type="date" className="bg-gray-800 border-gray-600 text-white" />
              </div>
              <div className="space-y-2">
                <Label className="text-gray-300">Gender</Label>
                <Select>
                  <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600">
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                    <SelectItem value="prefer_not_to_say">Prefer not to say</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setShowCreateDialog(false)} className="border-gray-600 text-gray-300">
                Cancel
              </Button>
              <Button style={{backgroundColor: '#9ACD32', color: '#000000'}} className="hover:opacity-90">
                Create Patient
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PatientManagement;