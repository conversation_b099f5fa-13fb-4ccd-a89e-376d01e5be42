import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Logo } from "@/components/Logo";
import { useAuth } from "@/hooks/useAuth";
import { SocialLoginButtons } from "@/components/auth/SocialLoginButtons";
import { toast } from "sonner";
import {
  Mail,
  Lock,
  User,
  Eye,
  EyeOff,
  ArrowRight,
  AlertTriangle,
  CheckCircle,
  Phone,
  MapPin,
  Calendar
} from "lucide-react";

const Signup = () => {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    password: "",
    confirmPassword: "",
    role: "patient" as "patient" | "doctor" | "nurse" | "admin",
    specialty: "",
    licenseNumber: "",
    dateOfBirth: "",
    gender: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [localError, setLocalError] = useState("");
  const [success, setSuccess] = useState("");
  const { register, loading, error: authError } = useAuth();
  const navigate = useNavigate();

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear errors when user starts typing
    if (localError) {
      setLocalError("");
    }
  };

  // Password strength calculation
  const getPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 6) strength++;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
  };

  const passwordStrength = getPasswordStrength(formData.password);
  const getStrengthLabel = (strength: number) => {
    if (strength <= 2) return { label: 'Weak', color: 'text-red-500' };
    if (strength <= 4) return { label: 'Medium', color: 'text-yellow-500' };
    return { label: 'Strong', color: 'text-green-500' };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLocalError("");
    setSuccess("");

    // Enhanced validation
    if (!formData.firstName.trim()) {
      setLocalError("First name is required");
      return;
    }

    if (!formData.lastName.trim()) {
      setLocalError("Last name is required");
      return;
    }

    if (!formData.email.trim()) {
      setLocalError("Email is required");
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setLocalError("Please enter a valid email address");
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setLocalError("Passwords do not match");
      return;
    }

    if (formData.password.length < 6) {
      setLocalError("Password must be at least 6 characters long");
      return;
    }

    // Password strength validation
    const hasUpperCase = /[A-Z]/.test(formData.password);
    const hasLowerCase = /[a-z]/.test(formData.password);
    const hasNumbers = /\d/.test(formData.password);

    if (!hasUpperCase || !hasLowerCase || !hasNumbers) {
      setLocalError("Password must contain at least one uppercase letter, one lowercase letter, and one number");
      return;
    }

    // Role-specific validation
    if (formData.role === 'patient') {
      if (!formData.dateOfBirth || !formData.dateOfBirth.trim()) {
        setLocalError("Date of birth is required for patients");
        return;
      }

      // Validate date format
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(formData.dateOfBirth)) {
        setLocalError("Please enter a valid date of birth");
        return;
      }
    }

    if (formData.role === 'patient' && formData.dateOfBirth) {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();

      if (age < 0 || age > 120) {
        setLocalError("Please enter a valid date of birth");
        return;
      }
    }

    if (formData.role === 'doctor' && !formData.specialty.trim()) {
      setLocalError("Medical specialty is required for doctors");
      return;
    }

    if ((formData.role === 'doctor' || formData.role === 'nurse') && !formData.licenseNumber.trim()) {
      setLocalError("License number is required for medical professionals");
      return;
    }

    // Build registration data based on role
    const registerData = {
      firstName: formData.firstName.trim(),
      lastName: formData.lastName.trim(),
      email: formData.email.trim().toLowerCase(),
      password: formData.password,
      phoneNumber: formData.phoneNumber.trim() || undefined,
      role: formData.role
    };

    // Add role-specific fields
    if (formData.role === 'patient') {
      console.log('🔗 Adding patient fields:', {
        dateOfBirth: formData.dateOfBirth,
        gender: formData.gender
      });
      registerData.dateOfBirth = formData.dateOfBirth;
      if (formData.gender) {
        registerData.gender = formData.gender;
      }
    }

    if (formData.role === 'doctor') {
      registerData.specialty = formData.specialty;
      registerData.licenseNumber = formData.licenseNumber;
    }

    if (formData.role === 'nurse') {
      registerData.licenseNumber = formData.licenseNumber;
    }

    console.log('🔗 Submitting registration data:', registerData);

    try {
      const success = await register(registerData);
      if (success) {
        setSuccess("Account created successfully! Redirecting to dashboard...");
        setTimeout(() => navigate("/dashboard"), 2000);
      } else {
        console.error('❌ Registration failed');
        // Handle specific error cases
        if (authError?.includes('Email already in use') || authError?.includes('already exists')) {
          setLocalError("This email is already registered. Try logging in instead or use a different email address.");
        } else if (authError?.includes('validation')) {
          setLocalError("Please check your information and try again.");
        } else {
          setLocalError(authError || "Registration failed. Please try again.");
        }
      }
    } catch (error) {
      console.error('💥 Registration error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Registration failed';

      // Handle specific error types
      if (errorMessage.includes('409') || errorMessage.includes('Email already in use') || errorMessage.includes('already exists')) {
        setLocalError("This email is already registered. Try logging in instead or use a different email address.");
      } else if (errorMessage.includes('400')) {
        setLocalError("Invalid information provided. Please check your details and try again.");
      } else if (errorMessage.includes('500')) {
        setLocalError("Server error. Please try again later.");
      } else {
        setLocalError(errorMessage);
      }
    }
  };

  const [socialLoading, setSocialLoading] = useState(false);

  const handleSocialError = (error: string) => {
    toast.error(error);
    setLocalError(error);
  };

  const handleSocialLoading = (isLoading: boolean) => {
    setSocialLoading(isLoading);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-pink-50 dark:bg-black flex items-center justify-center p-4 transition-colors duration-300">
      <div className="w-full max-w-2xl">
        {/* Go Back Home Button */}
        <div className="mb-6">
          <Link to="/">
            <Button variant="ghost" className="text-muted-foreground hover:text-primary">
              ← Back to Home
            </Button>
          </Link>
        </div>

        {/* Logo and Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Logo size="lg" showText={true} />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 transition-colors duration-300">Create your account</h1>
          <p className="text-gray-600 dark:text-gray-300 transition-colors duration-300">Join MEDORA and revolutionize your healthcare practice</p>
        </div>

        <Card className="modern-card">
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl text-center">Sign Up</CardTitle>
            <CardDescription className="text-center">
              Create your MEDORA account to get started
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Social Login Buttons */}
            <SocialLoginButtons
              mode="register"
              onError={handleSocialError}
              onLoading={handleSocialLoading}
            />

            {/* Error/Success Messages */}
            {(localError || authError) && (
              <div className="flex items-start space-x-3 p-4 bg-red-50 border border-red-200 rounded-lg">
                <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <span className="text-red-800 text-sm font-medium">{localError || authError}</span>
                  {(localError?.includes('already registered') || authError?.includes('already registered')) && (
                    <div className="mt-2">
                      <Link
                        to="/login"
                        className="text-red-700 hover:text-red-800 text-sm underline font-medium"
                      >
                        Go to login page →
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            )}

            {success && (
              <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-green-800 text-sm">{success}</span>
              </div>
            )}

            {/* Signup Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange("firstName", e.target.value)}
                      placeholder="Enter your first name"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange("lastName", e.target.value)}
                      placeholder="Enter your last name"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      placeholder="Enter your email"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    <Input
                      id="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                      placeholder="Enter your phone number"
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              {/* Date of Birth and Gender - Only for Patients */}
              {formData.role === 'patient' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth">
                      Date of Birth
                      <span className="text-red-500 ml-1">*</span>
                    </Label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                      <Input
                        id="dateOfBirth"
                        type="date"
                        value={formData.dateOfBirth}
                        onChange={(e) => handleInputChange("dateOfBirth", e.target.value)}
                        className="pl-10"
                        required
                        max={new Date().toISOString().split('T')[0]} // Prevent future dates
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="gender">Gender</Label>
                    <Select value={formData.gender} onValueChange={(value) => handleInputChange("gender", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                        <SelectItem value="prefer_not_to_say">Prefer not to say</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <Select value={formData.role} onValueChange={(value) => handleInputChange("role", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="doctor">Doctor</SelectItem>
                      <SelectItem value="nurse">Nurse</SelectItem>
                      <SelectItem value="admin">Administrator</SelectItem>
                      <SelectItem value="patient">Patient</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="specialty">Medical Specialty</Label>
                  <Select
                    value={formData.specialty}
                    onValueChange={(value) => handleInputChange("specialty", value)}
                    disabled={formData.role !== 'doctor'}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select specialty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cardiology">Cardiology</SelectItem>
                      <SelectItem value="dermatology">Dermatology</SelectItem>
                      <SelectItem value="endocrinology">Endocrinology</SelectItem>
                      <SelectItem value="gastroenterology">Gastroenterology</SelectItem>
                      <SelectItem value="neurology">Neurology</SelectItem>
                      <SelectItem value="oncology">Oncology</SelectItem>
                      <SelectItem value="orthopedics">Orthopedics</SelectItem>
                      <SelectItem value="pediatrics">Pediatrics</SelectItem>
                      <SelectItem value="psychiatry">Psychiatry</SelectItem>
                      <SelectItem value="radiology">Radiology</SelectItem>
                      <SelectItem value="surgery">Surgery</SelectItem>
                      <SelectItem value="urology">Urology</SelectItem>
                      <SelectItem value="general_practice">General Practice</SelectItem>
                      <SelectItem value="emergency_medicine">Emergency Medicine</SelectItem>
                      <SelectItem value="internal_medicine">Internal Medicine</SelectItem>
                      <SelectItem value="family_medicine">Family Medicine</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="licenseNumber">
                    License Number
                    {(formData.role === 'doctor' || formData.role === 'nurse') && (
                      <span className="text-red-500 ml-1">*</span>
                    )}
                  </Label>
                  <Input
                    id="licenseNumber"
                    value={formData.licenseNumber}
                    onChange={(e) => handleInputChange("licenseNumber", e.target.value)}
                    placeholder={formData.role === 'doctor' ? "e.g., MD123456" : formData.role === 'nurse' ? "e.g., RN123456" : "License number"}
                    className=""
                    disabled={formData.role !== 'doctor' && formData.role !== 'nurse'}
                    required={formData.role === 'doctor' || formData.role === 'nurse'}
                  />
                  {(formData.role === 'doctor' || formData.role === 'nurse') && (
                    <p className="text-xs text-muted-foreground">
                      Required for medical professionals
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={(e) => handleInputChange("password", e.target.value)}
                      placeholder="Create a password"
                      className="pl-10 pr-10"
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </Button>
                  </div>
                  {formData.password && (
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              passwordStrength <= 2 ? 'bg-red-500 w-1/3' :
                              passwordStrength <= 4 ? 'bg-yellow-500 w-2/3' :
                              'bg-green-500 w-full'
                            }`}
                          />
                        </div>
                        <span className={`text-xs font-medium ${getStrengthLabel(passwordStrength).color}`}>
                          {getStrengthLabel(passwordStrength).label}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Password must contain: uppercase, lowercase, number (6+ chars)
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                      placeholder="Confirm your password"
                      className="pl-10 pr-10"
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="terms"
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  required
                />
                <Label htmlFor="terms" className="text-sm text-muted-foreground">
                  I agree to the{" "}
                  <a href="#" className="text-primary hover:text-primary/80">Terms of Service</a>
                  {" "}and{" "}
                  <a href="#" className="text-primary hover:text-primary/80">Privacy Policy</a>
                </Label>
              </div>

              <Button
                type="submit"
                disabled={loading || socialLoading}
                className="w-full gradient-primary hover:scale-105 transition-transform"
              >
                {loading ? (
                  <>
                    <div className="loading-spinner w-4 h-4 mr-2"></div>
                    Creating account...
                  </>
                ) : socialLoading ? (
                  <>
                    <div className="loading-spinner w-4 h-4 mr-2"></div>
                    Connecting...
                  </>
                ) : (
                  <>
                    Create Account
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Sign In Link */}
        <div className="text-center mt-6">
          <p className="text-gray-600">
            Already have an account?{" "}
            <Link 
              to="/login"
              className="text-green-600 hover:text-green-700 font-semibold transition-colors"
            >
              Sign in here
            </Link>
          </p>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-xs text-gray-500">
            By creating an account, you agree to our{" "}
            <a href="#" className="text-green-600 hover:text-green-700">Terms of Service</a>
            {" "}and{" "}
            <a href="#" className="text-green-600 hover:text-green-700">Privacy Policy</a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Signup;