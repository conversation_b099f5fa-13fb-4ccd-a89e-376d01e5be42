# MEDORA - Application Administration and Enhancement (AAE) Manual

## 📋 Table of Contents

1. [System Overview](#system-overview)
2. [Prerequisites](#prerequisites)
3. [Installation & Setup](#installation--setup)
4. [CORS Configuration](#cors-configuration)
5. [Backend Configuration](#backend-configuration)
6. [Frontend Configuration](#frontend-configuration)
7. [Database Setup](#database-setup)
8. [Environment Variables](#environment-variables)
9. [UI Enhancement Guide](#ui-enhancement-guide)
10. [Deployment](#deployment)
11. [Troubleshooting](#troubleshooting)
12. [Security Considerations](#security-considerations)

---

## 🔍 System Overview

MEDORA is a comprehensive AI-powered medical assistant platform built with:

- **Frontend**: React 18 + TypeScript + Vite + TailwindCSS + Radix UI
- **Backend**: Node.js + Express + MongoDB + Socket.io
- **AI Integration**: OpenAI GPT models with multimodal capabilities
- **Payment**: Paystack & Flutterwave integration
- **Authentication**: JWT + OAuth (Google, Facebook, GitHub, LinkedIn, Twitter)

### Architecture Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │    Database     │
│   (Port 1200)   │◄──►│   (Port 5000)   │◄──►│    MongoDB      │
│                 │    │                 │    │                 │
│ • React App     │    │ • Express API   │    │ • User Data     │
│ • UI Components │    │ • Socket.io     │    │ • Medical Data  │
│ • State Mgmt    │    │ • AI Services   │    │ • Settings      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## 📋 Prerequisites

### System Requirements

- **Node.js**: Version 18.0.0 or higher
- **MongoDB**: Version 6.0 or higher
- **npm**: Version 8.0.0 or higher
- **Git**: Latest version
- **Operating System**: Windows 10/11, macOS 10.15+, or Linux Ubuntu 20.04+

### Development Tools (Optional)

- **VS Code**: Recommended IDE with extensions:
  - ES7+ React/Redux/React-Native snippets
  - Tailwind CSS IntelliSense
  - TypeScript Importer
  - MongoDB for VS Code
- **Postman**: For API testing
- **MongoDB Compass**: For database management

---

## 🚀 Installation & Setup

### Step 1: Clone and Navigate

```bash
# Clone the repository
git clone <repository-url>
cd medora-main

# Install frontend dependencies
npm install

# Install backend dependencies
cd backend
npm install
cd ..
```

### Step 2: Environment Configuration

```bash
# Copy environment template
cp backend/.env.example backend/.env

# Edit the .env file with your configurations
# See Environment Variables section for details
```

### Step 3: Database Setup

```bash
# Start MongoDB service
# Windows (if MongoDB is installed as service):
net start MongoDB

# macOS (using Homebrew):
brew services start mongodb-community

# Linux (systemd):
sudo systemctl start mongod
```

### Step 4: Start the Application

```bash
# Option 1: Start both frontend and backend together
npm start

# Option 2: Start individually
# Terminal 1 - Backend
npm run backend

# Terminal 2 - Frontend
npm run frontend
```

### Step 5: Verify Installation

- **Frontend**: http://localhost:1200
- **Backend API**: http://localhost:5000/api
- **Health Check**: http://localhost:5000/health

---

## 🌐 CORS Configuration

### Understanding CORS in MEDORA

Cross-Origin Resource Sharing (CORS) is configured in multiple places to ensure secure communication between frontend and backend.

### Backend CORS Setup

**File**: `backend/server.js` and `backend/simple-server.js`

#### Development Configuration

```javascript
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:1200',    // Primary frontend port
      'http://localhost:5173',    // Vite default port
      'http://127.0.0.1:1200',
      'http://127.0.0.1:5173',
      process.env.FRONTEND_URL
    ].filter(Boolean);
    
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));
```

#### Production Configuration

```javascript
// For production, use specific domains
const corsOptions = {
  origin: [
    'https://yourdomain.com',
    'https://www.yourdomain.com',
    'https://app.yourdomain.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};
```

### Frontend CORS Configuration

**File**: `vite.config.ts`

```typescript
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 1200,
    cors: true,  // Enable CORS for development
    proxy: {
      // Proxy API requests to backend
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false
      }
    }
  }
}));
```

### Socket.io CORS Configuration

```javascript
const io = new Server(server, {
  cors: {
    origin: [
      process.env.FRONTEND_URL || "http://localhost:1200",
      "http://localhost:3000",
      "http://localhost:5173"
    ],
    methods: ["GET", "POST"],
    credentials: true
  }
});
```

### Environment-Based CORS

**File**: `backend/.env`

```bash
# Development
FRONTEND_URL=http://localhost:1200
ALLOWED_ORIGINS=http://localhost:1200,http://localhost:3000,http://localhost:5173

# Production
FRONTEND_URL=https://yourdomain.com
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

### Common CORS Issues & Solutions

#### Issue 1: "Access to fetch blocked by CORS policy"

**Solution**: Ensure the frontend URL is included in the backend's allowed origins.

```javascript
// Add your frontend URL to allowedOrigins array
const allowedOrigins = [
  'http://localhost:1200',  // Add this line
  // ... other origins
];
```

#### Issue 2: Credentials not included in CORS request

**Solution**: Ensure both frontend and backend are configured for credentials.

```javascript
// Backend
app.use(cors({ credentials: true }));

// Frontend (in API calls)
fetch('/api/endpoint', {
  credentials: 'include'
});
```

#### Issue 3: Preflight OPTIONS request failing

**Solution**: Ensure OPTIONS method is allowed.

```javascript
app.use(cors({
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS']
}));
```

---

## 🔧 Backend Configuration

### Core Server Configuration

**File**: `backend/simple-server.js` (Primary server)

#### Key Configuration Sections

1. **Express App Setup**
2. **Middleware Configuration**
3. **Database Connection**
4. **Route Registration**
5. **Error Handling**

### Middleware Stack

```javascript
// Security middleware
app.use(helmet());
app.use(cors(corsOptions));

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Session management
app.use(session({
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Authentication
app.use(passport.initialize());
app.use(passport.session());
```

### Database Configuration

#### MongoDB Connection

```javascript
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/lawra';

mongoose.connect(MONGODB_URI, {
  dbName: process.env.DB_NAME || 'medora',
  useNewUrlParser: true,
  useUnifiedTopology: true
});
```

#### Connection Event Handlers

```javascript
mongoose.connection.on('connected', () => {
  console.log('✅ Connected to MongoDB');
});

mongoose.connection.on('error', (err) => {
  console.error('❌ MongoDB connection error:', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('⚠️ MongoDB disconnected');
});
```

### API Routes Structure

```
/api
├── /auth          # Authentication endpoints
├── /patients      # Patient management
├── /diagnosis     # Medical diagnosis
├── /ml           # Machine learning analysis
├── /ai           # AI chat and consultation
├── /enhanced-ai  # Advanced AI features
├── /payments     # Payment processing
└── /admin        # Administrative functions
```

### Authentication Middleware

```javascript
function requireAuth(req, res, next) {
  try {
    const auth = req.headers.authorization || '';
    const token = auth.startsWith('Bearer ') ? auth.slice(7) : null;
    
    if (!token) {
      return res.status(401).json({ 
        success: false, 
        error: 'Unauthorized' 
      });
    }
    
    const payload = jwt.verify(token, process.env.JWT_SECRET);
    req.user = payload;
    next();
  } catch (e) {
    return res.status(401).json({ 
      success: false, 
      error: 'Unauthorized' 
    });
  }
}
```

### Role-Based Access Control

```javascript
function requireRole(role) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        success: false, 
        error: 'Unauthorized' 
      });
    }
    
    if (req.user.role !== role) {
      return res.status(403).json({ 
        success: false, 
        error: 'Forbidden' 
      });
    }
    
    next();
  };
}

// Usage
app.get('/api/admin/users', requireAuth, requireRole('admin'), handler);
```

---

## 🎨 Frontend Configuration

### Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Base UI components (Radix UI)
│   ├── auth/           # Authentication components
│   └── admin/          # Admin-specific components
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── services/           # API service functions
├── lib/                # Utility functions
└── assets/             # Static assets
```

### Key Configuration Files

#### 1. Vite Configuration (`vite.config.ts`)

```typescript
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 1200,
    cors: true,
  },
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    outDir: "dist",
    sourcemap: mode === "development",
    minify: mode === "production",
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ["react", "react-dom"],
          ui: ["@radix-ui/react-dialog", "@radix-ui/react-dropdown-menu"],
        },
      },
    },
  },
}));
```

#### 2. TypeScript Configuration (`tsconfig.json`)

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

#### 3. TailwindCSS Configuration (`tailwind.config.ts`)

```typescript
export default {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        // ... more color definitions
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
```

### API Service Configuration

**File**: `src/services/api.ts`

```typescript
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

class ApiService {
  private baseURL: string;

  constructor() {
    this.baseURL = API_BASE_URL;
  }

  async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      credentials: 'include',
      ...options,
    };

    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // HTTP method helpers
  get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

export const apiService = new ApiService();
```

### State Management

MEDORA uses React's built-in state management with custom hooks for complex state logic.

#### Authentication Hook (`src/hooks/useAuth.tsx`)

```typescript
interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (userData: RegisterData) => Promise<void>;
  loading: boolean;
  error: string | null;
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

---

## 💾 Database Setup

### MongoDB Installation

#### Windows

1. Download MongoDB Community Server from [mongodb.com](https://www.mongodb.com/try/download/community)
2. Run the installer and follow the setup wizard
3. Choose "Complete" installation
4. Install MongoDB as a Windows Service
5. Install MongoDB Compass (GUI tool)

#### macOS

```bash
# Using Homebrew
brew tap mongodb/brew
brew install mongodb-community

# Start MongoDB service
brew services start mongodb-community
```

#### Linux (Ubuntu/Debian)

```bash
# Import MongoDB public GPG key
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# Create list file for MongoDB
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Update package database
sudo apt-get update

# Install MongoDB
sudo apt-get install -y mongodb-org

# Start MongoDB service
sudo systemctl start mongod
sudo systemctl enable mongod
```

### Database Configuration

#### Connection String Format

```bash
# Local MongoDB
MONGODB_URI=mongodb://localhost:27017/medora

# MongoDB with authentication
MONGODB_URI=**************************************************

# MongoDB Atlas (Cloud)
MONGODB_URI=mongodb+srv://username:<EMAIL>/medora

# MongoDB with replica set
MONGODB_URI=mongodb://host1:27017,host2:27017,host3:27017/medora?replicaSet=rs0
```

#### Database Schema

The application uses the following main collections:

1. **users** - User accounts and profiles
2. **patients** - Patient medical records
3. **diagnoses** - Medical diagnosis records
4. **payments** - Payment transaction logs
5. **subscriptions** - User subscription data
6. **settings** - Application configuration
7. **mlanalyses** - Machine learning analysis results

#### Initial Data Setup

```javascript
// Create default admin user
const adminUser = {
  email: '<EMAIL>',
  password: 'hashedPassword',
  firstName: 'Admin',
  lastName: 'User',
  role: 'admin',
  isVerified: true,
  createdAt: new Date()
};

// Create default settings
const defaultSettings = {
  siteName: 'MEDORA',
  siteDescription: 'AI-Powered Medical Assistant',
  timezone: 'UTC',
  language: 'en',
  maintenanceMode: false,
  // ... more settings
};
```

### Database Indexes

For optimal performance, create the following indexes:

```javascript
// User collection indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ role: 1 });
db.users.createIndex({ createdAt: -1 });

// Patient collection indexes
db.patients.createIndex({ userId: 1 });
db.patients.createIndex({ createdAt: -1 });

// Diagnosis collection indexes
db.diagnoses.createIndex({ patientId: 1 });
db.diagnoses.createIndex({ createdAt: -1 });

// Payment collection indexes
db.payments.createIndex({ email: 1 });
db.payments.createIndex({ provider: 1 });
db.payments.createIndex({ createdAt: -1 });
```

### Backup and Restore

#### Backup

```bash
# Backup entire database
mongodump --db medora --out /path/to/backup/

# Backup specific collection
mongodump --db medora --collection users --out /path/to/backup/

# Backup with compression
mongodump --db medora --gzip --out /path/to/backup/
```

#### Restore

```bash
# Restore entire database
mongorestore --db medora /path/to/backup/medora/

# Restore specific collection
mongorestore --db medora --collection users /path/to/backup/medora/users.bson

# Restore from compressed backup
mongorestore --gzip --db medora /path/to/backup/medora/
```

---

## 🔐 Environment Variables

### Complete Environment Configuration

Create a `.env` file in the `backend` directory with the following variables:

```bash
# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=production
PORT=5000
FRONTEND_URL=http://localhost:1200

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MONGODB_URI=mongodb://localhost:27017/medora
DB_NAME=medora

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
JWT_SECRET=your-super-secure-jwt-secret-key-here-min-32-chars
SESSION_SECRET=your-super-secure-session-secret-key-here-min-32-chars
JWT_EXPIRE=7d
BCRYPT_ROUNDS=12

# =============================================================================
# AI & ML SERVICES
# =============================================================================
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.7

# Alternative AI Services (Optional)
ANTHROPIC_API_KEY=your-anthropic-api-key-here
GOOGLE_AI_API_KEY=your-google-ai-api-key-here

# =============================================================================
# PAYMENT GATEWAYS
# =============================================================================
# Paystack Configuration
PAYSTACK_SECRET_KEY=sk_test_your-paystack-secret-key-here
PAYSTACK_PUBLIC_KEY=pk_test_your-paystack-public-key-here

# Flutterwave Configuration
FLW_SECRET_KEY=FLWSECK_TEST-your-flutterwave-secret-key-here
FLW_PUBLIC_KEY=FLWPUBK_TEST-your-flutterwave-public-key-here
FLW_SECRET_HASH=your-flutterwave-webhook-secret-hash-here

# =============================================================================
# SOCIAL AUTHENTICATION (OAuth)
# =============================================================================
# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Facebook OAuth
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# GitHub OAuth
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# LinkedIn OAuth
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Twitter OAuth
TWITTER_CONSUMER_KEY=your-twitter-consumer-key
TWITTER_CONSUMER_SECRET=your-twitter-consumer-secret

# OAuth Callback Base URL
OAUTH_CALLBACK_BASE_URL=http://localhost:5000/api/auth

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=MEDORA System

# =============================================================================
# EXTERNAL MEDICAL APIS
# =============================================================================
FDA_API_KEY=your-fda-api-key-here
PUBMED_API_KEY=your-pubmed-api-key-here
DRUGS_API_KEY=your-drugs-api-key-here

# =============================================================================
# LOGGING & MONITORING
# =============================================================================
LOG_LEVEL=info
LOG_FILE=./logs/medora.log
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_LOGGING=true

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
ALLOWED_ORIGINS=http://localhost:1200,http://localhost:3000,http://localhost:5173
CORS_ORIGIN=http://localhost:1200

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX_REQUESTS=5

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,docx

# =============================================================================
# REDIS CONFIGURATION (Optional)
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password-here

# =============================================================================
# WEB SCRAPING CONFIGURATION
# =============================================================================
SCRAPE_DELAY=1000
MAX_CONCURRENT_SCRAPES=5
USER_AGENT=MEDORA-Bot/1.0

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
HELMET_CSP=true
ENABLE_HTTPS_REDIRECT=false

# =============================================================================
# WEBHOOK URLS
# =============================================================================
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your/discord/webhook

# =============================================================================
# MACHINE LEARNING CONFIGURATION
# =============================================================================
ML_MODEL_PATH=./models
TENSORFLOW_BACKEND=cpu
ENABLE_AI_CHAT=true
ENABLE_ML_ANALYSIS=true
ENABLE_VOICE_FEATURES=true
```

### Environment Variable Descriptions

#### Critical Variables (Required)

| Variable | Description | Example |
|----------|-------------|---------|
| `NODE_ENV` | Application environment | `production` or `development` |
| `PORT` | Backend server port | `5000` |
| `MONGODB_URI` | MongoDB connection string | `mongodb://localhost:27017/medora` |
| `JWT_SECRET` | JWT signing secret (min 32 chars) | `your-super-secure-jwt-secret-key` |
| `FRONTEND_URL` | Frontend application URL | `http://localhost:1200` |

#### Optional Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | OpenAI API key for AI features | None (AI disabled) |
| `PAYSTACK_SECRET_KEY` | Paystack payment gateway | None (payments disabled) |
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | None (Google auth disabled) |
| `SMTP_USER` | Email service username | None (email disabled) |

### Security Best Practices

1. **Never commit `.env` files** to version control
2. **Use strong, unique secrets** for JWT and session keys
3. **Rotate API keys regularly**
4. **Use environment-specific configurations**
5. **Validate environment variables** on application startup

### Environment Validation

Add this to your server startup:

```javascript
const requiredEnvVars = [
  'NODE_ENV',
  'PORT',
  'MONGODB_URI',
  'JWT_SECRET',
  'FRONTEND_URL'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingVars.forEach(varName => {
    console.error(`   - ${varName}`);
  });
  process.exit(1);
}
```

---

## 🎨 UI Enhancement Guide

### Design System Overview

MEDORA uses a modern design system built on:

- **TailwindCSS**: Utility-first CSS framework
- **Radix UI**: Unstyled, accessible UI primitives
- **Lucide React**: Beautiful, customizable icons
- **Custom CSS Variables**: For theming and consistency

### Color Palette

#### Primary Colors

```css
:root {
  --primary: 222.2 84% 4.9%;           /* Dark blue-gray */
  --primary-foreground: 210 40% 98%;   /* Light text on primary */
  --secondary: 210 40% 96%;            /* Light gray */
  --secondary-foreground: 222.2 84% 4.9%; /* Dark text on secondary */
}

[data-theme="dark"] {
  --primary: 210 40% 98%;              /* Light in dark mode */
  --primary-foreground: 222.2 84% 4.9%; /* Dark text in dark mode */
  --secondary: 222.2 84% 4.9%;         /* Dark in dark mode */
  --secondary-foreground: 210 40% 98%; /* Light text in dark mode */
}
```

#### Medical Theme Colors

```css
:root {
  --medical-primary: 200 100% 40%;     /* Medical blue */
  --medical-success: 120 100% 25%;     /* Success green */
  --medical-warning: 45 100% 50%;      /* Warning amber */
  --medical-error: 0 100% 50%;         /* Error red */
  --medical-info: 210 100% 50%;        /* Info blue */
}
```

### Typography System

#### Font Configuration

```css
/* Font imports in index.css */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

body {
  font-family: var(--font-family);
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}
```

#### Typography Scale

```typescript
// Typography utility classes
const typography = {
  h1: "scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl",
  h2: "scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight first:mt-0",
  h3: "scroll-m-20 text-2xl font-semibold tracking-tight",
  h4: "scroll-m-20 text-xl font-semibold tracking-tight",
  p: "leading-7 [&:not(:first-child)]:mt-6",
  lead: "text-xl text-muted-foreground",
  large: "text-lg font-semibold",
  small: "text-sm font-medium leading-none",
  muted: "text-sm text-muted-foreground",
};
```

### Component Architecture

#### Base Component Structure

```typescript
// Example: Button component
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = "default", size = "default", asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
```

#### Variant System

```typescript
// Using class-variance-authority for component variants
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);
```

### Custom Components

#### Medical Card Component

```typescript
interface MedicalCardProps {
  title: string;
  subtitle?: string;
  status?: 'normal' | 'warning' | 'critical';
  children: React.ReactNode;
  actions?: React.ReactNode;
}

export const MedicalCard: React.FC<MedicalCardProps> = ({
  title,
  subtitle,
  status = 'normal',
  children,
  actions
}) => {
  const statusColors = {
    normal: 'border-green-200 bg-green-50',
    warning: 'border-yellow-200 bg-yellow-50',
    critical: 'border-red-200 bg-red-50'
  };

  return (
    <Card className={cn("transition-all duration-200", statusColors[status])}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">{title}</CardTitle>
            {subtitle && (
              <CardDescription className="mt-1">{subtitle}</CardDescription>
            )}
          </div>
          {actions && <div className="flex gap-2">{actions}</div>}
        </div>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );
};
```

#### Diagnosis Status Badge

```typescript
interface DiagnosisStatusProps {
  status: 'pending' | 'in-progress' | 'completed' | 'requires-attention';
  size?: 'sm' | 'md' | 'lg';
}

export const DiagnosisStatus: React.FC<DiagnosisStatusProps> = ({ 
  status, 
  size = 'md' 
}) => {
  const variants = {
    pending: 'bg-gray-100 text-gray-800 border-gray-200',
    'in-progress': 'bg-blue-100 text-blue-800 border-blue-200',
    completed: 'bg-green-100 text-green-800 border-green-200',
    'requires-attention': 'bg-red-100 text-red-800 border-red-200'
  };

  const sizes = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  return (
    <Badge 
      className={cn(
        'border font-medium',
        variants[status],
        sizes[size]
      )}
    >
      {status.replace('-', ' ').toUpperCase()}
    </Badge>
  );
};
```

### Layout Components

#### Dashboard Layout

```typescript
interface DashboardLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  sidebar,
  header
}) => {
  return (
    <div className="min-h-screen bg-background">
      {header && (
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          {header}
        </header>
      )}
      
      <div className="flex">
        {sidebar && (
          <aside className="w-64 border-r bg-muted/10">
            <div className="sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto">
              {sidebar}
            </div>
          </aside>
        )}
        
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
};
```

### Responsive Design

#### Breakpoint System

```typescript
// Tailwind breakpoints
const breakpoints = {
  sm: '640px',   // Small devices
  md: '768px',   // Medium devices
  lg: '1024px',  // Large devices
  xl: '1280px',  // Extra large devices
  '2xl': '1536px' // 2X large devices
};

// Usage in components
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* Responsive grid */}
</div>
```

#### Mobile-First Approach

```typescript
// Mobile-first responsive utilities
const ResponsiveContainer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div className="
      px-4 sm:px-6 lg:px-8
      py-4 sm:py-6 lg:py-8
      max-w-7xl mx-auto
    ">
      {children}
    </div>
  );
};
```

### Animation and Transitions

#### Custom Animations

```css
/* Custom keyframes in globals.css */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes pulse-medical {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Utility classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse-medical {
  animation: pulse-medical 2s infinite;
}
```

#### Transition Utilities

```typescript
// Smooth transitions for interactive elements
const transitionClasses = {
  default: "transition-all duration-200 ease-in-out",
  fast: "transition-all duration-150 ease-in-out",
  slow: "transition-all duration-300 ease-in-out",
  colors: "transition-colors duration-200 ease-in-out",
  transform: "transition-transform duration-200 ease-in-out"
};
```

### Dark Mode Implementation

#### Theme Toggle Component

```typescript
export const ThemeToggle: React.FC = () => {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');

  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark';
    if (savedTheme) {
      setTheme(savedTheme);
      document.documentElement.setAttribute('data-theme', savedTheme);
    }
  }, []);

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
    document.documentElement.setAttribute('data-theme', newTheme);
  };

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="h-9 w-9"
    >
      {theme === 'light' ? (
        <Moon className="h-4 w-4" />
      ) : (
        <Sun className="h-4 w-4" />
      )}
      <span className="sr-only">Toggle theme</span>
    </Button>
  );
};
```

### Accessibility Features

#### ARIA Labels and Roles

```typescript
// Accessible form components
export const AccessibleInput: React.FC<InputProps & {
  label: string;
  error?: string;
  required?: boolean;
}> = ({ label, error, required, ...props }) => {
  const id = useId();
  const errorId = `${id}-error`;

  return (
    <div className="space-y-2">
      <Label htmlFor={id} className="text-sm font-medium">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      
      <Input
        id={id}
        aria-describedby={error ? errorId : undefined}
        aria-invalid={!!error}
        {...props}
      />
      
      {error && (
        <p id={errorId} className="text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};
```

#### Keyboard Navigation

```typescript
// Custom hook for keyboard navigation
export const useKeyboardNavigation = (items: string[], onSelect: (item: string) => void) => {
  const [selectedIndex, setSelectedIndex] = useState(0);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedIndex(prev => (prev + 1) % items.length);
        break;
      case 'ArrowUp':
        event.preventDefault();
        setSelectedIndex(prev => (prev - 1 + items.length) % items.length);
        break;
      case 'Enter':
        event.preventDefault();
        onSelect(items[selectedIndex]);
        break;
      case 'Escape':
        event.preventDefault();
        setSelectedIndex(0);
        break;
    }
  }, [items, selectedIndex, onSelect]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  return { selectedIndex, setSelectedIndex };
};
```

### Performance Optimization

#### Code Splitting

```typescript
// Lazy loading for route components
const Dashboard = lazy(() => import('./pages/Dashboard'));
const PatientManagement = lazy(() => import('./pages/PatientManagement'));
const DiagnosisInterface = lazy(() => import('./components/DiagnosisInterface'));

// Usage in router
<Route 
  path="/dashboard" 
  element={
    <Suspense fallback={<LoadingSpinner />}>
      <Dashboard />
    </Suspense>
  } 
/>
```

#### Image Optimization

```typescript
// Optimized image component
interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  priority = false
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {!isLoaded && !error && (
        <div className="absolute inset-0 bg-muted animate-pulse" />
      )}
      
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        loading={priority ? "eager" : "lazy"}
        onLoad={() => setIsLoaded(true)}
        onError={() => setError(true)}
        className={cn(
          "transition-opacity duration-300",
          isLoaded ? "opacity-100" : "opacity-0",
          error && "hidden"
        )}
      />
      
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted">
          <ImageIcon className="h-8 w-8 text-muted-foreground" />
        </div>
      )}
    </div>
  );
};
```

### Customization Guidelines

#### Adding New Components

1. **Create component file** in appropriate directory
2. **Follow naming conventions**: PascalCase for components
3. **Use TypeScript interfaces** for props
4. **Implement accessibility features**
5. **Add to component index** for easy imports

#### Modifying Existing Components

1. **Check for breaking changes** in props
2. **Update TypeScript interfaces**
3. **Test across different screen sizes**
4. **Verify accessibility compliance**
5. **Update documentation**

#### Theme Customization

```typescript
// Extend the default theme
const customTheme = {
  ...defaultTheme,
  colors: {
    ...defaultTheme.colors,
    brand: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      500: '#0ea5e9',
      900: '#0c4a6e',
    }
  },
  fontFamily: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    mono: ['JetBrains Mono', 'monospace'],
  }
};
```

This comprehensive UI Enhancement Guide provides the foundation for customizing and extending MEDORA's user interface while maintaining consistency, accessibility, and performance standards.

---

## 🚀 Deployment

### Production Deployment Options

#### Option 1: Traditional VPS/Server Deployment

##### Prerequisites

- Ubuntu 20.04+ or CentOS 8+ server
- Node.js 18+ installed
- MongoDB 6+ installed
- Nginx for reverse proxy
- SSL certificate (Let's Encrypt recommended)
- Domain name configured

##### Step-by-Step Deployment

**1. Server Setup**

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Install Nginx
sudo apt install nginx -y
sudo systemctl start nginx
sudo systemctl enable nginx

# Install PM2 for process management
sudo npm install -g pm2
```

**2. Application Deployment**

```bash
# Clone repository
git clone <your-repository-url> /var/www/medora
cd /var/www/medora

# Install dependencies
npm install
cd backend && npm install && cd ..

# Build frontend
npm run build

# Set up environment variables
cp backend/.env.example backend/.env
# Edit backend/.env with production values

# Start application with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

**3. Nginx Configuration**

Create `/etc/nginx/sites-available/medora`:

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Frontend (React app)
    location / {
        root /var/www/medora/dist;
        index index.html;
        try_files $uri $uri/ /index.html;

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Backend API
    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # Increase timeout for AI operations
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Socket.io
    location /socket.io/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Health check
    location /health {
        proxy_pass http://localhost:5000;
        access_log off;
    }
}
```

**4. SSL Certificate Setup**

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

**5. PM2 Ecosystem Configuration**

Create `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [
    {
      name: 'medora-backend',
      script: 'backend/simple-server.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 5000
      },
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024'
    }
  ]
};
```

#### Option 2: Docker Deployment

**1. Dockerfile for Backend**

Create `backend/Dockerfile`:

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S medora -u 1001

# Change ownership
RUN chown -R medora:nodejs /app
USER medora

EXPOSE 5000

CMD ["node", "simple-server.js"]
```

**2. Dockerfile for Frontend**

Create `Dockerfile`:

```dockerfile
# Build stage
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built app
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

**3. Docker Compose Configuration**

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  mongodb:
    image: mongo:6.0
    container_name: medora-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: medora
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    ports:
      - "27017:27017"
    networks:
      - medora-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medora-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      MONGODB_URI: mongodb://mongodb:27017/medora
      JWT_SECRET: ${JWT_SECRET}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
    depends_on:
      - mongodb
    ports:
      - "5000:5000"
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    networks:
      - medora-network

  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: medora-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - medora-network

  redis:
    image: redis:7-alpine
    container_name: medora-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - medora-network

volumes:
  mongodb_data:
  redis_data:

networks:
  medora-network:
    driver: bridge
```

**4. Deploy with Docker Compose**

```bash
# Create environment file
cp .env.example .env
# Edit .env with production values

# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f

# Scale backend service
docker-compose up -d --scale backend=3
```

#### Option 3: Cloud Platform Deployment

##### Heroku Deployment

**1. Prepare for Heroku**

Create `Procfile`:

```
web: node backend/simple-server.js
```

Create `package.json` scripts:

```json
{
  "scripts": {
    "start": "node backend/simple-server.js",
    "heroku-postbuild": "npm run build"
  }
}
```

**2. Deploy to Heroku**

```bash
# Install Heroku CLI
npm install -g heroku

# Login to Heroku
heroku login

# Create Heroku app
heroku create medora-app

# Add MongoDB addon
heroku addons:create mongolab:sandbox

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set JWT_SECRET=your-jwt-secret
heroku config:set OPENAI_API_KEY=your-openai-key

# Deploy
git push heroku main

# Open app
heroku open
```

##### AWS Deployment with Elastic Beanstalk

**1. Prepare Application**

Create `.ebextensions/01_nginx.config`:

```yaml
files:
  "/etc/nginx/conf.d/proxy.conf":
    mode: "000644"
    owner: root
    group: root
    content: |
      upstream nodejs {
        server 127.0.0.1:8081;
        keepalive 256;
      }

      server {
        listen 8080;

        if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})T(\d{2})") {
          set $year $1;
          set $month $2;
          set $day $3;
          set $hour $4;
        }
        access_log /var/log/nginx/healthd/application.log.$year-$month-$day-$hour healthd;
        access_log /var/log/nginx/access.log main;

        location / {
          proxy_pass http://nodejs;
          proxy_set_header Connection "";
          proxy_http_version 1.1;
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        gzip on;
        gzip_comp_level 4;
        gzip_types text/html text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;
      }
```

**2. Deploy to AWS**

```bash
# Install EB CLI
pip install awsebcli

# Initialize EB application
eb init medora-app

# Create environment
eb create production

# Deploy
eb deploy

# Open application
eb open
```

### Environment-Specific Configurations

#### Development Environment

```bash
# .env.development
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:1200
MONGODB_URI=mongodb://localhost:27017/medora-dev
JWT_SECRET=dev-jwt-secret
OPENAI_API_KEY=your-dev-openai-key
LOG_LEVEL=debug
ENABLE_REQUEST_LOGGING=true
```

#### Staging Environment

```bash
# .env.staging
NODE_ENV=staging
PORT=5000
FRONTEND_URL=https://staging.yourdomain.com
MONGODB_URI=mongodb://staging-db:27017/medora
JWT_SECRET=staging-jwt-secret
OPENAI_API_KEY=your-staging-openai-key
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true
```

#### Production Environment

```bash
# .env.production
NODE_ENV=production
PORT=5000
FRONTEND_URL=https://yourdomain.com
MONGODB_URI=mongodb://prod-db:27017/medora
JWT_SECRET=super-secure-production-jwt-secret
OPENAI_API_KEY=your-production-openai-key
LOG_LEVEL=warn
ENABLE_REQUEST_LOGGING=false
```

### Monitoring and Logging

#### Application Monitoring

**1. PM2 Monitoring**

```bash
# Monitor processes
pm2 monit

# View logs
pm2 logs

# Restart application
pm2 restart all

# Reload application (zero downtime)
pm2 reload all
```

**2. Log Management**

Create `backend/config/winston.js`:

```javascript
const winston = require('winston');
const path = require('path');

const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'medora-backend' },
  transports: [
    new winston.transports.File({
      filename: path.join(__dirname, '../logs/error.log'),
      level: 'error'
    }),
    new winston.transports.File({
      filename: path.join(__dirname, '../logs/combined.log')
    }),
  ],
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

module.exports = logger;
```

**3. Health Checks**

```javascript
// Health check endpoint
app.get('/health', (req, res) => {
  const healthcheck = {
    uptime: process.uptime(),
    message: 'OK',
    timestamp: Date.now(),
    checks: {
      database: 'OK',
      memory: process.memoryUsage(),
      cpu: process.cpuUsage()
    }
  };

  try {
    res.status(200).send(healthcheck);
  } catch (error) {
    healthcheck.message = error;
    res.status(503).send();
  }
});
```

### Performance Optimization

#### Frontend Optimization

**1. Build Optimization**

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          utils: ['date-fns', 'clsx', 'tailwind-merge']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  }
});
```

**2. Code Splitting**

```typescript
// Lazy load components
const Dashboard = lazy(() => import('./pages/Dashboard'));
const PatientManagement = lazy(() => import('./pages/PatientManagement'));

// Route-based code splitting
<Route
  path="/dashboard"
  element={
    <Suspense fallback={<LoadingSpinner />}>
      <Dashboard />
    </Suspense>
  }
/>
```

#### Backend Optimization

**1. Database Indexing**

```javascript
// Create indexes for better performance
db.users.createIndex({ email: 1 }, { unique: true });
db.patients.createIndex({ userId: 1 });
db.diagnoses.createIndex({ patientId: 1, createdAt: -1 });
db.payments.createIndex({ email: 1, createdAt: -1 });
```

**2. Caching Strategy**

```javascript
const redis = require('redis');
const client = redis.createClient(process.env.REDIS_URL);

// Cache middleware
const cache = (duration = 300) => {
  return async (req, res, next) => {
    const key = req.originalUrl;
    const cached = await client.get(key);

    if (cached) {
      return res.json(JSON.parse(cached));
    }

    res.sendResponse = res.json;
    res.json = (body) => {
      client.setex(key, duration, JSON.stringify(body));
      res.sendResponse(body);
    };

    next();
  };
};

// Usage
app.get('/api/patients', cache(600), getPatients);
```

### Security Hardening

#### SSL/TLS Configuration

```nginx
# Strong SSL configuration
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;

# HSTS
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

#### Security Headers

```javascript
// Helmet configuration
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.openai.com"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

#### Rate Limiting

```javascript
const rateLimit = require('express-rate-limit');

// General rate limiting
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});

// Strict rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 5,
  message: 'Too many authentication attempts'
});

app.use('/api/', generalLimiter);
app.use('/api/auth', authLimiter);
```

### Backup and Recovery

#### Database Backup

```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/medora"
DB_NAME="medora"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup database
mongodump --db $DB_NAME --out $BACKUP_DIR/mongodb_$DATE

# Compress backup
tar -czf $BACKUP_DIR/mongodb_$DATE.tar.gz -C $BACKUP_DIR mongodb_$DATE

# Remove uncompressed backup
rm -rf $BACKUP_DIR/mongodb_$DATE

# Keep only last 7 days of backups
find $BACKUP_DIR -name "mongodb_*.tar.gz" -mtime +7 -delete

echo "Backup completed: mongodb_$DATE.tar.gz"
```

#### Application Backup

```bash
#!/bin/bash
# app-backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/medora"
APP_DIR="/var/www/medora"

# Backup application files
tar -czf $BACKUP_DIR/app_$DATE.tar.gz -C $APP_DIR \
  --exclude=node_modules \
  --exclude=.git \
  --exclude=logs \
  --exclude=uploads \
  .

# Backup uploads separately
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz -C $APP_DIR uploads

echo "Application backup completed: app_$DATE.tar.gz"
```

#### Automated Backup with Cron

```bash
# Add to crontab (crontab -e)

# Daily database backup at 2 AM
0 2 * * * /var/scripts/backup.sh

# Weekly application backup on Sunday at 3 AM
0 3 * * 0 /var/scripts/app-backup.sh

# Monthly cleanup of old backups
0 4 1 * * find /var/backups/medora -name "*.tar.gz" -mtime +30 -delete
```

This deployment section provides comprehensive guidance for deploying MEDORA in various environments, from traditional servers to modern cloud platforms, with proper security, monitoring, and backup strategies.

---

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Frontend Issues

##### Issue 1: "Module not found" errors

**Symptoms:**
```
Error: Cannot resolve module '@/components/ui/button'
```

**Solutions:**
1. Check TypeScript path configuration in `tsconfig.json`
2. Verify Vite alias configuration in `vite.config.ts`
3. Ensure the file exists at the specified path

```typescript
// vite.config.ts
resolve: {
  alias: {
    "@": path.resolve(__dirname, "./src"),
  },
}
```

##### Issue 2: CORS errors in development

**Symptoms:**
```
Access to fetch at 'http://localhost:5000/api/auth/login' from origin 'http://localhost:1200' has been blocked by CORS policy
```

**Solutions:**
1. Check backend CORS configuration
2. Ensure frontend URL is in allowed origins
3. Verify credentials are included in requests

```javascript
// Backend: Add frontend URL to CORS
const allowedOrigins = [
  'http://localhost:1200',  // Add this
  // ... other origins
];

// Frontend: Include credentials
fetch('/api/endpoint', {
  credentials: 'include'
});
```

##### Issue 3: Build failures

**Symptoms:**
```
Build failed with errors
Type error: Property 'xyz' does not exist on type 'ABC'
```

**Solutions:**
1. Fix TypeScript type errors
2. Update type definitions
3. Check for missing dependencies

```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check for type errors
npm run lint
```

#### Backend Issues

##### Issue 1: MongoDB connection failures

**Symptoms:**
```
MongooseError: Operation `users.findOne()` buffering timed out after 10000ms
```

**Solutions:**
1. Verify MongoDB is running
2. Check connection string format
3. Ensure network connectivity

```bash
# Check MongoDB status
sudo systemctl status mongod

# Test connection
mongo --eval "db.adminCommand('ismaster')"

# Check connection string
MONGODB_URI=mongodb://localhost:27017/medora
```

##### Issue 2: JWT token errors

**Symptoms:**
```
JsonWebTokenError: invalid signature
```

**Solutions:**
1. Verify JWT_SECRET is consistent
2. Check token expiration
3. Ensure proper token format

```javascript
// Verify JWT secret is set
if (!process.env.JWT_SECRET) {
  throw new Error('JWT_SECRET is required');
}

// Check token format
const token = req.headers.authorization?.split(' ')[1];
if (!token) {
  return res.status(401).json({ error: 'No token provided' });
}
```

##### Issue 3: Payment gateway errors

**Symptoms:**
```
PaystackError: Invalid authorization header
```

**Solutions:**
1. Verify API keys are correct
2. Check environment variables
3. Ensure proper request format

```javascript
// Verify API keys
if (!process.env.PAYSTACK_SECRET_KEY) {
  throw new Error('PAYSTACK_SECRET_KEY is required');
}

// Check request headers
headers: {
  'Authorization': `Bearer ${process.env.PAYSTACK_SECRET_KEY}`,
  'Content-Type': 'application/json'
}
```

#### Database Issues

##### Issue 1: Slow query performance

**Symptoms:**
- API responses taking > 5 seconds
- High CPU usage on database server

**Solutions:**
1. Add appropriate indexes
2. Optimize query patterns
3. Use aggregation pipelines

```javascript
// Add indexes for common queries
db.users.createIndex({ email: 1 });
db.patients.createIndex({ userId: 1, createdAt: -1 });
db.diagnoses.createIndex({ patientId: 1, status: 1 });

// Use projection to limit returned fields
const users = await User.find({}, 'firstName lastName email role');

// Use aggregation for complex queries
const stats = await User.aggregate([
  { $match: { role: 'patient' } },
  { $group: { _id: '$status', count: { $sum: 1 } } }
]);
```

##### Issue 2: Database connection pool exhaustion

**Symptoms:**
```
MongoError: connection pool exhausted
```

**Solutions:**
1. Increase connection pool size
2. Implement connection pooling
3. Close unused connections

```javascript
// Mongoose connection options
mongoose.connect(MONGODB_URI, {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  bufferCommands: false,
  bufferMaxEntries: 0
});
```

#### Authentication Issues

##### Issue 1: OAuth callback failures

**Symptoms:**
- OAuth redirects to error page
- "Invalid redirect URI" errors

**Solutions:**
1. Verify callback URLs in OAuth provider settings
2. Check environment variables
3. Ensure HTTPS in production

```javascript
// Google OAuth configuration
passport.use(new GoogleStrategy({
  clientID: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  callbackURL: `${process.env.OAUTH_CALLBACK_BASE_URL}/google/callback`
}, callback));
```

##### Issue 2: Session persistence issues

**Symptoms:**
- Users logged out after page refresh
- Session data not persisting

**Solutions:**
1. Configure session storage properly
2. Check cookie settings
3. Verify session secret

```javascript
// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  store: new MongoStore({ mongoUrl: process.env.MONGODB_URI }),
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));
```

### Debugging Tools and Techniques

#### Frontend Debugging

**1. React Developer Tools**

```bash
# Install React DevTools browser extension
# Available for Chrome, Firefox, and Edge
```

**2. Network Debugging**

```javascript
// Add request/response interceptors
const apiService = {
  async request(url, options) {
    console.log('🚀 Request:', { url, options });

    try {
      const response = await fetch(url, options);
      console.log('✅ Response:', {
        status: response.status,
        url: response.url
      });
      return response;
    } catch (error) {
      console.error('❌ Request failed:', error);
      throw error;
    }
  }
};
```

**3. State Debugging**

```typescript
// Custom hook for debugging state changes
const useDebugState = <T>(state: T, name: string) => {
  useEffect(() => {
    console.log(`${name} state changed:`, state);
  }, [state, name]);
};

// Usage
const [user, setUser] = useState(null);
useDebugState(user, 'user');
```

#### Backend Debugging

**1. Request Logging**

```javascript
// Detailed request logging middleware
const requestLogger = (req, res, next) => {
  const start = Date.now();

  console.log(`📥 ${req.method} ${req.path}`, {
    headers: req.headers,
    body: req.body,
    query: req.query,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`📤 ${req.method} ${req.path} - ${res.statusCode} (${duration}ms)`);
  });

  next();
};

app.use(requestLogger);
```

**2. Database Query Debugging**

```javascript
// Enable Mongoose debug mode
mongoose.set('debug', process.env.NODE_ENV === 'development');

// Custom query logging
const originalExec = mongoose.Query.prototype.exec;
mongoose.Query.prototype.exec = function() {
  console.log('🔍 Query:', this.getQuery());
  console.log('📊 Collection:', this.mongooseCollection.name);
  return originalExec.apply(this, arguments);
};
```

**3. Error Tracking**

```javascript
// Comprehensive error handler
const errorHandler = (err, req, res, next) => {
  console.error('❌ Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    body: req.body,
    user: req.user?.id,
    timestamp: new Date().toISOString()
  });

  // Send appropriate response
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: err.errors
    });
  }

  if (err.name === 'CastError') {
    return res.status(400).json({
      success: false,
      error: 'Invalid ID format'
    });
  }

  res.status(500).json({
    success: false,
    error: process.env.NODE_ENV === 'production'
      ? 'Internal server error'
      : err.message
  });
};
```

### Performance Debugging

#### Frontend Performance

**1. Bundle Analysis**

```bash
# Analyze bundle size
npm run build
npx vite-bundle-analyzer dist

# Check for large dependencies
npm ls --depth=0 --long
```

**2. React Performance Profiling**

```typescript
// Wrap components with Profiler
import { Profiler } from 'react';

const onRenderCallback = (id, phase, actualDuration) => {
  console.log('⏱️ Component render:', {
    id,
    phase,
    actualDuration
  });
};

<Profiler id="Dashboard" onRender={onRenderCallback}>
  <Dashboard />
</Profiler>
```

#### Backend Performance

**1. API Response Time Monitoring**

```javascript
// Response time middleware
const responseTime = (req, res, next) => {
  const start = process.hrtime();

  res.on('finish', () => {
    const [seconds, nanoseconds] = process.hrtime(start);
    const duration = seconds * 1000 + nanoseconds / 1000000;

    if (duration > 1000) {
      console.warn(`⚠️ Slow response: ${req.method} ${req.path} - ${duration.toFixed(2)}ms`);
    }
  });

  next();
};
```

**2. Memory Usage Monitoring**

```javascript
// Memory monitoring
setInterval(() => {
  const usage = process.memoryUsage();
  console.log('💾 Memory usage:', {
    rss: `${Math.round(usage.rss / 1024 / 1024)} MB`,
    heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024)} MB`,
    heapUsed: `${Math.round(usage.heapUsed / 1024 / 1024)} MB`,
    external: `${Math.round(usage.external / 1024 / 1024)} MB`
  });
}, 30000); // Every 30 seconds
```

### Log Analysis

#### Centralized Logging

```javascript
// Winston configuration for structured logging
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'medora-backend' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Usage
logger.info('User logged in', { userId: user.id, ip: req.ip });
logger.error('Database connection failed', { error: err.message });
```

#### Log Monitoring Commands

```bash
# Monitor logs in real-time
tail -f logs/combined.log

# Search for specific errors
grep "ERROR" logs/combined.log | tail -20

# Monitor API response times
grep "Response time" logs/combined.log | awk '{print $NF}' | sort -n

# Count error types
grep "ERROR" logs/combined.log | awk '{print $4}' | sort | uniq -c
```

---

## 🔒 Security Considerations

### Authentication Security

#### JWT Security Best Practices

**1. Secure JWT Configuration**

```javascript
// Strong JWT configuration
const jwtOptions = {
  expiresIn: '15m', // Short expiration for access tokens
  issuer: 'medora-api',
  audience: 'medora-client',
  algorithm: 'HS256'
};

// Refresh token configuration
const refreshTokenOptions = {
  expiresIn: '7d',
  issuer: 'medora-api',
  audience: 'medora-client'
};

// Generate tokens
const generateTokens = (user) => {
  const accessToken = jwt.sign(
    { sub: user.id, role: user.role },
    process.env.JWT_SECRET,
    jwtOptions
  );

  const refreshToken = jwt.sign(
    { sub: user.id, type: 'refresh' },
    process.env.JWT_REFRESH_SECRET,
    refreshTokenOptions
  );

  return { accessToken, refreshToken };
};
```

**2. Token Refresh Implementation**

```javascript
// Token refresh endpoint
app.post('/api/auth/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({ error: 'Refresh token required' });
    }

    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);

    if (decoded.type !== 'refresh') {
      return res.status(401).json({ error: 'Invalid token type' });
    }

    const user = await User.findById(decoded.sub);
    if (!user || !user.isActive) {
      return res.status(401).json({ error: 'User not found or inactive' });
    }

    const tokens = generateTokens(user);
    res.json({ success: true, ...tokens });

  } catch (error) {
    res.status(401).json({ error: 'Invalid refresh token' });
  }
});
```

#### Password Security

**1. Strong Password Policies**

```javascript
// Password validation
const passwordSchema = {
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  maxLength: 128
};

const validatePassword = (password) => {
  const errors = [];

  if (password.length < passwordSchema.minLength) {
    errors.push(`Password must be at least ${passwordSchema.minLength} characters`);
  }

  if (passwordSchema.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (passwordSchema.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (passwordSchema.requireNumbers && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (passwordSchema.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return errors;
};
```

**2. Secure Password Hashing**

```javascript
const bcrypt = require('bcryptjs');

// Hash password with high cost factor
const hashPassword = async (password) => {
  const saltRounds = 12; // High cost factor for security
  return await bcrypt.hash(password, saltRounds);
};

// Verify password
const verifyPassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};

// Password change with verification
const changePassword = async (userId, currentPassword, newPassword) => {
  const user = await User.findById(userId).select('+password');

  if (!await verifyPassword(currentPassword, user.password)) {
    throw new Error('Current password is incorrect');
  }

  const passwordErrors = validatePassword(newPassword);
  if (passwordErrors.length > 0) {
    throw new Error(passwordErrors.join(', '));
  }

  user.password = await hashPassword(newPassword);
  user.passwordChangedAt = new Date();
  await user.save();
};
```

### Data Protection

#### Input Validation and Sanitization

**1. Request Validation Middleware**

```javascript
const { body, validationResult } = require('express-validator');

// Validation rules
const userValidationRules = () => {
  return [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Valid email is required'),
    body('firstName')
      .trim()
      .isLength({ min: 1, max: 50 })
      .matches(/^[a-zA-Z\s]+$/)
      .withMessage('First name must contain only letters'),
    body('lastName')
      .trim()
      .isLength({ min: 1, max: 50 })
      .matches(/^[a-zA-Z\s]+$/)
      .withMessage('Last name must contain only letters'),
    body('phone')
      .optional()
      .isMobilePhone()
      .withMessage('Valid phone number is required')
  ];
};

// Validation middleware
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  next();
};

// Usage
app.post('/api/users', userValidationRules(), validate, createUser);
```

**2. SQL Injection Prevention**

```javascript
// Use parameterized queries with Mongoose
const getUserByEmail = async (email) => {
  // Safe - uses parameterized query
  return await User.findOne({ email: email });
};

// Avoid string concatenation
const unsafeQuery = (email) => {
  // NEVER DO THIS - vulnerable to injection
  return User.find({ $where: `this.email === '${email}'` });
};
```

**3. XSS Prevention**

```javascript
const xss = require('xss');

// XSS sanitization middleware
const sanitizeInput = (req, res, next) => {
  const sanitizeObject = (obj) => {
    for (let key in obj) {
      if (typeof obj[key] === 'string') {
        obj[key] = xss(obj[key]);
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        sanitizeObject(obj[key]);
      }
    }
  };

  if (req.body) sanitizeObject(req.body);
  if (req.query) sanitizeObject(req.query);
  if (req.params) sanitizeObject(req.params);

  next();
};

app.use(sanitizeInput);
```

#### Data Encryption

**1. Sensitive Data Encryption**

```javascript
const crypto = require('crypto');

class EncryptionService {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.secretKey = crypto.scryptSync(process.env.ENCRYPTION_KEY, 'salt', 32);
  }

  encrypt(text) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.secretKey, iv);

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }

  decrypt(encryptedData) {
    const { encrypted, iv, authTag } = encryptedData;
    const decipher = crypto.createDecipher(
      this.algorithm,
      this.secretKey,
      Buffer.from(iv, 'hex')
    );

    decipher.setAuthTag(Buffer.from(authTag, 'hex'));

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }
}

// Usage for sensitive medical data
const encryptionService = new EncryptionService();

const patientSchema = new mongoose.Schema({
  // ... other fields
  medicalHistory: {
    type: String,
    set: function(value) {
      return encryptionService.encrypt(value);
    },
    get: function(value) {
      return encryptionService.decrypt(value);
    }
  }
});
```

### API Security

#### Rate Limiting

**1. Advanced Rate Limiting**

```javascript
const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const redis = require('redis');

const redisClient = redis.createClient(process.env.REDIS_URL);

// Different rate limits for different endpoints
const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    store: new RedisStore({
      client: redisClient,
      prefix: 'rl:'
    }),
    windowMs,
    max,
    message: { error: message },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      // Use user ID if authenticated, otherwise IP
      return req.user?.id || req.ip;
    }
  });
};

// Apply different limits
app.use('/api/auth/login', createRateLimiter(15 * 60 * 1000, 5, 'Too many login attempts'));
app.use('/api/auth/register', createRateLimiter(60 * 60 * 1000, 3, 'Too many registration attempts'));
app.use('/api/ai/chat', createRateLimiter(60 * 1000, 10, 'Too many AI requests'));
app.use('/api/', createRateLimiter(15 * 60 * 1000, 100, 'Too many requests'));
```

**2. API Key Authentication**

```javascript
// API key middleware for external integrations
const apiKeyAuth = async (req, res, next) => {
  const apiKey = req.headers['x-api-key'];

  if (!apiKey) {
    return res.status(401).json({ error: 'API key required' });
  }

  try {
    // Hash the provided key and compare with stored hash
    const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');
    const validKey = await ApiKey.findOne({
      keyHash: hashedKey,
      isActive: true,
      expiresAt: { $gt: new Date() }
    });

    if (!validKey) {
      return res.status(401).json({ error: 'Invalid or expired API key' });
    }

    // Update last used timestamp
    validKey.lastUsedAt = new Date();
    await validKey.save();

    req.apiKey = validKey;
    next();
  } catch (error) {
    res.status(500).json({ error: 'API key validation failed' });
  }
};
```

#### HTTPS and Security Headers

**1. Security Headers Configuration**

```javascript
const helmet = require('helmet');

app.use(helmet({
  // Content Security Policy
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      scriptSrc: ["'self'"],
      connectSrc: [
        "'self'",
        "https://api.openai.com",
        "https://api.paystack.co",
        "https://api.flutterwave.com"
      ],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: []
    }
  },

  // HTTP Strict Transport Security
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },

  // X-Frame-Options
  frameguard: { action: 'deny' },

  // X-Content-Type-Options
  noSniff: true,

  // Referrer Policy
  referrerPolicy: { policy: 'same-origin' },

  // X-XSS-Protection
  xssFilter: true
}));

// Additional security headers
app.use((req, res, next) => {
  res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
  res.setHeader('X-Download-Options', 'noopen');
  res.setHeader('X-DNS-Prefetch-Control', 'off');
  next();
});
```

### HIPAA Compliance

#### Audit Logging

**1. Comprehensive Audit Trail**

```javascript
// Audit log schema
const auditLogSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  action: { type: String, required: true },
  resource: { type: String, required: true },
  resourceId: { type: String },
  details: { type: mongoose.Schema.Types.Mixed },
  ipAddress: { type: String, required: true },
  userAgent: { type: String },
  timestamp: { type: Date, default: Date.now },
  success: { type: Boolean, required: true }
});

const AuditLog = mongoose.model('AuditLog', auditLogSchema);

// Audit logging middleware
const auditLogger = (action, resource) => {
  return async (req, res, next) => {
    const originalSend = res.send;

    res.send = function(data) {
      // Log the action after response
      AuditLog.create({
        userId: req.user?.id,
        action,
        resource,
        resourceId: req.params.id,
        details: {
          method: req.method,
          url: req.originalUrl,
          body: req.body,
          query: req.query
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        success: res.statusCode < 400
      }).catch(console.error);

      originalSend.call(this, data);
    };

    next();
  };
};

// Usage
app.get('/api/patients/:id',
  requireAuth,
  auditLogger('VIEW_PATIENT', 'patient'),
  getPatient
);

app.post('/api/patients',
  requireAuth,
  auditLogger('CREATE_PATIENT', 'patient'),
  createPatient
);
```

**2. Data Access Controls**

```javascript
// Role-based access control
const rbac = {
  admin: ['*'],
  doctor: ['patients:read', 'patients:write', 'diagnoses:read', 'diagnoses:write'],
  nurse: ['patients:read', 'diagnoses:read'],
  patient: ['patients:read:own', 'diagnoses:read:own']
};

const checkPermission = (permission) => {
  return (req, res, next) => {
    const userRole = req.user.role;
    const userPermissions = rbac[userRole] || [];

    // Check for wildcard permission
    if (userPermissions.includes('*')) {
      return next();
    }

    // Check for specific permission
    if (userPermissions.includes(permission)) {
      return next();
    }

    // Check for own resource access
    const ownPermission = permission + ':own';
    if (userPermissions.includes(ownPermission)) {
      // Verify resource ownership
      if (req.params.userId === req.user.id) {
        return next();
      }
    }

    res.status(403).json({ error: 'Insufficient permissions' });
  };
};

// Usage
app.get('/api/patients',
  requireAuth,
  checkPermission('patients:read'),
  getPatients
);
```

#### Data Retention and Deletion

**1. Automated Data Retention**

```javascript
// Data retention policies
const retentionPolicies = {
  auditLogs: 7 * 365, // 7 years
  patientRecords: 10 * 365, // 10 years
  sessionLogs: 90, // 90 days
  temporaryFiles: 30 // 30 days
};

// Cleanup job
const cleanupExpiredData = async () => {
  const now = new Date();

  // Clean up audit logs
  const auditCutoff = new Date(now.getTime() - retentionPolicies.auditLogs * 24 * 60 * 60 * 1000);
  await AuditLog.deleteMany({ timestamp: { $lt: auditCutoff } });

  // Clean up session logs
  const sessionCutoff = new Date(now.getTime() - retentionPolicies.sessionLogs * 24 * 60 * 60 * 1000);
  await SessionLog.deleteMany({ createdAt: { $lt: sessionCutoff } });

  console.log('Data cleanup completed');
};

// Schedule cleanup job (run daily)
setInterval(cleanupExpiredData, 24 * 60 * 60 * 1000);
```

**2. Secure Data Deletion**

```javascript
// Secure deletion with overwriting
const secureDelete = async (model, query) => {
  const documents = await model.find(query);

  for (const doc of documents) {
    // Overwrite sensitive fields with random data
    for (const field in doc.toObject()) {
      if (typeof doc[field] === 'string') {
        doc[field] = crypto.randomBytes(doc[field].length).toString('hex');
      }
    }
    await doc.save();
  }

  // Then delete the documents
  await model.deleteMany(query);
};

// Usage
await secureDelete(Patient, { deletedAt: { $lt: new Date() } });
```

This comprehensive security section covers authentication, data protection, API security, and HIPAA compliance requirements essential for a medical application like MEDORA.

---

## 📚 Additional Resources

### Documentation Links

- **React Documentation**: https://react.dev/
- **TypeScript Handbook**: https://www.typescriptlang.org/docs/
- **TailwindCSS Documentation**: https://tailwindcss.com/docs
- **Radix UI Documentation**: https://www.radix-ui.com/docs
- **Vite Documentation**: https://vitejs.dev/guide/
- **Express.js Documentation**: https://expressjs.com/
- **MongoDB Documentation**: https://docs.mongodb.com/
- **Mongoose Documentation**: https://mongoosejs.com/docs/

### API References

- **OpenAI API**: https://platform.openai.com/docs/api-reference
- **Paystack API**: https://paystack.com/docs/api/
- **Flutterwave API**: https://developer.flutterwave.com/docs
- **Google OAuth**: https://developers.google.com/identity/protocols/oauth2
- **Facebook Login**: https://developers.facebook.com/docs/facebook-login/

### Security Resources

- **OWASP Top 10**: https://owasp.org/www-project-top-ten/
- **HIPAA Compliance Guide**: https://www.hhs.gov/hipaa/for-professionals/security/guidance/index.html
- **JWT Best Practices**: https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/
- **Node.js Security Checklist**: https://blog.risingstack.com/node-js-security-checklist/

### Community and Support

- **GitHub Issues**: Create issues for bugs and feature requests
- **Stack Overflow**: Tag questions with `medora`, `react`, `nodejs`
- **Discord Community**: Join our development community
- **Documentation Wiki**: Contribute to our knowledge base

---

## 🎯 Quick Reference

### Essential Commands

```bash
# Development
npm start                    # Start both frontend and backend
npm run dev                  # Start frontend only
npm run backend             # Start backend only
npm run build               # Build for production

# Database
mongod                      # Start MongoDB
mongo                       # MongoDB shell
mongodump --db medora       # Backup database
mongorestore --db medora    # Restore database

# Process Management
pm2 start ecosystem.config.js  # Start with PM2
pm2 logs                       # View logs
pm2 restart all               # Restart all processes
pm2 stop all                  # Stop all processes

# SSL/HTTPS
certbot --nginx -d yourdomain.com  # Get SSL certificate
certbot renew                      # Renew certificates

# Docker
docker-compose up -d              # Start with Docker
docker-compose logs -f            # View Docker logs
docker-compose down               # Stop Docker services
```

### Environment Variables Quick Reference

```bash
# Required
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb://localhost:27017/medora
JWT_SECRET=your-jwt-secret
FRONTEND_URL=http://localhost:1200

# Optional but Recommended
OPENAI_API_KEY=your-openai-key
PAYSTACK_SECRET_KEY=your-paystack-key
GOOGLE_CLIENT_ID=your-google-client-id
SMTP_USER=<EMAIL>
```

### Default Ports

- **Frontend**: 1200
- **Backend**: 5000
- **MongoDB**: 27017
- **Redis**: 6379

### Default Credentials

- **Admin Email**: <EMAIL>
- **Admin Password**: Americana123456789@

> ⚠️ **Security Warning**: Change default credentials immediately in production!

---

## 📞 Support and Maintenance

### Getting Help

1. **Check this manual** for common issues and solutions
2. **Search existing issues** on GitHub
3. **Create a new issue** with detailed information:
   - Environment details (OS, Node.js version, etc.)
   - Steps to reproduce the problem
   - Error messages and logs
   - Expected vs actual behavior

### Reporting Security Issues

For security-related issues, please:

1. **Do NOT create public issues**
2. **Email security concerns** to: <EMAIL>
3. **Include detailed information** about the vulnerability
4. **Allow time for response** before public disclosure

### Contributing

We welcome contributions! Please:

1. **Fork the repository**
2. **Create a feature branch**
3. **Follow coding standards**
4. **Add tests for new features**
5. **Submit a pull request**

### Maintenance Schedule

- **Security updates**: Applied immediately
- **Bug fixes**: Released weekly
- **Feature updates**: Released monthly
- **Major versions**: Released quarterly

---

## 📄 License and Legal

### Software License

MEDORA is released under the MIT License. See the LICENSE file for details.

### Medical Disclaimer

⚠️ **IMPORTANT MEDICAL DISCLAIMER**

MEDORA is an AI-powered medical assistant tool designed to support healthcare professionals and provide general health information. It is NOT a substitute for professional medical advice, diagnosis, or treatment.

**Key Points:**

- Always seek advice from qualified healthcare professionals
- Never disregard professional medical advice based on AI recommendations
- In case of medical emergencies, contact emergency services immediately
- AI recommendations should be verified by licensed medical professionals
- This software is for informational and educational purposes only

### Privacy and Data Protection

- All patient data is encrypted and stored securely
- HIPAA compliance measures are implemented
- Data retention policies are enforced
- User consent is required for data processing
- Data can be exported or deleted upon request

### Terms of Service

By using MEDORA, you agree to:

- Use the software responsibly and ethically
- Comply with applicable laws and regulations
- Protect patient privacy and confidentiality
- Report security issues responsibly
- Not use the software for illegal activities

---

## 🏁 Conclusion

This Application Administration and Enhancement (AAE) Manual provides comprehensive guidance for setting up, configuring, deploying, and maintaining the MEDORA AI-powered medical assistant platform.

### What You've Learned

✅ **System Architecture**: Understanding of frontend, backend, and database components
✅ **Installation Process**: Complete setup from development to production
✅ **CORS Configuration**: Proper cross-origin resource sharing setup
✅ **Backend Configuration**: Express.js, MongoDB, and API setup
✅ **Frontend Configuration**: React, TypeScript, and Vite configuration
✅ **Database Management**: MongoDB setup, indexing, and optimization
✅ **Environment Management**: Proper configuration for different environments
✅ **UI Enhancement**: Design system, components, and customization
✅ **Deployment Strategies**: Multiple deployment options and best practices
✅ **Troubleshooting**: Common issues and debugging techniques
✅ **Security Implementation**: Authentication, authorization, and data protection

### Next Steps

1. **Set up your development environment** using the installation guide
2. **Configure your environment variables** according to your needs
3. **Customize the UI** to match your branding and requirements
4. **Deploy to your preferred platform** using the deployment guides
5. **Implement security measures** appropriate for your use case
6. **Monitor and maintain** your application using the provided tools

### Best Practices Summary

- **Security First**: Always prioritize security in medical applications
- **Regular Backups**: Implement automated backup strategies
- **Monitor Performance**: Use logging and monitoring tools
- **Keep Updated**: Regularly update dependencies and security patches
- **Document Changes**: Maintain clear documentation of customizations
- **Test Thoroughly**: Implement comprehensive testing strategies
- **Follow Standards**: Adhere to medical data handling regulations

### Support and Community

Remember that this manual is a living document. As MEDORA evolves, so will this guide. Stay connected with the community, contribute improvements, and help make MEDORA better for everyone.

For the latest updates, additional resources, and community support, visit our official documentation and community channels.

---

**Happy coding, and thank you for choosing MEDORA! 🚀**

*Last updated: September 2025*
*Version: 1.0.0*
*Manual Version: AAE-2025.09*
