import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Settings,
  Shield,
  Database,
  Mail,
  Bell,
  Globe,
  Lock,
  Users,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Server,
  Key,
  Eye,
  EyeOff
} from "lucide-react";
import { adminService } from '@/services/adminService';
import { toast } from 'sonner';

interface SystemSettings {
  // General Settings
  siteName: string;
  siteDescription: string;
  timezone: string;
  language: string;
  maintenanceMode: boolean;

  // Security Settings
  sessionTimeout: number;
  maxLoginAttempts: number;
  passwordMinLength: number;
  requireTwoFactor: boolean;
  enableAuditLog: boolean;

  // Email Settings
  smtpHost: string;
  smtpPort: number;
  smtpUser: string;
  smtpPassword: string;
  fromEmail: string;
  fromName: string;

  // AI/ML Settings
  aiConfidenceThreshold: number;
  mlModelPath: string;
  enableAutoRetrain: boolean;
  maxConcurrentRequests: number;

  // Database Settings
  dbBackupFrequency: string;
  dbRetentionDays: number;
  enableQueryLogging: boolean;

  // Notification Settings
  emailNotifications: boolean;
  pushNotifications: boolean;
  slackWebhook: string;
  alertThreshold: number;
}

const GlobalSettings = () => {
  const [settings, setSettings] = useState<SystemSettings>({
    siteName: "MEDORA",
    siteDescription: "AI-Powered Medical Assistant",
    timezone: "UTC",
    language: "en",
    maintenanceMode: false,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    passwordMinLength: 8,
    requireTwoFactor: false,
    enableAuditLog: true,
    smtpHost: "smtp.gmail.com",
    smtpPort: 587,
    smtpUser: "",
    smtpPassword: "",
    fromEmail: "<EMAIL>",
    fromName: "MEDORA System",
    aiConfidenceThreshold: 0.7,
    mlModelPath: "/models",
    enableAutoRetrain: true,
    maxConcurrentRequests: 100,
    dbBackupFrequency: "daily",
    dbRetentionDays: 30,
    enableQueryLogging: false,
    emailNotifications: true,
    pushNotifications: true,
    slackWebhook: "",
    alertThreshold: 80
  });

  useEffect(() => {
    (async () => {
      try {
        const res = await adminService.getSettings();
        if (res.success && res.data) setSettings((prev) => ({ ...prev, ...res.data }));
      } catch (e) {
        console.error('Failed to load settings', e);
        toast.error('Failed to load settings');
      }
    })();
  }, []);

  const [showPassword, setShowPassword] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleSave = async () => {
    setIsSaving(true);
    setSaveStatus('idle');

    try {
      const res = await adminService.saveSettings(settings);
      if (res.success) {
        setSaveStatus('success');
        toast.success('Settings saved');
      } else {
        throw new Error(res.error || 'Failed to save settings');
      }
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (error) {
      setSaveStatus('error');
      toast.error('Error saving settings');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    // Reset to default values
    setSettings({
      siteName: "MEDORA",
      siteDescription: "AI-Powered Medical Assistant",
      timezone: "UTC",
      language: "en",
      maintenanceMode: false,
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      passwordMinLength: 8,
      requireTwoFactor: false,
      enableAuditLog: true,
      smtpHost: "smtp.gmail.com",
      smtpPort: 587,
      smtpUser: "",
      smtpPassword: "",
      fromEmail: "<EMAIL>",
      fromName: "MEDORA System",
      aiConfidenceThreshold: 0.7,
      mlModelPath: "/models",
      enableAutoRetrain: true,
      maxConcurrentRequests: 100,
      dbBackupFrequency: "daily",
      dbRetentionDays: 30,
      enableQueryLogging: false,
      emailNotifications: true,
      pushNotifications: true,
      slackWebhook: "",
      alertThreshold: 80
    });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold gradient-text">Global Settings</h1>
          <p className="text-gray-600">Configure system-wide settings and preferences</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleReset} disabled={isSaving}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Reset
          </Button>
          <Button onClick={handleSave} disabled={isSaving} className="gradient-primary">
            {isSaving ? (
              <>
                <div className="loading-spinner w-4 h-4 mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Save Status */}
      {saveStatus === 'success' && (
        <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg">
          <CheckCircle className="w-4 h-4 text-green-600" />
          <span className="text-green-800">Settings saved successfully!</span>
        </div>
      )}

      {saveStatus === 'error' && (
        <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg">
          <AlertTriangle className="w-4 h-4 text-red-600" />
          <span className="text-red-800">Error saving settings. Please try again.</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* General Settings */}
        <Card className="modern-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="w-5 h-5" />
              <span>General Settings</span>
            </CardTitle>
            <CardDescription>Basic system configuration</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="siteName">Site Name</Label>
              <Input
                id="siteName"
                value={settings.siteName}
                onChange={(e) => setSettings({...settings, siteName: e.target.value})}
                className="modern-input"
              />
            </div>

            <div>
              <Label htmlFor="siteDescription">Site Description</Label>
              <Textarea
                id="siteDescription"
                value={settings.siteDescription}
                onChange={(e) => setSettings({...settings, siteDescription: e.target.value})}
                className="modern-input"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="timezone">Timezone</Label>
                <Select value={settings.timezone} onValueChange={(value) => setSettings({...settings, timezone: value})}>
                  <SelectTrigger className="modern-input">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="UTC">UTC</SelectItem>
                    <SelectItem value="EST">Eastern Time</SelectItem>
                    <SelectItem value="PST">Pacific Time</SelectItem>
                    <SelectItem value="GMT">GMT</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="language">Language</Label>
                <Select value={settings.language} onValueChange={(value) => setSettings({...settings, language: value})}>
                  <SelectTrigger className="modern-input">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                    <SelectItem value="fr">French</SelectItem>
                    <SelectItem value="de">German</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="maintenanceMode">Maintenance Mode</Label>
                <p className="text-sm text-gray-600">Temporarily disable the system</p>
              </div>
              <Switch
                id="maintenanceMode"
                checked={settings.maintenanceMode}
                onCheckedChange={(checked) => setSettings({...settings, maintenanceMode: checked})}
              />
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card className="modern-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="w-5 h-5" />
              <span>Security Settings</span>
            </CardTitle>
            <CardDescription>Authentication and security configuration</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                <Input
                  id="sessionTimeout"
                  type="number"
                  value={settings.sessionTimeout}
                  onChange={(e) => setSettings({...settings, sessionTimeout: parseInt(e.target.value)})}
                  className="modern-input"
                />
              </div>

              <div>
                <Label htmlFor="maxLoginAttempts">Max Login Attempts</Label>
                <Input
                  id="maxLoginAttempts"
                  type="number"
                  value={settings.maxLoginAttempts}
                  onChange={(e) => setSettings({...settings, maxLoginAttempts: parseInt(e.target.value)})}
                  className="modern-input"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
              <Input
                id="passwordMinLength"
                type="number"
                value={settings.passwordMinLength}
                onChange={(e) => setSettings({...settings, passwordMinLength: parseInt(e.target.value)})}
                className="modern-input"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="requireTwoFactor">Require Two-Factor Authentication</Label>
                <p className="text-sm text-gray-600">Enforce 2FA for all users</p>
              </div>
              <Switch
                id="requireTwoFactor"
                checked={settings.requireTwoFactor}
                onCheckedChange={(checked) => setSettings({...settings, requireTwoFactor: checked})}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="enableAuditLog">Enable Audit Logging</Label>
                <p className="text-sm text-gray-600">Log all system activities</p>
              </div>
              <Switch
                id="enableAuditLog"
                checked={settings.enableAuditLog}
                onCheckedChange={(checked) => setSettings({...settings, enableAuditLog: checked})}
              />
            </div>
          </CardContent>
        </Card>

        {/* Email Settings */}
        <Card className="modern-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="w-5 h-5" />
              <span>Email Settings</span>
            </CardTitle>
            <CardDescription>SMTP configuration for notifications</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="smtpHost">SMTP Host</Label>
                <Input
                  id="smtpHost"
                  value={settings.smtpHost}
                  onChange={(e) => setSettings({...settings, smtpHost: e.target.value})}
                  className="modern-input"
                />
              </div>

              <div>
                <Label htmlFor="smtpPort">SMTP Port</Label>
                <Input
                  id="smtpPort"
                  type="number"
                  value={settings.smtpPort}
                  onChange={(e) => setSettings({...settings, smtpPort: parseInt(e.target.value)})}
                  className="modern-input"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="smtpUser">SMTP Username</Label>
              <Input
                id="smtpUser"
                value={settings.smtpUser}
                onChange={(e) => setSettings({...settings, smtpUser: e.target.value})}
                className="modern-input"
              />
            </div>

            <div>
              <Label htmlFor="smtpPassword">SMTP Password</Label>
              <div className="relative">
                <Input
                  id="smtpPassword"
                  type={showPassword ? "text" : "password"}
                  value={settings.smtpPassword}
                  onChange={(e) => setSettings({...settings, smtpPassword: e.target.value})}
                  className="modern-input pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="fromEmail">From Email</Label>
                <Input
                  id="fromEmail"
                  type="email"
                  value={settings.fromEmail}
                  onChange={(e) => setSettings({...settings, fromEmail: e.target.value})}
                  className="modern-input"
                />
              </div>

              <div>
                <Label htmlFor="fromName">From Name</Label>
                <Input
                  id="fromName"
                  value={settings.fromName}
                  onChange={(e) => setSettings({...settings, fromName: e.target.value})}
                  className="modern-input"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* AI/ML Settings */}
        <Card className="modern-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Server className="w-5 h-5" />
              <span>AI/ML Settings</span>
            </CardTitle>
            <CardDescription>Machine learning model configuration</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="aiConfidenceThreshold">AI Confidence Threshold</Label>
              <Input
                id="aiConfidenceThreshold"
                type="number"
                step="0.1"
                min="0"
                max="1"
                value={settings.aiConfidenceThreshold}
                onChange={(e) => setSettings({...settings, aiConfidenceThreshold: parseFloat(e.target.value)})}
                className="modern-input"
              />
              <p className="text-sm text-gray-600">Minimum confidence level for AI predictions (0.0 - 1.0)</p>
            </div>

            <div>
              <Label htmlFor="mlModelPath">ML Model Path</Label>
              <Input
                id="mlModelPath"
                value={settings.mlModelPath}
                onChange={(e) => setSettings({...settings, mlModelPath: e.target.value})}
                className="modern-input"
              />
            </div>

            <div>
              <Label htmlFor="maxConcurrentRequests">Max Concurrent Requests</Label>
              <Input
                id="maxConcurrentRequests"
                type="number"
                value={settings.maxConcurrentRequests}
                onChange={(e) => setSettings({...settings, maxConcurrentRequests: parseInt(e.target.value)})}
                className="modern-input"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="enableAutoRetrain">Enable Auto-Retraining</Label>
                <p className="text-sm text-gray-600">Automatically retrain models with new data</p>
              </div>
              <Switch
                id="enableAutoRetrain"
                checked={settings.enableAutoRetrain}
                onCheckedChange={(checked) => setSettings({...settings, enableAutoRetrain: checked})}
              />
            </div>
          </CardContent>
        </Card>

        {/* Database Settings */}
        <Card className="modern-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="w-5 h-5" />
              <span>Database Settings</span>
            </CardTitle>
            <CardDescription>Database configuration and backup settings</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="dbBackupFrequency">Backup Frequency</Label>
              <Select value={settings.dbBackupFrequency} onValueChange={(value) => setSettings({...settings, dbBackupFrequency: value})}>
                <SelectTrigger className="modern-input">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hourly">Hourly</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="dbRetentionDays">Backup Retention (days)</Label>
              <Input
                id="dbRetentionDays"
                type="number"
                value={settings.dbRetentionDays}
                onChange={(e) => setSettings({...settings, dbRetentionDays: parseInt(e.target.value)})}
                className="modern-input"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="enableQueryLogging">Enable Query Logging</Label>
                <p className="text-sm text-gray-600">Log database queries for debugging</p>
              </div>
              <Switch
                id="enableQueryLogging"
                checked={settings.enableQueryLogging}
                onCheckedChange={(checked) => setSettings({...settings, enableQueryLogging: checked})}
              />
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card className="modern-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bell className="w-5 h-5" />
              <span>Notification Settings</span>
            </CardTitle>
            <CardDescription>Configure notification preferences</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailNotifications">Email Notifications</Label>
                <p className="text-sm text-gray-600">Send notifications via email</p>
              </div>
              <Switch
                id="emailNotifications"
                checked={settings.emailNotifications}
                onCheckedChange={(checked) => setSettings({...settings, emailNotifications: checked})}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="pushNotifications">Push Notifications</Label>
                <p className="text-sm text-gray-600">Send browser push notifications</p>
              </div>
              <Switch
                id="pushNotifications"
                checked={settings.pushNotifications}
                onCheckedChange={(checked) => setSettings({...settings, pushNotifications: checked})}
              />
            </div>

            <div>
              <Label htmlFor="slackWebhook">Slack Webhook URL</Label>
              <Input
                id="slackWebhook"
                value={settings.slackWebhook}
                onChange={(e) => setSettings({...settings, slackWebhook: e.target.value})}
                className="modern-input"
                placeholder="https://hooks.slack.com/services/..."
              />
            </div>

            <div>
              <Label htmlFor="alertThreshold">Alert Threshold (%)</Label>
              <Input
                id="alertThreshold"
                type="number"
                min="0"
                max="100"
                value={settings.alertThreshold}
                onChange={(e) => setSettings({...settings, alertThreshold: parseInt(e.target.value)})}
                className="modern-input"
              />
              <p className="text-sm text-gray-600">System resource usage threshold for alerts</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default GlobalSettings;
