import React, { useState } from 'react';
import { Users, Activity, AlertTriangle, Heart, Plus, Calendar } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PatientManagement from './PatientManagement';

const AdminDashboard = () => {
  const [selectedPatient, setSelectedPatient] = useState(null);

  // Patient-focused dashboard stats
  const stats = {
    totalPatients: 2847,
    activePatients: 2654,
    criticalAlerts: 12,
    recentAdmissions: 34
  };

  const recentPatients = [
    { id: 1, mrn: 'MRN-2024-001', name: '<PERSON>', age: 45, gender: 'Female', status: 'Active', lastVisit: '2024-01-15', condition: 'Hypertension', priority: 'Medium' },
    { id: 2, mrn: 'MRN-2024-002', name: '<PERSON>', age: 62, gender: 'Male', status: 'Critical', lastVisit: '2024-01-15', condition: 'Cardiac Arrest', priority: 'High' },
    { id: 3, mrn: 'MRN-2024-003', name: 'Maria Garcia', age: 34, gender: 'Female', status: 'Active', lastVisit: '2024-01-14', condition: 'Pregnancy Check', priority: 'Low' },
    { id: 4, mrn: 'MRN-2024-004', name: 'James Thompson', age: 28, gender: 'Male', status: 'Discharged', lastVisit: '2024-01-13', condition: 'Fracture', priority: 'Medium' }
  ];

  const criticalAlerts = [
    { id: 1, patientName: 'Robert Williams', mrn: 'MRN-2024-002', alert: 'Abnormal Vital Signs', severity: 'Critical', time: '10 min ago' },
    { id: 2, patientName: 'Linda Davis', mrn: 'MRN-2024-005', alert: 'Medication Allergy Alert', severity: 'High', time: '25 min ago' },
    { id: 3, patientName: 'Michael Brown', mrn: 'MRN-2024-006', alert: 'Lab Results Critical', severity: 'High', time: '1 hour ago' },
    { id: 4, patientName: 'Jennifer Wilson', mrn: 'MRN-2024-007', alert: 'Fall Risk Assessment', severity: 'Medium', time: '2 hours ago' }
  ];

  const recentAdmissions = [
    { id: 1, patientName: 'David Miller', mrn: 'MRN-2024-008', admissionTime: '2024-01-15 14:30', department: 'Emergency', reason: 'Chest Pain', status: 'Admitted' },
    { id: 2, patientName: 'Susan Anderson', mrn: 'MRN-2024-009', admissionTime: '2024-01-15 12:15', department: 'Cardiology', reason: 'Heart Surgery', status: 'Pre-Op' },
    { id: 3, patientName: 'Thomas Lee', mrn: 'MRN-2024-010', admissionTime: '2024-01-15 09:45', department: 'Orthopedics', reason: 'Hip Replacement', status: 'Surgery' },
    { id: 4, patientName: 'Patricia White', mrn: 'MRN-2024-011', admissionTime: '2024-01-14 22:30', department: 'ICU', reason: 'Respiratory Failure', status: 'Critical' }
  ];

  // Helper functions for status badges
  const getStatusBadge = (status) => {
    const variants = {
      'Active': 'default',
      'Critical': 'destructive',
      'Discharged': 'secondary',
      'Admitted': 'default',
      'Pre-Op': 'secondary',
      'Surgery': 'default'
    };
    return <Badge variant={variants[status] || 'secondary'}>{status}</Badge>;
  };

  const getSeverityBadge = (severity) => {
    const variants = {
      'Critical': 'destructive',
      'High': 'destructive',
      'Medium': 'secondary',
      'Low': 'outline'
    };
    return <Badge variant={variants[severity] || 'secondary'}>{severity}</Badge>;
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto">
        <header className="mb-8">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <img src="/lovable-uploads/logo-png.png" alt="MEDORA Logo" className="h-12 w-12" />
              <div>
                <h1 className="text-3xl font-bold" style={{color: '#9ACD32'}}>MEDORA AI Consultation Dashboard</h1>
                <p className="text-muted-foreground">AI-powered medical consultation system with patient management, diagnosis assistance, and medical analytics</p>
              </div>
            </div>
            <Button className="flex items-center gap-2" style={{backgroundColor: '#9ACD32', color: '#000000'}}>
              <Plus className="h-4 w-4" />
              Add Patient
            </Button>
          </div>
        </header>

        {/* Patient Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalPatients.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">+12% from last month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Patients</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activePatients.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">+8% from last month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Critical Alerts</CardTitle>
              <AlertTriangle className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">{stats.criticalAlerts}</div>
              <p className="text-xs text-muted-foreground">Requires immediate attention</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Recent Admissions</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.recentAdmissions}</div>
              <p className="text-xs text-muted-foreground">Last 24 hours</p>
            </CardContent>
          </Card>

        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="patients">Patient Management</TabsTrigger>
            <TabsTrigger value="alerts">Critical Alerts</TabsTrigger>
            <TabsTrigger value="admissions">Recent Admissions</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Patients</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentPatients.slice(0, 4).map((patient) => (
                      <div key={patient.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{patient.name}</p>
                          <p className="text-sm text-muted-foreground">{patient.mrn} • {patient.condition}</p>
                        </div>
                        <div className="text-right">
                          {getStatusBadge(patient.status)}
                          <p className="text-xs text-muted-foreground mt-1">{patient.lastVisit}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Critical Alerts Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {criticalAlerts.slice(0, 4).map((alert) => (
                      <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{alert.patientName}</p>
                          <p className="text-sm text-muted-foreground">{alert.alert}</p>
                        </div>
                        <div className="text-right">
                          {getSeverityBadge(alert.severity)}
                          <p className="text-xs text-muted-foreground mt-1">{alert.time}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="patients" className="space-y-6">
            <PatientManagement />
          </TabsContent>

          <TabsContent value="alerts" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Critical Alerts Management</CardTitle>
                <p className="text-sm text-muted-foreground">Monitor and respond to critical patient alerts</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {criticalAlerts.map((alert) => (
                    <div key={alert.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <AlertTriangle className="h-5 w-5 text-destructive" />
                          <h4 className="font-medium">{alert.patientName}</h4>
                          {getSeverityBadge(alert.severity)}
                        </div>
                        <p className="text-sm text-muted-foreground mb-1">{alert.mrn}</p>
                        <p className="text-sm">{alert.alert}</p>
                        <p className="text-xs text-muted-foreground mt-2">{alert.time}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">View Patient</Button>
                        <Button size="sm" style={{backgroundColor: '#9ACD32', color: '#000000'}}>Respond</Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="admissions" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Admissions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentAdmissions.map((admission) => (
                    <div key={admission.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <Calendar className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">{admission.patient}</p>
                          <p className="text-sm text-muted-foreground">{admission.department} • {admission.date}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={admission.status === 'Admitted' ? 'default' : 'secondary'}>
                          {admission.status}
                        </Badge>
                        <Button size="sm" variant="outline" style={{borderColor: '#9ACD32', color: '#9ACD32'}}>View Details</Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminDashboard;