import { apiClient } from './api';

// Types for Enhanced AI Service
export interface PatientData {
  patientId?: string;
  chiefComplaint: string;
  symptoms: string[];
  age: number;
  gender: 'male' | 'female' | 'other';
  medicalHistory?: string[];
  medications?: string[];
  allergies?: string[];
  vitalSigns?: {
    systolicBP?: number;
    diastolicBP?: number;
    heartRate?: number;
    temperature?: number;
    oxygenSaturation?: number;
    respiratoryRate?: number;
  };
  imaging?: any[];
  labResults?: any[];
}

export interface ConsultationResponse {
  consultation: {
    timestamp: string;
    patientId?: string;
    chiefComplaint: string;
    reasoning: Array<{
      step: string;
      thought: string;
      differentials?: any[];
      urgency?: string;
      immediateActions?: string[];
      recommendations?: any[];
      medications?: any[];
      nonPharmacologic?: any[];
      timeline?: any[];
      monitoring?: any[];
    }>;
    assessment: any;
    plan: any;
    confidence: number;
  };
  realtimeInfo?: any;
  consultationType: string;
  timestamp: string;
  consultedBy: string;
}

export interface MultiAgentConsultationResponse {
  consultation: {
    timestamp: string;
    patientId?: string;
    consultationType: string;
    agentReports: {
      emergency?: any;
      diagnostician?: any;
      internist?: any;
      pharmacologist?: any;
      radiologist?: any;
      pathologist?: any;
    };
    synthesis: any;
    recommendations: any;
    confidence: number;
  };
  agentCount: number;
  timestamp: string;
  consultedBy: string;
}

export interface SpecialistConsultationResponse {
  consultation: {
    agent: string;
    analysis: string;
    confidence: number;
    timestamp: string;
    ragSources?: number;
  };
  specialty: string;
  timestamp: string;
  consultedBy: string;
}

export interface MedicalLiteratureSearchResponse {
  results: {
    texts: Array<{
      content: string;
      metadata: any;
      relevance_score: number;
      type: string;
    }>;
    images: Array<{
      content: string;
      metadata: any;
      relevance_score: number;
      type: string;
    }>;
    drugs: Array<{
      content: string;
      metadata: any;
      relevance_score: number;
      type: string;
    }>;
    labs: Array<{
      content: string;
      metadata: any;
      relevance_score: number;
      type: string;
    }>;
    combined_score: number;
  };
  query: string;
  options: any;
  timestamp: string;
  searchedBy: string;
}

export interface RealTimeMedicalInfoResponse {
  results: {
    query: string;
    timestamp: string;
    sources: {
      pubmed?: {
        articles: Array<{
          pmid: string;
          title: string;
          abstract: string;
          journal: string;
          pubDate: string;
          authors: string[];
          keywords: string[];
          source: string;
          url: string;
        }>;
        total: number;
      };
      fda?: {
        drugs: Array<{
          brandName: string;
          genericName: string;
          manufacturer: string;
          indications: string;
          contraindications: string;
          warnings: string;
          adverseReactions: string;
          dosage: string;
          source: string;
          lastUpdated: string;
        }>;
        total: number;
      };
      who?: {
        updates: Array<{
          title: string;
          url: string;
          date: string;
          summary: string;
          source: string;
          topic: string;
        }>;
        total: number;
      };
      cdc?: {
        updates: Array<{
          title: string;
          url: string;
          date: string;
          summary: string;
          source: string;
          topic: string;
        }>;
        total: number;
      };
      medlineplus?: {
        topics: Array<{
          title: string;
          url: string;
          summary: string;
          source: string;
          category: string;
          lastUpdated: string;
        }>;
        total: number;
      };
    };
  };
  query: string;
  sources: string[];
  timestamp: string;
  searchedBy: string;
}

export interface SystemStatusResponse {
  systemStatus: {
    enhancedAI: {
      initialized: boolean;
      status: string;
    };
    multimodalRAG: {
      initialized: boolean;
      status: string;
    };
    agenticSystem: {
      initialized: boolean;
      agentCount: number;
      status: string;
    };
    scrapingService: {
      cacheStats: {
        size: number;
        timeout: number;
        keys: string[];
      };
      status: string;
    };
  };
  timestamp: string;
  checkedBy: string;
}

class EnhancedAIService {
  /**
   * Conduct comprehensive AI medical consultation with 25 years experience simulation
   */
  async conductConsultation(
    patientData: PatientData,
    consultationType: string = 'comprehensive'
  ): Promise<{ success: boolean; data?: ConsultationResponse; error?: string }> {
    try {
      const response = await apiClient.post<ConsultationResponse>('/enhanced-ai/consultation', {
        patientData,
        consultationType
      });
      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to conduct consultation'
      };
    }
  }

  /**
   * Conduct multi-agent medical consultation
   */
  async conductMultiAgentConsultation(
    patientData: PatientData,
    consultationType: string = 'comprehensive'
  ): Promise<{ success: boolean; data?: MultiAgentConsultationResponse; error?: string }> {
    try {
      const response = await apiClient.post<MultiAgentConsultationResponse>('/enhanced-ai/multi-agent-consultation', {
        patientData,
        consultationType
      });
      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to conduct multi-agent consultation'
      };
    }
  }

  /**
   * Get consultation from specific specialist agent
   */
  async getSpecialistConsultation(
    specialty: 'diagnostician' | 'pharmacologist' | 'radiologist' | 'pathologist' | 'emergencyPhysician' | 'internist',
    patientData: PatientData
  ): Promise<{ success: boolean; data?: SpecialistConsultationResponse; error?: string }> {
    try {
      const response = await apiClient.post<SpecialistConsultationResponse>(`/enhanced-ai/specialist-consultation/${specialty}`, {
        patientData
      });
      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to get specialist consultation'
      };
    }
  }

  /**
   * Upload and process medical document for RAG system
   */
  async uploadDocument(
    file: File,
    metadata: {
      title?: string;
      category?: string;
      description?: string;
    }
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const formData = new FormData();
      formData.append('document', file);
      if (metadata.title) formData.append('title', metadata.title);
      if (metadata.category) formData.append('category', metadata.category);
      if (metadata.description) formData.append('description', metadata.description);

      const response = await apiClient.post('/enhanced-ai/upload-document', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to upload document'
      };
    }
  }

  /**
   * Search medical literature using RAG system
   */
  async searchMedicalLiterature(
    query: string,
    options: {
      includeTexts?: boolean;
      includeImages?: boolean;
      includeDrugs?: boolean;
      includeLabs?: boolean;
      maxResults?: number;
    } = {}
  ): Promise<{ success: boolean; data?: MedicalLiteratureSearchResponse; error?: string }> {
    try {
      const params = new URLSearchParams({
        query,
        includeTexts: (options.includeTexts ?? true).toString(),
        includeImages: (options.includeImages ?? false).toString(),
        includeDrugs: (options.includeDrugs ?? true).toString(),
        includeLabs: (options.includeLabs ?? true).toString(),
        maxResults: (options.maxResults ?? 10).toString()
      });

      const response = await apiClient.get<MedicalLiteratureSearchResponse>(`/enhanced-ai/search-medical-literature?${params}`);
      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to search medical literature'
      };
    }
  }

  /**
   * Get real-time medical information from trusted sources
   */
  async getRealTimeMedicalInfo(
    query: string,
    options: {
      sources?: string[];
      maxResults?: number;
    } = {}
  ): Promise<{ success: boolean; data?: RealTimeMedicalInfoResponse; error?: string }> {
    try {
      const params = new URLSearchParams({
        query,
        sources: (options.sources ?? ['pubmed', 'fda', 'who', 'cdc', 'medlineplus']).join(','),
        maxResults: (options.maxResults ?? 5).toString()
      });

      const response = await apiClient.get<RealTimeMedicalInfoResponse>(`/enhanced-ai/real-time-medical-info?${params}`);
      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to get real-time medical information'
      };
    }
  }

  /**
   * Update medical knowledge across all AI agents (admin only)
   */
  async updateMedicalKnowledge(
    knowledgeUpdate: any
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const response = await apiClient.post('/enhanced-ai/update-knowledge', {
        knowledgeUpdate
      });
      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update medical knowledge'
      };
    }
  }

  /**
   * Get status of all AI systems (admin only)
   */
  async getSystemStatus(): Promise<{ success: boolean; data?: SystemStatusResponse; error?: string }> {
    try {
      const response = await apiClient.get<SystemStatusResponse>('/enhanced-ai/system-status');
      return response;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to get system status'
      };
    }
  }
}

export const enhancedAIService = new EnhancedAIService();
export default enhancedAIService;
