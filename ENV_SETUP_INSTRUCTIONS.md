# Environment Variables Setup Instructions

## 🔐 Important: Environment Files Not Committed

For security reasons, the `.env` files are not committed to git. You need to create them manually:

## 📁 Frontend Environment File

Create `.env` in the root directory:

```env
# MEDORA Frontend Environment Variables
# Production Configuration for Vercel Deployment

# API Configuration - Backend Vercel Deployment
VITE_API_URL=https://medora-ai-backend.vercel.app
VITE_API_BASE_URL=https://medora-ai-backend.vercel.app/api

# Application Configuration
VITE_APP_NAME=MEDORA
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION="AI-Powered Medical Assistant Platform"

# Theme Configuration
VITE_DEFAULT_THEME=dark

# Production Configuration
VITE_DEV_MODE=false
VITE_DEBUG=false

# Feature Flags
VITE_ENABLE_VOICE_CHAT=true
VITE_ENABLE_VIDEO_CALLS=true
VITE_ENABLE_AI_DIAGNOSIS=true
VITE_ENABLE_PAYMENTS=true

# Contact Information
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_CONTACT_PHONE=******-123-4567

# Social Media
VITE_TWITTER_HANDLE=@medora_ai
```

## 🔧 Backend Environment File

Create `backend/.env`:

```env
# =============================================================================
# MEDORA Backend Environment Configuration
# Production Configuration for Vercel Deployment
# =============================================================================

# Server Configuration
PORT=5000
NODE_ENV=production
FRONTEND_URL=https://medora-main-kdc735go4-bmds-projects-6efc3abf.vercel.app

# Database - Replace with your actual MongoDB Atlas URI
# For demo/testing without database, leave this commented out
# MONGODB_URI=mongodb+srv://username:<EMAIL>/medora?retryWrites=true&w=majority
DB_NAME=medora

# Authentication & Security
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
SESSION_SECRET=your-session-secret-key-here-also-make-it-random
JWT_EXPIRE=7d
BCRYPT_ROUNDS=12

# CORS Configuration - Updated with all Vercel URLs
ALLOWED_ORIGINS=https://www.medoraai.me,https://medoraai.me,https://medora-ai-backend.vercel.app,https://medora-main-kdc735go4-bmds-projects-6efc3abf.vercel.app,http://localhost:3000,http://localhost:1200,http://localhost:5173

# AI Service Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=500
OPENAI_TEMPERATURE=0.7

# Payment Gateways
PAYSTACK_SECRET_KEY=sk_live_your_paystack_secret_key_here
PAYSTACK_PUBLIC_KEY=pk_live_your_paystack_public_key_here
FLW_SECRET_KEY=your_flutterwave_secret_key_here
FLW_PUBLIC_KEY=your_flutterwave_public_key_here
FLW_SECRET_HASH=your_flutterwave_webhook_secret_hash_here
```

## 🚀 Vercel Environment Variables

Set these in your Vercel project dashboard:

### Required Variables:
- `NODE_ENV=production`
- `FRONTEND_URL=https://medora-main-kdc735go4-bmds-projects-6efc3abf.vercel.app`
- `ALLOWED_ORIGINS=https://www.medoraai.me,https://medoraai.me,https://medora-ai-backend.vercel.app,https://medora-main-kdc735go4-bmds-projects-6efc3abf.vercel.app,http://localhost:3000,http://localhost:1200,http://localhost:5173`
- `JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random`
- `SESSION_SECRET=your-session-secret-key-here-also-make-it-random`

### Optional (for full functionality):
- `MONGODB_URI=your-mongodb-atlas-uri`
- `OPENAI_API_KEY=your-openai-api-key`
- `PAYSTACK_SECRET_KEY=your-paystack-secret-key`
- `PAYSTACK_PUBLIC_KEY=your-paystack-public-key`

## ⚠️ Security Notes

1. **Never commit `.env` files** to version control
2. **Use strong, unique secrets** for JWT and session keys
3. **Rotate API keys regularly**
4. **Use different keys** for development and production
5. **Set appropriate scopes** in Vercel (Production/Preview/Development)

## 🧪 Testing

After setting up environment variables:

1. Test backend health: `curl https://medora-ai-backend.vercel.app/health`
2. Test CORS: Check browser console for CORS errors
3. Test API connectivity from frontend
4. Monitor Vercel function logs for any issues
