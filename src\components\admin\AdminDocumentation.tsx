import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Copy, ExternalLink, Download } from 'lucide-react';
import { toast } from 'sonner';

const AdminDocumentation = () => {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Code copied to clipboard');
  };

  const apiEndpoints = [
    {
      method: 'POST',
      endpoint: '/api/auth/login',
      description: 'Authenticate healthcare professional and return JWT token',
      parameters: ['email', 'password'],
      response: '{ "token": "jwt_token", "user": {...} }'
    },
    {
      method: 'GET',
      endpoint: '/api/healthcare-professionals',
      description: 'Get list of healthcare professionals (admin only)',
      parameters: ['page', 'limit', 'search', 'specialty'],
      response: '{ "professionals": [...], "total": 100, "page": 1 }'
    },
    {
      method: 'GET',
      endpoint: '/api/patients',
      description: 'Get list of patients (admin only)',
      parameters: ['page', 'limit', 'search', 'doctor_id'],
      response: '{ "patients": [...], "total": 250, "page": 1 }'
    },
    {
      method: 'GET',
      endpoint: '/api/medical-consultations',
      description: 'Get medical consultation analytics',
      parameters: ['start_date', 'end_date', 'specialty', 'doctor_id'],
      response: '{ "consultations": [...], "total": 1250, "by_specialty": {...} }'
    },
    {
      method: 'POST',
      endpoint: '/api/payments/create',
      description: 'Create new payment intent for medical services',
      parameters: ['amount', 'currency', 'gateway', 'service_type'],
      response: '{ "payment_intent": "pi_...", "client_secret": "..." }'
    },
    {
      method: 'GET',
      endpoint: '/api/analytics/revenue',
      description: 'Get medical practice revenue analytics',
      parameters: ['start_date', 'end_date', 'granularity', 'practice_id'],
      response: '{ "data": [...], "total": 45670, "by_service": {...} }'
    }
  ];

  const webhookEvents = [
    {
      event: 'healthcare_professional.created',
      description: 'Triggered when a new healthcare professional registers',
      payload: '{ "professional_id": "123", "email": "<EMAIL>", "specialty": "cardiology", "timestamp": "..." }'
    },
    {
      event: 'patient.created',
      description: 'Triggered when a new patient record is created',
      payload: '{ "patient_id": "456", "doctor_id": "123", "name": "Jane Doe", "timestamp": "..." }'
    },
    {
      event: 'consultation.completed',
      description: 'Triggered when a medical consultation is completed',
      payload: '{ "consultation_id": "789", "patient_id": "456", "doctor_id": "123", "diagnosis": "...", "timestamp": "..." }'
    },
    {
      event: 'prescription.created',
      description: 'Triggered when a new prescription is issued',
      payload: '{ "prescription_id": "101", "patient_id": "456", "medications": [...], "timestamp": "..." }'
    },
    {
      event: 'payment.succeeded',
      description: 'Triggered when a medical service payment is successful',
      payload: '{ "payment_id": "pay_123", "amount": 99, "service_type": "consultation", "doctor_id": "123" }'
    },
    {
      event: 'subscription.updated',
      description: 'Triggered when medical practice subscription status changes',
      payload: '{ "subscription_id": "sub_123", "status": "active", "practice_id": "123" }'
    }
  ];

  const sdks = [
    {
      language: 'JavaScript/Node.js',
      package: 'npm install medora-api',
      code: `import { MedoraAPI } from 'medora-api';

const api = new MedoraAPI('your-api-key');

// Get patient data
const patient = await api.patients.get('patient-id');

// Create medical consultation
const consultation = await api.consultations.create({
  patient_id: 'patient-456',
  symptoms: ['chest pain', 'shortness of breath'],
  specialty: 'cardiology'
});

// Create prescription
const prescription = await api.prescriptions.create({
  patient_id: 'patient-456',
  medications: [{
    name: 'Lisinopril',
    dosage: '10mg',
    frequency: 'once daily'
  }]
});`
    },
    {
      language: 'Python',
      package: 'pip install medora-python',
      code: `from medora import MedoraAPI

api = MedoraAPI('your-api-key')

# Get patient data
patient = api.patients.get('patient-id')

# Create medical consultation
consultation = api.consultations.create(
    patient_id='patient-456',
    symptoms=['chest pain', 'shortness of breath'],
    specialty='cardiology'
)

# Check drug interactions
interactions = api.prescriptions.check_interactions(
    medications=['lisinopril', 'metformin']
)`
    },
    {
      language: 'cURL',
      package: 'No installation required',
      code: `# Authenticate healthcare professional
curl -X POST https://api.medora.com/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{"email": "<EMAIL>", "password": "password"}'

# Get patients (with token)
curl -X GET https://api.medora.com/patients \\
  -H "Authorization: Bearer your-jwt-token"

# Create medical consultation
curl -X POST https://api.medora.com/medical-consultations \\
  -H "Authorization: Bearer your-jwt-token" \\
  -H "Content-Type: application/json" \\
  -d '{"patient_id": "456", "symptoms": ["chest pain"], "specialty": "cardiology"}'`
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">API Documentation</h1>
          <p className="text-muted-foreground">
            Complete API reference for developers and integrators
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Download PDF
          </Button>
          <Button>
            <ExternalLink className="mr-2 h-4 w-4" />
            View Online Docs
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="authentication">Authentication</TabsTrigger>
          <TabsTrigger value="endpoints">API Endpoints</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
          <TabsTrigger value="sdks">SDKs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Getting Started</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Base URL</h3>
                <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                  <code className="flex-1">https://api.medora.com/v1</code>
                <Button size="sm" variant="ghost" onClick={() => copyToClipboard('https://api.medora.com/v1')}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div>
                <h3 className="font-medium mb-2">Rate Limits</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-3 bg-muted/50 rounded-lg">
                    <p className="font-medium">Free Tier</p>
                    <p className="text-sm text-muted-foreground">100 requests/hour</p>
                  </div>
                  <div className="p-3 bg-muted/50 rounded-lg">
                    <p className="font-medium">Pro Tier</p>
                    <p className="text-sm text-muted-foreground">1,000 requests/hour</p>
                  </div>
                  <div className="p-3 bg-muted/50 rounded-lg">
                    <p className="font-medium">Enterprise</p>
                    <p className="text-sm text-muted-foreground">10,000 requests/hour</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Response Format</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Success Response</h3>
                  <div className="p-4 bg-muted rounded-lg">
                    <pre className="text-sm overflow-x-auto">
{`{
  "success": true,
  "data": {
    // Response data
  },
  "meta": {
    "timestamp": "2024-01-20T10:00:00Z",
    "request_id": "req_1234567890"
  }
}`}
                    </pre>
                  </div>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Error Response</h3>
                  <div className="p-4 bg-muted rounded-lg">
                    <pre className="text-sm overflow-x-auto">
{`{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "field": "email",
      "message": "Invalid email format"
    }
  },
  "meta": {
    "timestamp": "2024-01-20T10:00:00Z",
    "request_id": "req_1234567890"
  }
}`}
                    </pre>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="authentication" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>API Key Authentication</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground mb-4">
                  Include your API key in the Authorization header of all requests:
                </p>
                <div className="p-4 bg-muted rounded-lg">
                  <pre className="text-sm overflow-x-auto">
{`curl -X GET https://api.medora.com/v1/users \\
  -H "Authorization: Bearer sk_live_your_api_key_here"`}
                  </pre>
                  <Button 
                    size="sm" 
                    variant="ghost" 
                    className="mt-2"
                    onClick={() => copyToClipboard(`curl -X GET https://api.medora.com/v1/users \\
  -H "Authorization: Bearer sk_live_your_api_key_here"`)}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>JWT Token Authentication</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground mb-4">
                  For user-specific operations, use JWT tokens obtained from the login endpoint:
                </p>
                <div className="p-4 bg-muted rounded-lg">
                  <pre className="text-sm overflow-x-auto">
{`// 1. Login to get JWT token
curl -X POST https://api.medora.com/v1/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{"email": "<EMAIL>", "password": "password"}'

// 2. Use JWT token for subsequent requests
curl -X GET https://api.medora.com/v1/user/profile \\
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="endpoints" className="space-y-6">
          <div className="space-y-4">
            {apiEndpoints.map((endpoint, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Badge variant={endpoint.method === 'GET' ? 'secondary' : 'default'}>
                        {endpoint.method}
                      </Badge>
                      <code className="text-base">{endpoint.endpoint}</code>
                    </CardTitle>
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => copyToClipboard(`${endpoint.method} ${endpoint.endpoint}`)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">{endpoint.description}</p>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Parameters</h4>
                      <div className="space-y-1">
                        {endpoint.parameters.map((param, i) => (
                          <Badge key={i} variant="outline" className="mr-1">
                            {param}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Response</h4>
                      <div className="p-2 bg-muted rounded text-sm">
                        <code>{endpoint.response}</code>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="webhooks" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Webhook Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground mb-4">
                  Configure webhook endpoints in your admin settings to receive real-time notifications.
                </p>
                <div className="p-4 bg-muted/50 rounded-lg">
                  <h4 className="font-medium mb-2">Webhook Headers</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <code>Content-Type</code>
                      <span>application/json</span>
                    </div>
                    <div className="flex justify-between">
                      <code>X-Medora-Signature</code>
                      <span>HMAC-SHA256 signature</span>
                    </div>
                    <div className="flex justify-between">
                      <code>X-Medora-Event</code>
                      <span>Event type</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Webhook Events</h3>
            {webhookEvents.map((webhook, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Badge variant="outline">{webhook.event}</Badge>
                    </CardTitle>
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => copyToClipboard(webhook.payload)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">{webhook.description}</p>
                </CardHeader>
                <CardContent>
                  <div>
                    <h4 className="font-medium mb-2">Payload Example</h4>
                    <div className="p-3 bg-muted rounded-lg">
                      <pre className="text-sm overflow-x-auto">
                        {webhook.payload}
                      </pre>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="sdks" className="space-y-6">
          <div className="space-y-6">
            {sdks.map((sdk, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>{sdk.language}</CardTitle>
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => copyToClipboard(sdk.code)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">{sdk.package}</p>
                </CardHeader>
                <CardContent>
                  <div className="p-4 bg-muted rounded-lg">
                    <pre className="text-sm overflow-x-auto whitespace-pre-wrap">
                      {sdk.code}
                    </pre>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminDocumentation;