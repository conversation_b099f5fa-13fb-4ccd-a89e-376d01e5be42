const axios = require('axios');
const cheerio = require('cheerio');
const logger = require('../utils/logger');
const { RateLimiter } = require('limiter');

/**
 * Medical Information Scraping Service
 * Scrapes real-time medical information from trusted sources
 */
class MedicalScrapingService {
  constructor() {
    // Rate limiters for different sources
    this.rateLimiters = {
      pubmed: new RateLimiter({ tokensPerInterval: 3, interval: 'second' }),
      who: new RateLimiter({ tokensPerInterval: 2, interval: 'second' }),
      cdc: new RateLimiter({ tokensPerInterval: 2, interval: 'second' }),
      fda: new RateLimiter({ tokensPerInterval: 1, interval: 'second' }),
      medlineplus: new RateLimiter({ tokensPerInterval: 2, interval: 'second' })
    };

    this.sources = {
      pubmed: {
        baseUrl: 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/',
        apiKey: process.env.NCBI_API_KEY
      },
      who: {
        baseUrl: 'https://www.who.int/api/',
        newsUrl: 'https://www.who.int/news'
      },
      cdc: {
        baseUrl: 'https://www.cdc.gov/api/',
        newsUrl: 'https://www.cdc.gov/media/releases/'
      },
      fda: {
        baseUrl: 'https://api.fda.gov/',
        apiKey: process.env.FDA_API_KEY
      },
      medlineplus: {
        baseUrl: 'https://wsearch.nlm.nih.gov/ws/query'
      }
    };

    this.cache = new Map();
    this.cacheTimeout = 3600000; // 1 hour
  }

  /**
   * Search PubMed for medical literature
   */
  async searchPubMed(query, maxResults = 10) {
    try {
      await this.rateLimiters.pubmed.removeTokens(1);
      
      const cacheKey = `pubmed_${query}_${maxResults}`;
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.data;
        }
      }

      // Search for article IDs
      const searchUrl = `${this.sources.pubmed.baseUrl}esearch.fcgi`;
      const searchParams = {
        db: 'pubmed',
        term: query,
        retmax: maxResults,
        retmode: 'json',
        sort: 'relevance',
        api_key: this.sources.pubmed.apiKey
      };

      const searchResponse = await axios.get(searchUrl, { params: searchParams });
      const articleIds = searchResponse.data.esearchresult.idlist;

      if (articleIds.length === 0) {
        return { articles: [], total: 0 };
      }

      // Fetch article details
      const fetchUrl = `${this.sources.pubmed.baseUrl}efetch.fcgi`;
      const fetchParams = {
        db: 'pubmed',
        id: articleIds.join(','),
        retmode: 'xml',
        api_key: this.sources.pubmed.apiKey
      };

      const fetchResponse = await axios.get(fetchUrl, { params: fetchParams });
      const articles = this.parsePubMedXML(fetchResponse.data);

      const result = {
        articles,
        total: parseInt(searchResponse.data.esearchresult.count),
        query,
        timestamp: new Date()
      };

      this.cache.set(cacheKey, { data: result, timestamp: Date.now() });
      
      logger.info(`Retrieved ${articles.length} PubMed articles for query: ${query}`);
      return result;
    } catch (error) {
      logger.error('Error searching PubMed:', error);
      throw error;
    }
  }

  /**
   * Parse PubMed XML response
   */
  parsePubMedXML(xmlData) {
    const $ = cheerio.load(xmlData, { xmlMode: true });
    const articles = [];

    $('PubmedArticle').each((index, element) => {
      const article = $(element);
      
      const pmid = article.find('PMID').first().text();
      const title = article.find('ArticleTitle').text();
      const abstract = article.find('AbstractText').text();
      const journal = article.find('Title').first().text();
      const pubDate = this.extractPubDate(article);
      const authors = this.extractAuthors(article);
      const keywords = this.extractKeywords(article);

      articles.push({
        pmid,
        title,
        abstract,
        journal,
        pubDate,
        authors,
        keywords,
        source: 'PubMed',
        url: `https://pubmed.ncbi.nlm.nih.gov/${pmid}/`
      });
    });

    return articles;
  }

  /**
   * Extract publication date from PubMed XML
   */
  extractPubDate(article) {
    const year = article.find('PubDate Year').text() || article.find('PubDate MedlineDate').text().substring(0, 4);
    const month = article.find('PubDate Month').text() || '01';
    const day = article.find('PubDate Day').text() || '01';
    
    return new Date(`${year}-${month}-${day}`);
  }

  /**
   * Extract authors from PubMed XML
   */
  extractAuthors(article) {
    const authors = [];
    article.find('Author').each((index, authorElement) => {
      const author = $(authorElement);
      const lastName = author.find('LastName').text();
      const foreName = author.find('ForeName').text();
      if (lastName) {
        authors.push(`${foreName} ${lastName}`.trim());
      }
    });
    return authors;
  }

  /**
   * Extract keywords from PubMed XML
   */
  extractKeywords(article) {
    const keywords = [];
    article.find('Keyword').each((index, keywordElement) => {
      keywords.push($(keywordElement).text());
    });
    return keywords;
  }

  /**
   * Search FDA drug database
   */
  async searchFDADrugs(query, maxResults = 10) {
    try {
      await this.rateLimiters.fda.removeTokens(1);
      
      const cacheKey = `fda_drugs_${query}_${maxResults}`;
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.data;
        }
      }

      const searchUrl = `${this.sources.fda.baseUrl}drug/label.json`;
      const params = {
        search: `openfda.brand_name:"${query}" OR openfda.generic_name:"${query}"`,
        limit: maxResults
      };

      const response = await axios.get(searchUrl, { params });
      const drugs = response.data.results || [];

      const processedDrugs = drugs.map(drug => ({
        brandName: drug.openfda?.brand_name?.[0] || 'Unknown',
        genericName: drug.openfda?.generic_name?.[0] || 'Unknown',
        manufacturer: drug.openfda?.manufacturer_name?.[0] || 'Unknown',
        indications: drug.indications_and_usage?.[0] || 'Not specified',
        contraindications: drug.contraindications?.[0] || 'Not specified',
        warnings: drug.warnings?.[0] || 'Not specified',
        adverseReactions: drug.adverse_reactions?.[0] || 'Not specified',
        dosage: drug.dosage_and_administration?.[0] || 'Not specified',
        source: 'FDA',
        lastUpdated: new Date()
      }));

      const result = {
        drugs: processedDrugs,
        total: response.data.meta?.results?.total || processedDrugs.length,
        query,
        timestamp: new Date()
      };

      this.cache.set(cacheKey, { data: result, timestamp: Date.now() });
      
      logger.info(`Retrieved ${processedDrugs.length} FDA drug records for query: ${query}`);
      return result;
    } catch (error) {
      logger.error('Error searching FDA drugs:', error);
      throw error;
    }
  }

  /**
   * Get WHO health updates
   */
  async getWHOUpdates(topic = null, maxResults = 10) {
    try {
      await this.rateLimiters.who.removeTokens(1);
      
      const cacheKey = `who_updates_${topic || 'general'}_${maxResults}`;
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.data;
        }
      }

      const response = await axios.get(this.sources.who.newsUrl);
      const $ = cheerio.load(response.data);
      
      const updates = [];
      $('.sf-list-vertical__item').slice(0, maxResults).each((index, element) => {
        const item = $(element);
        const title = item.find('.link-container a').text().trim();
        const url = item.find('.link-container a').attr('href');
        const date = item.find('.date').text().trim();
        const summary = item.find('.summary').text().trim();

        if (title && (!topic || title.toLowerCase().includes(topic.toLowerCase()))) {
          updates.push({
            title,
            url: url.startsWith('http') ? url : `https://www.who.int${url}`,
            date: new Date(date),
            summary,
            source: 'WHO',
            topic: topic || 'general'
          });
        }
      });

      const result = {
        updates,
        total: updates.length,
        topic: topic || 'general',
        timestamp: new Date()
      };

      this.cache.set(cacheKey, { data: result, timestamp: Date.now() });
      
      logger.info(`Retrieved ${updates.length} WHO updates for topic: ${topic || 'general'}`);
      return result;
    } catch (error) {
      logger.error('Error getting WHO updates:', error);
      throw error;
    }
  }

  /**
   * Get CDC health alerts and updates
   */
  async getCDCUpdates(topic = null, maxResults = 10) {
    try {
      await this.rateLimiters.cdc.removeTokens(1);
      
      const cacheKey = `cdc_updates_${topic || 'general'}_${maxResults}`;
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.data;
        }
      }

      const response = await axios.get(this.sources.cdc.newsUrl);
      const $ = cheerio.load(response.data);
      
      const updates = [];
      $('.list-item').slice(0, maxResults).each((index, element) => {
        const item = $(element);
        const title = item.find('h3 a').text().trim();
        const url = item.find('h3 a').attr('href');
        const date = item.find('.date').text().trim();
        const summary = item.find('.description').text().trim();

        if (title && (!topic || title.toLowerCase().includes(topic.toLowerCase()))) {
          updates.push({
            title,
            url: url.startsWith('http') ? url : `https://www.cdc.gov${url}`,
            date: new Date(date),
            summary,
            source: 'CDC',
            topic: topic || 'general'
          });
        }
      });

      const result = {
        updates,
        total: updates.length,
        topic: topic || 'general',
        timestamp: new Date()
      };

      this.cache.set(cacheKey, { data: result, timestamp: Date.now() });
      
      logger.info(`Retrieved ${updates.length} CDC updates for topic: ${topic || 'general'}`);
      return result;
    } catch (error) {
      logger.error('Error getting CDC updates:', error);
      throw error;
    }
  }

  /**
   * Search MedlinePlus for patient education materials
   */
  async searchMedlinePlus(query, maxResults = 10) {
    try {
      await this.rateLimiters.medlineplus.removeTokens(1);
      
      const cacheKey = `medlineplus_${query}_${maxResults}`;
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.data;
        }
      }

      const params = {
        db: 'healthTopics',
        term: query,
        retmax: maxResults,
        rettype: 'brief'
      };

      const response = await axios.get(this.sources.medlineplus.baseUrl, { params });
      const $ = cheerio.load(response.data, { xmlMode: true });
      
      const topics = [];
      $('document').each((index, element) => {
        const doc = $(element);
        const title = doc.find('content').attr('title');
        const url = doc.find('content').attr('url');
        const summary = doc.find('content').text().trim();

        if (title) {
          topics.push({
            title,
            url,
            summary,
            source: 'MedlinePlus',
            category: 'Patient Education',
            lastUpdated: new Date()
          });
        }
      });

      const result = {
        topics,
        total: topics.length,
        query,
        timestamp: new Date()
      };

      this.cache.set(cacheKey, { data: result, timestamp: Date.now() });
      
      logger.info(`Retrieved ${topics.length} MedlinePlus topics for query: ${query}`);
      return result;
    } catch (error) {
      logger.error('Error searching MedlinePlus:', error);
      throw error;
    }
  }

  /**
   * Comprehensive medical information search across all sources
   */
  async comprehensiveSearch(query, options = {}) {
    try {
      const {
        includePubMed = true,
        includeFDA = true,
        includeWHO = true,
        includeCDC = true,
        includeMedlinePlus = true,
        maxResultsPerSource = 5
      } = options;

      const searchPromises = [];

      if (includePubMed) {
        searchPromises.push(
          this.searchPubMed(query, maxResultsPerSource)
            .then(result => ({ source: 'pubmed', data: result }))
            .catch(error => ({ source: 'pubmed', error: error.message }))
        );
      }

      if (includeFDA) {
        searchPromises.push(
          this.searchFDADrugs(query, maxResultsPerSource)
            .then(result => ({ source: 'fda', data: result }))
            .catch(error => ({ source: 'fda', error: error.message }))
        );
      }

      if (includeWHO) {
        searchPromises.push(
          this.getWHOUpdates(query, maxResultsPerSource)
            .then(result => ({ source: 'who', data: result }))
            .catch(error => ({ source: 'who', error: error.message }))
        );
      }

      if (includeCDC) {
        searchPromises.push(
          this.getCDCUpdates(query, maxResultsPerSource)
            .then(result => ({ source: 'cdc', data: result }))
            .catch(error => ({ source: 'cdc', error: error.message }))
        );
      }

      if (includeMedlinePlus) {
        searchPromises.push(
          this.searchMedlinePlus(query, maxResultsPerSource)
            .then(result => ({ source: 'medlineplus', data: result }))
            .catch(error => ({ source: 'medlineplus', error: error.message }))
        );
      }

      const results = await Promise.all(searchPromises);
      
      const compiledResults = {
        query,
        timestamp: new Date(),
        sources: {}
      };

      results.forEach(result => {
        if (result.error) {
          compiledResults.sources[result.source] = { error: result.error };
        } else {
          compiledResults.sources[result.source] = result.data;
        }
      });

      logger.info(`Comprehensive search completed for query: ${query}`);
      return compiledResults;
    } catch (error) {
      logger.error('Error in comprehensive search:', error);
      throw error;
    }
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
    logger.info('Medical scraping cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      timeout: this.cacheTimeout,
      keys: Array.from(this.cache.keys())
    };
  }
}

module.exports = MedicalScrapingService;
