import { apiClient, ApiResponse } from './api';
import { User } from './authService';

export interface Patient extends Omit<User, 'medicalHistory'> {
  patientId: string;
  medicalRecordNumber: string;
  insuranceInfo?: {
    provider: string;
    policyNumber: string;
    groupNumber?: string;
    expirationDate?: string;
  };
  allergies: string[];
  currentMedications: {
    name: string;
    dosage: string;
    frequency: string;
    startDate: string;
    endDate?: string;
    prescribedBy: string;
  }[];
  medicalHistory: {
    condition: string;
    diagnosedDate: string;
    status: 'active' | 'resolved' | 'chronic';
    notes?: string;
  }[];
  familyHistory: {
    relation: string;
    condition: string;
    ageAtDiagnosis?: number;
  }[];
  vitalSigns: {
    date: string;
    temperature?: number;
    bloodPressure?: {
      systolic: number;
      diastolic: number;
    };
    heartRate?: number;
    respiratoryRate?: number;
    oxygenSaturation?: number;
    weight?: number;
    height?: number;
    bmi?: number;
  }[];
  appointments: {
    _id: string;
    doctorId: string;
    date: string;
    time: string;
    type: 'consultation' | 'follow-up' | 'emergency' | 'routine';
    status: 'scheduled' | 'completed' | 'cancelled' | 'no-show';
    notes?: string;
  }[];
  labResults: {
    _id: string;
    testName: string;
    date: string;
    results: {
      parameter: string;
      value: string;
      unit: string;
      referenceRange: string;
      status: 'normal' | 'abnormal' | 'critical';
    }[];
    orderedBy: string;
    notes?: string;
  }[];
}

export interface CreatePatientData {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other';
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  emergencyContact: {
    name: string;
    relationship: string;
    phoneNumber: string;
  };
  insuranceInfo?: {
    provider: string;
    policyNumber: string;
    groupNumber?: string;
    expirationDate?: string;
  };
  allergies?: string[];
  medicalHistory?: string[];
}

export interface UpdatePatientData {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  emergencyContact?: {
    name: string;
    relationship: string;
    phoneNumber: string;
  };
  insuranceInfo?: {
    provider: string;
    policyNumber: string;
    groupNumber?: string;
    expirationDate?: string;
  };
  allergies?: string[];
}

export interface PatientList {
  patients: Patient[];
  total: number;
  page: number;
  limit: number;
}

class PatientService {
  async createPatient(patientData: CreatePatientData): Promise<ApiResponse<Patient>> {
    return apiClient.post<Patient>('/patients', patientData);
  }

  async getPatient(patientId: string): Promise<ApiResponse<Patient>> {
    return apiClient.get<Patient>(`/patients/${patientId}`);
  }

  async getAllPatients(
    page: number = 1,
    limit: number = 10,
    search?: string
  ): Promise<ApiResponse<PatientList>> {
    const searchQuery = search ? `&search=${encodeURIComponent(search)}` : '';
    return apiClient.get<PatientList>(
      `/patients?page=${page}&limit=${limit}${searchQuery}`
    );
  }

  async updatePatient(
    patientId: string,
    updateData: UpdatePatientData
  ): Promise<ApiResponse<Patient>> {
    return apiClient.put<Patient>(`/patients/${patientId}`, updateData);
  }

  async deletePatient(patientId: string): Promise<ApiResponse<{ message: string }>> {
    return apiClient.delete<{ message: string }>(`/patients/${patientId}`);
  }

  async addVitalSigns(
    patientId: string,
    vitalSigns: {
      temperature?: number;
      bloodPressure?: {
        systolic: number;
        diastolic: number;
      };
      heartRate?: number;
      respiratoryRate?: number;
      oxygenSaturation?: number;
      weight?: number;
      height?: number;
    }
  ): Promise<ApiResponse<Patient>> {
    return apiClient.post<Patient>(`/patients/${patientId}/vitals`, vitalSigns);
  }

  async addMedication(
    patientId: string,
    medication: {
      name: string;
      dosage: string;
      frequency: string;
      startDate: string;
      endDate?: string;
      prescribedBy: string;
    }
  ): Promise<ApiResponse<Patient>> {
    return apiClient.post<Patient>(`/patients/${patientId}/medications`, medication);
  }

  async updateMedication(
    patientId: string,
    medicationId: string,
    updateData: {
      dosage?: string;
      frequency?: string;
      endDate?: string;
    }
  ): Promise<ApiResponse<Patient>> {
    return apiClient.patch<Patient>(
      `/patients/${patientId}/medications/${medicationId}`,
      updateData
    );
  }

  async removeMedication(
    patientId: string,
    medicationId: string
  ): Promise<ApiResponse<Patient>> {
    return apiClient.delete<Patient>(`/patients/${patientId}/medications/${medicationId}`);
  }

  async addLabResult(
    patientId: string,
    labResult: {
      testName: string;
      results: {
        parameter: string;
        value: string;
        unit: string;
        referenceRange: string;
        status: 'normal' | 'abnormal' | 'critical';
      }[];
      orderedBy: string;
      notes?: string;
    }
  ): Promise<ApiResponse<Patient>> {
    return apiClient.post<Patient>(`/patients/${patientId}/lab-results`, labResult);
  }

  async getPatientStatistics(): Promise<ApiResponse<{
    totalPatients: number;
    newPatientsThisMonth: number;
    activePatients: number;
    averageAge: number;
    genderDistribution: { male: number; female: number; other: number };
  }>> {
    return apiClient.get('/patients/statistics');
  }
}

export const patientService = new PatientService();