const express = require('express');
const passport = require('passport');
const jwt = require('jsonwebtoken');
const router = express.Router();

// Helper function to generate JWT token
const generateToken = (user) => {
  return jwt.sign(
    { 
      userId: user._id, 
      email: user.email, 
      role: user.role 
    },
    process.env.JWT_SECRET,
    { expiresIn: '7d' }
  );
};

// Helper function to handle OAuth success
const handleOAuthSuccess = (req, res) => {
  try {
    const user = req.user;
    const token = generateToken(user);
    
    // Update last login
    user.lastLogin = new Date();
    user.save();
    
    // Redirect to frontend with token
    const frontendURL = process.env.FRONTEND_URL || 'http://localhost:1200';
    res.redirect(`${frontendURL}/auth/callback?token=${token}&user=${encodeURIComponent(JSON.stringify({
      id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      profilePicture: user.profilePicture
    }))}`);
  } catch (error) {
    console.error('OAuth success handler error:', error);
    const frontendURL = process.env.FRONTEND_URL || 'http://localhost:1200';
    res.redirect(`${frontendURL}/auth/error?message=Authentication failed`);
  }
};

// Helper function to handle OAuth failure
const handleOAuthFailure = (req, res) => {
  console.error('OAuth authentication failed');
  const frontendURL = process.env.FRONTEND_URL || 'http://localhost:1200';
  res.redirect(`${frontendURL}/auth/error?message=Authentication failed`);
};

// Google OAuth routes
router.get('/google', 
  passport.authenticate('google', { scope: ['profile', 'email'] })
);

router.get('/google/callback',
  passport.authenticate('google', { failureRedirect: '/auth/error' }),
  handleOAuthSuccess
);

// Facebook OAuth routes
router.get('/facebook',
  passport.authenticate('facebook', { scope: ['email'] })
);

router.get('/facebook/callback',
  passport.authenticate('facebook', { failureRedirect: '/auth/error' }),
  handleOAuthSuccess
);

// GitHub OAuth routes
router.get('/github',
  passport.authenticate('github', { scope: ['user:email'] })
);

router.get('/github/callback',
  passport.authenticate('github', { failureRedirect: '/auth/error' }),
  handleOAuthSuccess
);

// Twitter OAuth routes
router.get('/twitter',
  passport.authenticate('twitter')
);

router.get('/twitter/callback',
  passport.authenticate('twitter', { failureRedirect: '/auth/error' }),
  handleOAuthSuccess
);

// LinkedIn OAuth routes
router.get('/linkedin',
  passport.authenticate('linkedin', { scope: ['r_emailaddress', 'r_liteprofile'] })
);

router.get('/linkedin/callback',
  passport.authenticate('linkedin', { failureRedirect: '/auth/error' }),
  handleOAuthSuccess
);

// Error route
router.get('/error', (req, res) => {
  res.status(400).json({
    success: false,
    message: req.query.message || 'Authentication failed',
    error: 'OAuth authentication failed'
  });
});

// Get available OAuth providers
router.get('/providers', (req, res) => {
  const providers = [];
  
  if (process.env.GOOGLE_CLIENT_ID) providers.push('google');
  if (process.env.FACEBOOK_APP_ID) providers.push('facebook');
  if (process.env.GITHUB_CLIENT_ID) providers.push('github');
  if (process.env.TWITTER_CLIENT_ID) providers.push('twitter');
  if (process.env.LINKEDIN_CLIENT_ID) providers.push('linkedin');
  
  res.json({
    success: true,
    providers,
    baseUrl: `${req.protocol}://${req.get('host')}/api/auth`
  });
});

// Unlink social account
router.delete('/unlink/:provider', async (req, res) => {
  try {
    const { provider } = req.params;
    const userId = req.user?.userId || req.user?._id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
    
    const User = require('../models/User');
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Check if this is the only authentication method
    const hasPassword = user.password;
    const socialProviders = Object.keys(user.socialAuth || {}).filter(
      key => user.socialAuth[key] && Object.keys(user.socialAuth[key]).length > 0
    );
    
    if (!hasPassword && socialProviders.length === 1 && socialProviders[0] === provider) {
      return res.status(400).json({
        success: false,
        message: 'Cannot unlink the only authentication method. Please set a password first.'
      });
    }
    
    // Remove the social auth provider
    if (user.socialAuth && user.socialAuth[provider]) {
      user.socialAuth[provider] = undefined;
      await user.save();
    }
    
    res.json({
      success: true,
      message: `${provider} account unlinked successfully`
    });
  } catch (error) {
    console.error('Unlink social account error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unlink social account',
      error: error.message
    });
  }
});

// Link social account to existing user
router.post('/link/:provider', async (req, res) => {
  try {
    const { provider } = req.params;
    const userId = req.user?.userId || req.user?._id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
    
    // Store user ID in session for linking
    req.session.linkUserId = userId;
    
    // Redirect to OAuth provider
    const authUrl = `/api/auth/${provider}?link=true`;
    res.json({
      success: true,
      authUrl,
      message: `Redirecting to ${provider} for account linking`
    });
  } catch (error) {
    console.error('Link social account error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initiate account linking',
      error: error.message
    });
  }
});

module.exports = router;
