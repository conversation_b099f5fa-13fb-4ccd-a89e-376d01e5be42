import { apiClient, ApiResponse } from './api';

export interface ChatMessage {
  id: string;
  message: string;
  sender: 'user' | 'ai';
  timestamp: string;
  conversationId?: string;
}

export interface VoiceMessage {
  transcript: string;
  audioData?: string;
  conversationId?: string;
}

export interface AIResponse {
  response: string;
  conversationId: string;
  timestamp: string;
  confidence: number;
  shouldSpeak?: boolean;
}

export interface SpeechRecognitionEvent extends Event {
  resultIndex: number;
  results: SpeechRecognitionResultList;
}

interface SpeechRecognitionResultList {
  length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionResult {
  length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
  isFinal: boolean;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

export interface SymptomAnalysis {
  primarySymptoms: string[];
  possibleConditions: Array<{
    condition: string;
    probability: number;
    description: string;
  }>;
  recommendations: string[];
  urgencyLevel: 'low' | 'medium' | 'high';
  disclaimer: string;
}

class AIService {
  async sendChatMessage(message: string, conversationId?: string): Promise<ApiResponse<AIResponse>> {
    return apiClient.post<AIResponse>('/ai/chat', {
      message,
      conversationId
    });
  }

  async sendVoiceMessage(voiceData: VoiceMessage): Promise<ApiResponse<AIResponse>> {
    return apiClient.post<AIResponse>('/ai/voice', voiceData);
  }

  async getConversationHistory(conversationId: string): Promise<ApiResponse<{ messages: ChatMessage[] }>> {
    return apiClient.get<{ messages: ChatMessage[] }>(`/ai/conversation/${conversationId}`);
  }

  async analyzeSymptoms(
    symptoms: string[], 
    duration?: string, 
    severity?: number, 
    additionalInfo?: string
  ): Promise<ApiResponse<SymptomAnalysis>> {
    return apiClient.post<SymptomAnalysis>('/ai/analyze-symptoms', {
      symptoms,
      duration,
      severity,
      additionalInfo
    });
  }

  // Text-to-Speech using Web Speech API
  speak(text: string, options?: { rate?: number; pitch?: number; volume?: number }): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!window.speechSynthesis) {
        reject(new Error('Speech synthesis not supported'));
        return;
      }

      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = options?.rate || 0.9;
      utterance.pitch = options?.pitch || 1;
      utterance.volume = options?.volume || 0.8;

      utterance.onend = () => resolve();
      utterance.onerror = (event) => reject(new Error('Speech synthesis failed'));

      window.speechSynthesis.speak(utterance);
    });
  }

  // Speech-to-Text using Web Speech API
  startListening(
    onResult: (transcript: string, isFinal: boolean) => void,
    onError?: (error: string) => void
  ): () => void {
    if (!window.SpeechRecognition && !window.webkitSpeechRecognition) {
      onError?.('Speech recognition not supported');
      return () => {};
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();

    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';

    recognition.onresult = (event: SpeechRecognitionEvent) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const transcript = result[0].transcript;
        if (result.isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      onResult(finalTranscript || interimTranscript, !!finalTranscript);
    };

    recognition.onerror = (event: unknown) => {
      onError?.(event.error || 'Speech recognition error');
    };

    recognition.start();

    return () => {
      recognition.stop();
    };
  }
}

export const aiService = new AIService();
