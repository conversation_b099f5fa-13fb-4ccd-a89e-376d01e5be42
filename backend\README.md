# MEDORA AI Backend

🏥 **AI-Powered Medical Assistant Backend API**

A comprehensive Node.js backend system powering the MEDORA AI medical assistant platform with advanced AI capabilities, medical data processing, and secure patient management.

## 🚀 Features

### Core Functionality
- **AI Medical Consultations** - Advanced AI-powered medical analysis and recommendations
- **Patient Management** - Comprehensive patient record management system
- **Multi-Agent AI System** - Collaborative AI agents for enhanced medical insights
- **Medical Data Scraping** - Real-time medical research and drug information
- **Secure Authentication** - JWT-based auth with social login support
- **Payment Integration** - Paystack and Flutterwave payment processing
- **Real-time Communication** - WebSocket support for live consultations

### AI & ML Capabilities
- **Enhanced AI Service** - 25+ years medical experience simulation
- **Multimodal RAG System** - Advanced retrieval-augmented generation
- **Medical Scraping Service** - PubMed, FDA, WHO data integration
- **ML Analysis Engine** - Machine learning model integration
- **Agentic Medical System** - Multi-agent collaborative diagnosis

### Security & Compliance
- **CORS Protection** - Comprehensive cross-origin resource sharing
- **Rate Limiting** - API rate limiting and abuse prevention
- **Data Encryption** - Secure data handling and storage
- **Audit Logging** - Comprehensive system activity logging
- **Role-Based Access** - <PERSON><PERSON>, Doctor, Nurse, Patient roles

## 📋 Prerequisites

- **Node.js** >= 16.0.0
- **MongoDB** >= 5.0
- **npm** or **yarn**
- **OpenAI API Key** (for AI features)
- **MongoDB Atlas** account (recommended)

## 🌐 Live Deployment

The MEDORA AI Backend is currently deployed and running at:
**https://medora-ai-backend.vercel.app**

### API Health Check
Test the live API: https://medora-ai-backend.vercel.app/api/health

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone https://github.com/obibiifeanyi/medora-ai-backend.git
cd medora-ai-backend
```

### 2. Install Dependencies
```bash
npm install
# or
yarn install
```

### 3. Environment Configuration
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
# Server Configuration
PORT=5000
NODE_ENV=production
FRONTEND_URL=https://www.medoraai.me

# Database
MONGODB_URI=your_mongodb_connection_string
DB_NAME=medora

# Authentication & Security
JWT_SECRET=your_jwt_secret_key
SESSION_SECRET=your_session_secret
JWT_EXPIRE=7d

# AI Service Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-3.5-turbo

# Payment Gateways
PAYSTACK_SECRET_KEY=your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=your_paystack_public_key

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# CORS Configuration
CORS_ORIGIN=https://www.medoraai.me,https://medoraai.me
```

### 4. Start the Server
```bash
# Development
npm run dev

# Production
npm start
```

## 📚 API Documentation

### Authentication Endpoints
```
POST /api/auth/register     - User registration
POST /api/auth/login        - User login
POST /api/auth/logout       - User logout
POST /api/auth/refresh      - Refresh JWT token
POST /api/auth/forgot       - Password reset request
POST /api/auth/reset        - Password reset confirmation
```

### AI & Medical Endpoints
```
POST /api/enhanced-ai/consultation           - AI medical consultation
POST /api/enhanced-ai/multi-agent-consultation - Multi-agent consultation
POST /api/ai/chat                           - AI chat interface
POST /api/ml/analyze                        - ML analysis
POST /api/scraping/comprehensive            - Medical data scraping
```

### Patient Management
```
GET    /api/patients        - List patients
POST   /api/patients        - Create patient
GET    /api/patients/:id    - Get patient details
PUT    /api/patients/:id    - Update patient
DELETE /api/patients/:id    - Delete patient
```

### Payment Processing
```
POST /api/payments/initialize    - Initialize payment
POST /api/payments/verify        - Verify payment
GET  /api/payments/history       - Payment history
```

## 🏗️ Project Structure

```
medora-ai-backend/
├── config/                 # Configuration files
├── middleware/             # Express middleware
├── models/                 # MongoDB models
├── routes/                 # API routes
├── services/               # Business logic services
├── utils/                  # Utility functions
├── data/                   # Data storage (development)
├── simple-server.js        # Main server file
├── package.json           # Dependencies
└── .env.example           # Environment template
```

## 🔧 Configuration

### MongoDB Setup
1. Create a MongoDB Atlas cluster
2. Get your connection string
3. Add to `MONGODB_URI` in `.env`

### OpenAI Integration
1. Get API key from OpenAI
2. Add to `OPENAI_API_KEY` in `.env`
3. Configure model preferences

### Payment Gateway Setup
**Paystack:**
1. Create Paystack account
2. Get secret and public keys
3. Add to environment variables

**Flutterwave:**
1. Create Flutterwave account
2. Get API keys
3. Configure webhook URLs

## 🚀 Deployment

### Vercel Deployment (Recommended)
```bash
# Install Vercel CLI
npm install -g vercel

# Login and deploy
vercel login
vercel

# Set environment variables in Vercel dashboard
# Deploy to production
vercel --prod
```

**See [VERCEL_DEPLOYMENT.md](VERCEL_DEPLOYMENT.md) for detailed Vercel setup instructions.**

### Railway Deployment
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

### Heroku Deployment
```bash
# Install Heroku CLI
heroku create medora-ai-backend
heroku config:set NODE_ENV=production
heroku config:set MONGODB_URI=your_mongodb_uri
# Add other environment variables
git push heroku main
```

### Docker Deployment
```bash
# Build image
docker build -t medora-ai-backend .

# Run container
docker run -p 5000:5000 --env-file .env medora-ai-backend
```

## 🔒 Security Features

- **JWT Authentication** with refresh tokens
- **Password Hashing** using bcrypt
- **Rate Limiting** to prevent abuse
- **CORS Protection** for cross-origin requests
- **Input Validation** using express-validator
- **SQL Injection Protection** via MongoDB ODM
- **XSS Protection** with helmet middleware

## 🧪 Testing

```bash
# Run tests
npm test

# Run with coverage
npm run test:coverage

# Lint code
npm run lint
```

## 📊 Monitoring & Logging

- **Winston Logger** for structured logging
- **Error Tracking** with comprehensive error handling
- **Performance Monitoring** built-in metrics
- **Health Check** endpoint at `/api/health`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- **Email**: <EMAIL>
- **Documentation**: [API Docs](https://docs.medora.ai)
- **Issues**: [GitHub Issues](https://github.com/obibiifeanyi/medora-ai-backend/issues)

## 🔗 Related Projects

- **Frontend**: [MEDORA AI Frontend](https://github.com/obibiifeanyi/medora-ai-frontend)
- **Mobile App**: [MEDORA AI Mobile](https://github.com/obibiifeanyi/medora-ai-mobile)

## 🌟 Key Features in Detail

### Enhanced AI Medical Consultation
The system provides AI-powered medical consultations with:
- **25+ Years Experience Simulation** - AI trained on extensive medical knowledge
- **Multi-Modal Analysis** - Text, image, and document processing
- **Real-time Recommendations** - Instant medical insights and suggestions
- **Differential Diagnosis** - Multiple potential diagnoses with confidence scores

### Multi-Agent AI System
Collaborative AI agents working together:
- **Specialist Agents** - Different medical specialties
- **Research Agent** - Real-time medical research integration
- **Validation Agent** - Cross-verification of recommendations
- **Consensus Building** - Collaborative decision making

### Medical Data Integration
Real-time access to medical databases:
- **PubMed Integration** - Latest medical research
- **FDA Drug Database** - Comprehensive drug information
- **WHO Guidelines** - International health guidelines
- **Clinical Trials** - Ongoing and completed trials

### Advanced Security
Enterprise-grade security features:
- **Multi-Factor Authentication** - Optional 2FA support
- **Role-Based Permissions** - Granular access control
- **Audit Trails** - Complete activity logging
- **Data Encryption** - End-to-end encryption
- **HIPAA Compliance** - Healthcare data protection

## 🔄 API Response Format

All API responses follow a consistent format:

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

Error responses:
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 🔧 Environment Variables Reference

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `PORT` | Server port | No | 5000 |
| `NODE_ENV` | Environment | Yes | development |
| `MONGODB_URI` | MongoDB connection | Yes | - |
| `JWT_SECRET` | JWT signing key | Yes | - |
| `OPENAI_API_KEY` | OpenAI API key | Yes | - |
| `PAYSTACK_SECRET_KEY` | Paystack secret | No | - |
| `SMTP_HOST` | Email server | No | - |
| `CORS_ORIGIN` | Allowed origins | Yes | - |

---

**Built with ❤️ by the MEDORA AI Team**
