import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ModernCard } from './modern-card';
import { EnhancedButton } from './enhanced-button';
import {
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Play,
  Pause,
  RotateCcw,
  Sparkles,
  Brain,
  Activity,
  Zap
} from 'lucide-react';

interface VoiceInterfaceProps {
  className?: string;
}

const ResponsiveVoiceInterface: React.FC<VoiceInterfaceProps> = ({ className }) => {
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [currentMessage, setCurrentMessage] = useState('');
  const [conversationStep, setConversationStep] = useState(0);

  const conversation = [
    { type: 'user', text: 'Hello MEDORA, I have been experiencing chest pain.' },
    { type: 'ai', text: 'I understand your concern. Can you describe the pain? Is it sharp, dull, or crushing?' },
    { type: 'user', text: 'It\'s a sharp pain that comes and goes, especially when I breathe deeply.' },
    { type: 'ai', text: 'Based on your symptoms, I recommend seeing a healthcare provider. This could be related to several conditions that need proper evaluation.' }
  ];

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isListening) {
      interval = setInterval(() => {
        setAudioLevel(Math.random() * 100);
      }, 100);
    } else {
      setAudioLevel(0);
    }
    return () => clearInterval(interval);
  }, [isListening]);

  const toggleListening = () => {
    setIsListening(!isListening);
    if (!isListening) {
      // Simulate conversation progression
      setTimeout(() => {
        setIsListening(false);
        setIsSpeaking(true);
        setCurrentMessage(conversation[conversationStep]?.text || '');
        setTimeout(() => {
          setIsSpeaking(false);
          setConversationStep((prev) => (prev + 1) % conversation.length);
        }, 3000);
      }, 2000);
    }
  };

  const resetDemo = () => {
    setIsListening(false);
    setIsSpeaking(false);
    setConversationStep(0);
    setCurrentMessage('');
    setAudioLevel(0);
  };

  return (
    <div className={`w-full max-w-4xl mx-auto p-4 ${className}`}>
      <ModernCard 
        glassmorphism 
        glow 
        borderGlow 
        className="p-6 lg:p-8"
      >
        <div className="text-center mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-center space-x-2 mb-4"
          >
            <img
              src="/lovable-uploads/logo-png.png"
              alt="MEDORA AI"
              className="h-6 w-6 lg:h-8 lg:w-8 object-contain"
            />
            <h3 className="text-xl lg:text-2xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
              AI Voice Consultation Demo
            </h3>
          </motion.div>
          <p className="text-sm lg:text-base text-muted-foreground max-w-2xl mx-auto">
            Experience real-time AI-powered medical consultations with natural voice interaction
          </p>
        </div>

        {/* Voice Visualizer */}
        <div className="flex flex-col items-center space-y-6 lg:space-y-8">
          {/* Main Voice Button */}
          <motion.div
            className="relative"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {/* Pulse rings */}
            <AnimatePresence>
              {(isListening || isSpeaking) && (
                <>
                  {[...Array(3)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute inset-0 rounded-full border-2 border-primary/30"
                      initial={{ scale: 1, opacity: 0.7 }}
                      animate={{
                        scale: [1, 2, 3],
                        opacity: [0.7, 0.3, 0],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: i * 0.4,
                      }}
                    />
                  ))}
                </>
              )}
            </AnimatePresence>

            {/* Voice button */}
            <motion.button
              onClick={toggleListening}
              className={`relative w-20 h-20 lg:w-24 lg:h-24 rounded-full flex items-center justify-center transition-all duration-300 ${
                isListening
                  ? 'bg-red-500 hover:bg-red-600 shadow-lg shadow-red-500/25'
                  : isSpeaking
                  ? 'bg-blue-500 hover:bg-blue-600 shadow-lg shadow-blue-500/25'
                  : 'bg-primary hover:bg-primary/90 shadow-lg shadow-primary/25'
              }`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              {isListening ? (
                <MicOff className="h-8 w-8 lg:h-10 lg:w-10 text-white" />
              ) : isSpeaking ? (
                <Volume2 className="h-8 w-8 lg:h-10 lg:w-10 text-white" />
              ) : (
                <Mic className="h-8 w-8 lg:h-10 lg:w-10 text-white" />
              )}
            </motion.button>
          </motion.div>

          {/* Audio Level Visualizer */}
          <div className="flex items-center space-x-1 h-12 lg:h-16">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="w-1 lg:w-1.5 bg-primary rounded-full"
                animate={{
                  height: isListening
                    ? Math.random() * (audioLevel / 2) + 10
                    : isSpeaking
                    ? Math.sin((Date.now() / 100) + i) * 20 + 30
                    : 4,
                }}
                transition={{ duration: 0.1 }}
              />
            ))}
          </div>

          {/* Status Text */}
          <motion.div
            className="text-center min-h-[60px] lg:min-h-[80px] flex items-center justify-center"
            layout
          >
            <AnimatePresence mode="wait">
              {isListening && (
                <motion.div
                  key="listening"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="flex items-center space-x-2"
                >
                  <Activity className="h-4 w-4 lg:h-5 lg:w-5 text-red-500 animate-pulse" />
                  <span className="text-sm lg:text-base font-medium text-red-500">
                    Listening...
                  </span>
                </motion.div>
              )}
              {isSpeaking && (
                <motion.div
                  key="speaking"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="text-center space-y-2"
                >
                  <div className="flex items-center justify-center space-x-2">
                    <Zap className="h-4 w-4 lg:h-5 lg:w-5 text-blue-500 animate-pulse" />
                    <span className="text-sm lg:text-base font-medium text-blue-500">
                      MEDORA is responding...
                    </span>
                  </div>
                  <p className="text-xs lg:text-sm text-muted-foreground max-w-md mx-auto px-4">
                    {currentMessage}
                  </p>
                </motion.div>
              )}
              {!isListening && !isSpeaking && (
                <motion.div
                  key="ready"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="text-center space-y-2"
                >
                  <div className="flex items-center justify-center space-x-2">
                    <Sparkles className="h-4 w-4 lg:h-5 lg:w-5 text-primary" />
                    <span className="text-sm lg:text-base font-medium">
                      Ready to help
                    </span>
                  </div>
                  <p className="text-xs lg:text-sm text-muted-foreground">
                    Click the microphone to start your consultation
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Control Buttons */}
          <div className="flex flex-wrap items-center justify-center gap-3 lg:gap-4">
            <EnhancedButton
              variant="outline"
              size="sm"
              onClick={toggleListening}
              disabled={isSpeaking}
              icon={isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
              className="text-xs lg:text-sm"
            >
              {isListening ? 'Stop' : 'Start'}
            </EnhancedButton>
            
            <EnhancedButton
              variant="outline"
              size="sm"
              onClick={resetDemo}
              icon={<RotateCcw className="h-4 w-4" />}
              className="text-xs lg:text-sm"
            >
              Reset
            </EnhancedButton>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 w-full mt-8">
            {[
              { icon: Brain, title: 'AI Analysis', desc: 'Real-time symptom analysis' },
              { icon: Activity, title: 'Voice Recognition', desc: 'Natural speech processing' },
              { icon: Zap, title: 'Instant Response', desc: 'Immediate medical insights' }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="text-center p-4 rounded-lg bg-accent/50 hover:bg-accent/70 transition-colors duration-200"
              >
                <feature.icon className="h-6 w-6 lg:h-8 lg:w-8 text-primary mx-auto mb-2" />
                <h4 className="text-sm lg:text-base font-semibold mb-1">{feature.title}</h4>
                <p className="text-xs lg:text-sm text-muted-foreground">{feature.desc}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </ModernCard>
    </div>
  );
};

export default ResponsiveVoiceInterface;
