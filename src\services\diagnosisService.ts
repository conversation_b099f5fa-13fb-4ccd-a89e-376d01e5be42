import { apiClient, ApiResponse } from './api';

export interface Symptom {
  name: string;
  severity: 'mild' | 'moderate' | 'severe';
  duration: string;
  description?: string;
}

export interface DiagnosisRequest {
  patientId?: string;
  symptoms: Symptom[];
  medicalHistory?: string[];
  currentMedications?: string[];
  allergies?: string[];
  vitalSigns?: {
    temperature?: number;
    bloodPressure?: {
      systolic: number;
      diastolic: number;
    };
    heartRate?: number;
    respiratoryRate?: number;
    oxygenSaturation?: number;
  };
  additionalNotes?: string;
}

export interface DiagnosisResult {
  _id: string;
  patientId: string;
  symptoms: Symptom[];
  possibleConditions: {
    condition: string;
    probability: number;
    description: string;
    severity: 'low' | 'medium' | 'high';
    recommendedActions: string[];
  }[];
  recommendations: {
    immediate: string[];
    followUp: string[];
    lifestyle: string[];
    monitoring: string[];
  };
  riskFactors: string[];
  redFlags: string[];
  confidence: number;
  aiAnalysis: {
    model: string;
    version: string;
    processingTime: number;
    dataQuality: number;
  };
  doctorId?: string;
  status: 'pending' | 'reviewed' | 'completed';
  createdAt: string;
  updatedAt: string;
}

export interface DiagnosisHistory {
  diagnoses: DiagnosisResult[];
  total: number;
  page: number;
  limit: number;
}

class DiagnosisService {
  async createDiagnosis(diagnosisData: DiagnosisRequest): Promise<ApiResponse<DiagnosisResult>> {
    return apiClient.post<DiagnosisResult>('/diagnosis', diagnosisData);
  }

  async updateDiagnosis(id: string, diagnosisData: Partial<DiagnosisRequest>): Promise<ApiResponse<DiagnosisResult>> {
    return apiClient.put<DiagnosisResult>(`/diagnosis/${id}`, diagnosisData);
  }

  async getDiagnosis(diagnosisId: string): Promise<ApiResponse<DiagnosisResult>> {
    return apiClient.get<DiagnosisResult>(`/diagnosis/${diagnosisId}`);
  }

  async getPatientDiagnoses(
    patientId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<ApiResponse<DiagnosisHistory>> {
    return apiClient.get<DiagnosisHistory>(
      `/diagnosis/patient/${patientId}?page=${page}&limit=${limit}`
    );
  }

  async getAllDiagnoses(
    page: number = 1,
    limit: number = 10,
    status?: string
  ): Promise<ApiResponse<DiagnosisHistory>> {
    const statusQuery = status ? `&status=${status}` : '';
    return apiClient.get<DiagnosisHistory>(
      `/diagnosis?page=${page}&limit=${limit}${statusQuery}`
    );
  }

  async updateDiagnosisStatus(
    diagnosisId: string,
    status: 'pending' | 'reviewed' | 'completed',
    doctorNotes?: string
  ): Promise<ApiResponse<DiagnosisResult>> {
    return apiClient.patch<DiagnosisResult>(`/diagnosis/${diagnosisId}`, {
      status,
      doctorNotes
    });
  }

  async deleteDiagnosis(diagnosisId: string): Promise<ApiResponse<{ message: string }>> {
    return apiClient.delete<{ message: string }>(`/diagnosis/${diagnosisId}`);
  }

  async getSymptomSuggestions(query: string): Promise<ApiResponse<string[]>> {
    return apiClient.get<string[]>(`/diagnosis/symptoms/search?q=${encodeURIComponent(query)}`);
  }

  async getDiagnosisStatistics(): Promise<ApiResponse<{
    totalDiagnoses: number;
    pendingReviews: number;
    completedToday: number;
    averageConfidence: number;
    topConditions: { condition: string; count: number }[];
  }>> {
    return apiClient.get('/diagnosis/statistics');
  }
}

export const diagnosisService = new DiagnosisService();