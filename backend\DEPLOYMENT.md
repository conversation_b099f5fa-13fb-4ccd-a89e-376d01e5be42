# MEDORA AI Backend Deployment Guide

## 🚀 Deployment Options

### 1. Railway Deployment (Recommended)

Railway provides excellent Node.js hosting with automatic deployments.

#### Prerequisites
- Railway account
- GitHub repository

#### Steps
1. **Install Railway CLI**
   ```bash
   npm install -g @railway/cli
   ```

2. **Login to Railway**
   ```bash
   railway login
   ```

3. **Initialize Project**
   ```bash
   railway init
   ```

4. **Set Environment Variables**
   ```bash
   railway variables set NODE_ENV=production
   railway variables set MONGODB_URI=your_mongodb_uri
   railway variables set JWT_SECRET=your_jwt_secret
   railway variables set OPENAI_API_KEY=your_openai_key
   railway variables set FRONTEND_URL=https://www.medoraai.me
   railway variables set CORS_ORIGIN=https://www.medoraai.me,https://medoraai.me
   ```

5. **Deploy**
   ```bash
   railway up
   ```

#### Railway Configuration
Create `railway.json`:
```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm start",
    "healthcheckPath": "/api/health",
    "healthcheckTimeout": 100,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### 2. Heroku Deployment

#### Prerequisites
- Heroku account
- Heroku CLI installed

#### Steps
1. **Create Heroku App**
   ```bash
   heroku create medora-ai-backend
   ```

2. **Set Environment Variables**
   ```bash
   heroku config:set NODE_ENV=production
   heroku config:set MONGODB_URI=your_mongodb_uri
   heroku config:set JWT_SECRET=your_jwt_secret
   heroku config:set OPENAI_API_KEY=your_openai_key
   heroku config:set FRONTEND_URL=https://www.medoraai.me
   ```

3. **Deploy**
   ```bash
   git push heroku main
   ```

#### Heroku Configuration
Create `Procfile`:
```
web: npm start
```

### 3. DigitalOcean App Platform

#### Steps
1. **Connect GitHub Repository**
   - Go to DigitalOcean App Platform
   - Connect your GitHub repository

2. **Configure Build Settings**
   ```yaml
   name: medora-ai-backend
   services:
   - name: api
     source_dir: /
     github:
       repo: obibiifeanyi/medora-ai-backend
       branch: main
     run_command: npm start
     environment_slug: node-js
     instance_count: 1
     instance_size_slug: basic-xxs
     envs:
     - key: NODE_ENV
       value: production
     - key: PORT
       value: "8080"
   ```

### 4. AWS EC2 Deployment

#### Prerequisites
- AWS account
- EC2 instance (Ubuntu 20.04 LTS recommended)

#### Steps
1. **Connect to EC2 Instance**
   ```bash
   ssh -i your-key.pem ubuntu@your-ec2-ip
   ```

2. **Install Node.js**
   ```bash
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```

3. **Install PM2**
   ```bash
   sudo npm install -g pm2
   ```

4. **Clone Repository**
   ```bash
   git clone https://github.com/obibiifeanyi/medora-ai-backend.git
   cd medora-ai-backend
   ```

5. **Install Dependencies**
   ```bash
   npm install
   ```

6. **Configure Environment**
   ```bash
   cp .env.example .env
   nano .env
   ```

7. **Start with PM2**
   ```bash
   pm2 start simple-server.js --name "medora-backend"
   pm2 startup
   pm2 save
   ```

#### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 5. Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 5000

USER node

CMD ["npm", "start"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  medora-backend:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=${MONGODB_URI}
      - JWT_SECRET=${JWT_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    restart: unless-stopped
    depends_on:
      - mongodb

  mongodb:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    restart: unless-stopped

volumes:
  mongodb_data:
```

#### Deploy with Docker
```bash
# Build image
docker build -t medora-ai-backend .

# Run container
docker run -d \
  --name medora-backend \
  -p 5000:5000 \
  --env-file .env \
  medora-ai-backend

# Or use Docker Compose
docker-compose up -d
```

## 🔧 Environment Configuration

### Required Environment Variables
```env
# Server
NODE_ENV=production
PORT=5000
FRONTEND_URL=https://www.medoraai.me

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/medora

# Authentication
JWT_SECRET=your-super-secret-jwt-key
SESSION_SECRET=your-session-secret
JWT_EXPIRE=7d

# AI Services
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo

# CORS
CORS_ORIGIN=https://www.medoraai.me,https://medoraai.me

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Payments
PAYSTACK_SECRET_KEY=sk_live_your_paystack_secret
PAYSTACK_PUBLIC_KEY=pk_live_your_paystack_public
```

### Optional Environment Variables
```env
# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/medora.log

# Medical APIs
FDA_API_KEY=your_fda_api_key
PUBMED_API_KEY=your_pubmed_api_key
```

## 🔍 Health Checks

### Health Check Endpoint
```
GET /api/health
```

Response:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "uptime": 3600,
    "version": "1.0.0",
    "database": "connected",
    "ai_service": "operational"
  }
}
```

### Monitoring Setup
```bash
# PM2 Monitoring
pm2 monit

# Check logs
pm2 logs medora-backend

# Restart if needed
pm2 restart medora-backend
```

## 🔒 Security Checklist

- [ ] Environment variables properly set
- [ ] CORS origins configured correctly
- [ ] Rate limiting enabled
- [ ] HTTPS enabled (production)
- [ ] Database connection secured
- [ ] API keys rotated regularly
- [ ] Logs monitored for security events
- [ ] Backup strategy implemented

## 🚨 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Check `CORS_ORIGIN` environment variable
   - Ensure frontend URL is included in allowed origins

2. **Database Connection Failed**
   - Verify `MONGODB_URI` is correct
   - Check network connectivity
   - Ensure database user has proper permissions

3. **AI Service Errors**
   - Verify `OPENAI_API_KEY` is valid
   - Check API quota and billing
   - Monitor rate limits

4. **Payment Integration Issues**
   - Verify payment gateway credentials
   - Check webhook URLs
   - Monitor payment logs

### Logs and Debugging
```bash
# View application logs
tail -f logs/medora.log

# PM2 logs
pm2 logs medora-backend --lines 100

# Docker logs
docker logs medora-backend
```

## 📊 Performance Optimization

### Production Optimizations
- Enable gzip compression
- Use CDN for static assets
- Implement caching strategies
- Monitor memory usage
- Set up load balancing (if needed)

### Database Optimization
- Create proper indexes
- Monitor query performance
- Implement connection pooling
- Regular database maintenance

---

For additional support, contact: <EMAIL>
