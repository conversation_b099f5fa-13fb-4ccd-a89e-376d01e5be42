const express = require('express');
const { body, validationResult, param } = require('express-validator');
const Diagnosis = require('../models/Diagnosis');
const Patient = require('../models/Patient');
const { protect, restrictTo, requireDoctor } = require('../middleware/auth');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const mlService = require('../services/mlService');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation middleware
const validateDiagnosisCreation = [
  body('patient').isMongoId().withMessage('Valid patient ID is required'),
  body('chiefComplaint').trim().isLength({ min: 1 }).withMessage('Chief complaint is required'),
  body('symptoms').isArray({ min: 1 }).withMessage('At least one symptom is required'),
  body('symptoms.*.name').trim().isLength({ min: 1 }).withMessage('Symptom name is required'),
  body('symptoms.*.severity').isIn(['mild', 'moderate', 'severe']).withMessage('Valid severity is required'),
];

const validateSymptomAnalysis = [
  body('symptoms').isArray({ min: 1 }).withMessage('At least one symptom is required'),
  body('patientId').optional().isMongoId().withMessage('Valid patient ID required'),
  body('includeHistory').optional().isBoolean(),
];

// @route   GET /api/diagnosis
// @desc    Get all diagnoses (with filters)
// @access  Private (medical staff)
router.get('/', restrictTo('admin', 'doctor', 'nurse'), async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      patient,
      physician,
      status,
      priority,
      condition,
      startDate,
      endDate,
      sortBy = 'diagnosisDate',
      sortOrder = 'desc',
    } = req.query;

    // Build query
    const query = {};
    
    if (patient) query.patient = patient;
    if (physician) query.diagnosingPhysician = physician;
    if (status) query.status = status;
    if (priority) query.priority = priority;
    if (condition) {
      query.$or = [
        { 'primaryDiagnosis.condition': new RegExp(condition, 'i') },
        { 'differentialDiagnoses.condition': new RegExp(condition, 'i') },
      ];
    }
    
    // Date range filter
    if (startDate || endDate) {
      query.diagnosisDate = {};
      if (startDate) query.diagnosisDate.$gte = new Date(startDate);
      if (endDate) query.diagnosisDate.$lte = new Date(endDate);
    }

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const diagnoses = await Diagnosis.find(query)
      .populate('patient', 'mrn userId')
      .populate({
        path: 'patient',
        populate: {
          path: 'userId',
          select: 'firstName lastName dateOfBirth',
        },
      })
      .populate('diagnosingPhysician', 'firstName lastName specialty')
      .populate('followUp.assignedTo', 'firstName lastName specialty')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Diagnosis.countDocuments(query);

    res.json({
      status: 'success',
      data: {
        diagnoses,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total,
        },
      },
    });
  } catch (error) {
    logger.error('Get diagnoses error:', error);
    next(error);
  }
});

// @route   POST /api/diagnosis
// @desc    Create a new diagnosis
// @access  Private (doctors only)
router.post('/', requireDoctor, validateDiagnosisCreation, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400));
    }

    const {
      patient: patientId,
      chiefComplaint,
      historyOfPresentIllness,
      symptoms,
      physicalExamination,
      diagnosticTests,
      primaryDiagnosis,
      secondaryDiagnoses,
      treatmentPlan,
      followUp,
      priority,
      clinicalNotes,
    } = req.body;

    // Verify patient exists
    const patient = await Patient.findById(patientId).populate('userId');
    if (!patient) {
      return next(new AppError('Patient not found', 404));
    }

    // Create diagnosis
    const diagnosis = new Diagnosis({
      patient: patientId,
      diagnosingPhysician: req.user.id,
      chiefComplaint,
      historyOfPresentIllness,
      symptoms,
      physicalExamination,
      diagnosticTests,
      primaryDiagnosis,
      secondaryDiagnoses,
      treatmentPlan,
      followUp: {
        ...followUp,
        assignedTo: followUp?.assignedTo || req.user.id,
      },
      priority: priority || 'medium',
      clinicalNotes,
    });

    await diagnosis.save();
    await diagnosis.populate([
      { path: 'patient', select: 'mrn userId' },
      { path: 'diagnosingPhysician', select: 'firstName lastName specialty' },
    ]);

    logger.info(`New diagnosis created for patient: ${patient.mrn} by ${req.user.email}`);

    res.status(201).json({
      status: 'success',
      data: {
        diagnosis,
      },
    });
  } catch (error) {
    logger.error('Create diagnosis error:', error);
    next(error);
  }
});

// @route   POST /api/diagnosis/analyze-symptoms
// @desc    Analyze symptoms using AI/ML
// @access  Private (medical staff)
router.post('/analyze-symptoms', restrictTo('admin', 'doctor', 'nurse'), validateSymptomAnalysis, async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400));
    }

    const { symptoms, patientId, includeHistory = false } = req.body;

    let patientData = null;
    if (patientId) {
      patientData = await Patient.findById(patientId)
        .populate('userId', 'dateOfBirth gender')
        .lean();
      
      if (!patientData) {
        return next(new AppError('Patient not found', 404));
      }
    }

    // Prepare data for ML analysis
    const analysisData = {
      symptoms,
      patientAge: patientData?.userId?.dateOfBirth 
        ? Math.floor((Date.now() - new Date(patientData.userId.dateOfBirth)) / (365.25 * 24 * 60 * 60 * 1000))
        : null,
      patientGender: patientData?.userId?.gender,
      vitalSigns: patientData?.latestVitalSigns,
      medicalHistory: includeHistory ? patientData?.medicalHistory : null,
      currentMedications: includeHistory ? patientData?.activeMedications : null,
    };

    // Call ML service for symptom analysis
    const aiAnalysis = await mlService.analyzeSymptoms(analysisData);

    logger.info(`Symptom analysis completed for ${patientId ? `patient: ${patientId}` : 'anonymous patient'} by ${req.user.email}`);

    res.json({
      status: 'success',
      data: {
        analysis: aiAnalysis,
        timestamp: new Date(),
        analyzedBy: {
          id: req.user.id,
          name: `${req.user.firstName} ${req.user.lastName}`,
          specialty: req.user.specialty,
        },
      },
    });
  } catch (error) {
    logger.error('Symptom analysis error:', error);
    next(error);
  }
});

// @route   GET /api/diagnosis/:id
// @desc    Get diagnosis by ID
// @access  Private (medical staff)
router.get('/:id', param('id').isMongoId(), restrictTo('admin', 'doctor', 'nurse'), async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Invalid diagnosis ID', 400));
    }

    const diagnosis = await Diagnosis.findById(req.params.id)
      .populate('patient', 'mrn userId')
      .populate({
        path: 'patient',
        populate: {
          path: 'userId',
          select: 'firstName lastName dateOfBirth gender phoneNumber',
        },
      })
      .populate('diagnosingPhysician', 'firstName lastName specialty licenseNumber')
      .populate('followUp.assignedTo', 'firstName lastName specialty')
      .populate('modifiedBy', 'firstName lastName')
      .populate('qualityMetrics.reviewedBy', 'firstName lastName specialty');

    if (!diagnosis) {
      return next(new AppError('Diagnosis not found', 404));
    }

    res.json({
      status: 'success',
      data: {
        diagnosis,
      },
    });
  } catch (error) {
    logger.error('Get diagnosis error:', error);
    next(error);
  }
});

// @route   PUT /api/diagnosis/:id
// @desc    Update diagnosis
// @access  Private (doctors only)
router.put('/:id', requireDoctor, param('id').isMongoId(), async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Invalid diagnosis ID', 400));
    }

    const allowedUpdates = [
      'historyOfPresentIllness',
      'symptoms',
      'physicalExamination',
      'diagnosticTests',
      'primaryDiagnosis',
      'secondaryDiagnoses',
      'treatmentPlan',
      'followUp',
      'status',
      'priority',
      'clinicalNotes',
      'outcome',
    ];

    const updates = {};
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    updates.modifiedBy = req.user.id;
    updates.lastModified = new Date();

    const diagnosis = await Diagnosis.findByIdAndUpdate(
      req.params.id,
      updates,
      { new: true, runValidators: true }
    ).populate([
      { path: 'patient', select: 'mrn userId' },
      { path: 'diagnosingPhysician', select: 'firstName lastName specialty' },
    ]);

    if (!diagnosis) {
      return next(new AppError('Diagnosis not found', 404));
    }

    logger.info(`Diagnosis updated: ${diagnosis._id} by ${req.user.email}`);

    res.json({
      status: 'success',
      data: {
        diagnosis,
      },
    });
  } catch (error) {
    logger.error('Update diagnosis error:', error);
    next(error);
  }
});

// @route   POST /api/diagnosis/:id/ai-analysis
// @desc    Run AI analysis on existing diagnosis
// @access  Private (doctors only)
router.post('/:id/ai-analysis', requireDoctor, param('id').isMongoId(), async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Invalid diagnosis ID', 400));
    }

    const diagnosis = await Diagnosis.findById(req.params.id)
      .populate('patient')
      .populate({
        path: 'patient',
        populate: {
          path: 'userId',
          select: 'dateOfBirth gender',
        },
      });

    if (!diagnosis) {
      return next(new AppError('Diagnosis not found', 404));
    }

    // Prepare comprehensive data for AI analysis
    const analysisData = {
      symptoms: diagnosis.symptoms,
      physicalExamination: diagnosis.physicalExamination,
      diagnosticTests: diagnosis.diagnosticTests,
      patientAge: diagnosis.patient.userId?.dateOfBirth 
        ? Math.floor((Date.now() - new Date(diagnosis.patient.userId.dateOfBirth)) / (365.25 * 24 * 60 * 60 * 1000))
        : null,
      patientGender: diagnosis.patient.userId?.gender,
      vitalSigns: diagnosis.patient.latestVitalSigns,
      medicalHistory: diagnosis.patient.medicalHistory,
      currentMedications: diagnosis.patient.activeMedications,
      existingDiagnosis: diagnosis.primaryDiagnosis,
    };

    // Run comprehensive AI analysis
    const aiAnalysis = await mlService.comprehensiveAnalysis(analysisData);

    // Update diagnosis with AI analysis
    diagnosis.aiAnalysis = aiAnalysis;
    diagnosis.modifiedBy = req.user.id;
    diagnosis.lastModified = new Date();
    
    await diagnosis.save();

    logger.info(`AI analysis completed for diagnosis: ${diagnosis._id} by ${req.user.email}`);

    res.json({
      status: 'success',
      data: {
        aiAnalysis,
        diagnosisId: diagnosis._id,
      },
    });
  } catch (error) {
    logger.error('AI analysis error:', error);
    next(error);
  }
});

// @route   POST /api/diagnosis/:id/review
// @desc    Review and approve diagnosis
// @access  Private (senior doctors/admin)
router.post('/:id/review', restrictTo('admin', 'doctor'), param('id').isMongoId(), async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Invalid diagnosis ID', 400));
    }

    const { approved, reviewNotes, qualityScore } = req.body;

    const diagnosis = await Diagnosis.findById(req.params.id);
    if (!diagnosis) {
      return next(new AppError('Diagnosis not found', 404));
    }

    // Update quality metrics
    diagnosis.qualityMetrics = {
      ...diagnosis.qualityMetrics,
      reviewRequired: false,
      reviewedBy: req.user.id,
      reviewedAt: new Date(),
      reviewNotes,
      accuracy: qualityScore || diagnosis.qualityMetrics?.accuracy,
    };

    // Update status if approved
    if (approved) {
      diagnosis.status = 'confirmed';
    }

    diagnosis.modifiedBy = req.user.id;
    diagnosis.lastModified = new Date();
    
    await diagnosis.save();

    logger.info(`Diagnosis reviewed: ${diagnosis._id} by ${req.user.email} - ${approved ? 'Approved' : 'Needs revision'}`);

    res.json({
      status: 'success',
      data: {
        diagnosis,
        reviewStatus: approved ? 'approved' : 'needs_revision',
      },
    });
  } catch (error) {
    logger.error('Diagnosis review error:', error);
    next(error);
  }
});

// @route   GET /api/diagnosis/stats/overview
// @desc    Get diagnosis statistics overview
// @access  Private (medical staff)
router.get('/stats/overview', restrictTo('admin', 'doctor', 'nurse'), async (req, res, next) => {
  try {
    const { startDate, endDate, physician } = req.query;

    // Build date filter
    const dateFilter = {};
    if (startDate || endDate) {
      dateFilter.diagnosisDate = {};
      if (startDate) dateFilter.diagnosisDate.$gte = new Date(startDate);
      if (endDate) dateFilter.diagnosisDate.$lte = new Date(endDate);
    }

    // Add physician filter if specified
    if (physician) {
      dateFilter.diagnosingPhysician = physician;
    }

    // Aggregate statistics
    const stats = await Diagnosis.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: null,
          totalDiagnoses: { $sum: 1 },
          byStatus: {
            $push: '$status',
          },
          byPriority: {
            $push: '$priority',
          },
          avgProcessingTime: {
            $avg: '$qualityMetrics.timeliness',
          },
          avgAccuracy: {
            $avg: '$qualityMetrics.accuracy',
          },
        },
      },
    ]);

    // Get top conditions
    const topConditions = await Diagnosis.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$primaryDiagnosis.condition',
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 } },
      { $limit: 10 },
    ]);

    res.json({
      status: 'success',
      data: {
        overview: stats[0] || {
          totalDiagnoses: 0,
          byStatus: [],
          byPriority: [],
          avgProcessingTime: 0,
          avgAccuracy: 0,
        },
        topConditions,
      },
    });
  } catch (error) {
    logger.error('Get diagnosis stats error:', error);
    next(error);
  }
});

module.exports = router;