import React, { useState } from 'react';
import { 
  User, 
  Mail, 
  Building, 
  Shield, 
  Edit3, 
  Save, 
  X, 
  Crown,
  Settings,
  Bell,
  Lock,
  CreditCard
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardHeader, Card<PERSON>ontent, CardFooter } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';

interface UserProfileProps {
  user?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    organization: string;
    role: string;
    subscription: {
      plan: 'Starter' | 'Professional' | 'Enterprise';
      status: 'active' | 'expired' | 'trial';
      expiresAt: string;
    };
    avatar?: string;
  };
}

export const UserProfile: React.FC<UserProfileProps> = ({ 
  user = {
    id: '1',
    firstName: 'Dr. <PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    organization: 'Central Medical Center',
    role: 'Chief Medical Officer',
    subscription: {
      plan: 'Professional',
      status: 'active',
      expiresAt: '2024-12-31'
    }
  }
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: user.firstName,
    lastName: user.lastName,
    organization: user.organization,
    role: user.role
  });
  const [notifications, setNotifications] = useState({
    emailAlerts: true,
    pushNotifications: true,
    marketingEmails: false,
    securityAlerts: true
  });

  const handleSave = () => {
    // Save logic here
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData({
      firstName: user.firstName,
      lastName: user.lastName,
      organization: user.organization,
      role: user.role
    });
    setIsEditing(false);
  };

  const getSubscriptionBadge = () => {
    const { plan, status } = user.subscription;
    const variant = status === 'active' ? 'default' : status === 'trial' ? 'secondary' : 'destructive';
    const icon = plan === 'Professional' ? Crown : plan === 'Enterprise' ? Building : Shield;
    const IconComponent = icon;
    
    return (
      <Badge variant={variant} className="flex items-center gap-1">
        <IconComponent className="h-3 w-3" />
        {plan} {status === 'trial' && '(Trial)'}
      </Badge>
    );
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <img 
              src="/lovable-uploads/logo-png.png" 
              alt="MEDORA Logo" 
              className="h-12 w-12 object-contain"
            />
            <div>
              <h1 className="text-3xl font-bold font-unica" style={{color: '#9ACD32'}}>MEDORA Profile Settings</h1>
              <p className="text-muted-foreground">Manage your account and preferences</p>
            </div>
          </div>
          {getSubscriptionBadge()}
        </div>

        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 neu-flat">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Profile
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              Security
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              Notifications
            </TabsTrigger>
            <TabsTrigger value="billing" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              Billing
            </TabsTrigger>
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile">
            <Card className="card-gradient neu-raised">
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-foreground">Personal Information</h2>
                  <p className="text-muted-foreground">Update your personal details</p>
                </div>
                {!isEditing && (
                  <Button variant="outline" onClick={() => setIsEditing(true)} className="neu-flat">
                    <Edit3 className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                        disabled={!isEditing}
                        className="pl-9 neu-inset"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                      disabled={!isEditing}
                      className="neu-inset"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="email"
                      value={user.email}
                      disabled
                      className="pl-9 neu-inset bg-muted/20"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">Email cannot be changed. Contact support if needed.</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="organization">Organization</Label>
                  <div className="relative">
                    <Building className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="organization"
                      value={formData.organization}
                      onChange={(e) => setFormData(prev => ({ ...prev, organization: e.target.value }))}
                      disabled={!isEditing}
                      className="pl-9 neu-inset"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <div className="relative">
                    <Shield className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="role"
                      value={formData.role}
                      onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value }))}
                      disabled={!isEditing}
                      className="pl-9 neu-inset"
                    />
                  </div>
                </div>
              </CardContent>
              {isEditing && (
                <CardFooter className="flex gap-2">
                  <Button onClick={handleSave} className="primary-gradient btn-3d">
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </Button>
                  <Button variant="outline" onClick={handleCancel} className="neu-flat">
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </CardFooter>
              )}
            </Card>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security">
            <div className="space-y-4">
              <Card className="card-gradient neu-raised">
                <CardHeader>
                  <h2 className="text-xl font-semibold text-foreground">Password & Security</h2>
                  <p className="text-muted-foreground">Manage your password and security settings</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button variant="outline" className="w-full justify-start neu-flat">
                    <Lock className="h-4 w-4 mr-2" />
                    Change Password
                  </Button>
                  <Button variant="outline" className="w-full justify-start neu-flat">
                    <Shield className="h-4 w-4 mr-2" />
                    Enable Two-Factor Authentication
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Notifications Tab */}
          <TabsContent value="notifications">
            <Card className="card-gradient neu-raised">
              <CardHeader>
                <h2 className="text-xl font-semibold text-foreground">Notification Preferences</h2>
                <p className="text-muted-foreground">Choose what notifications you want to receive</p>
              </CardHeader>
              <CardContent className="space-y-6">
                {Object.entries(notifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-foreground">
                        {key === 'emailAlerts' && 'Email Alerts'}
                        {key === 'pushNotifications' && 'Push Notifications'}
                        {key === 'marketingEmails' && 'Marketing Emails'}
                        {key === 'securityAlerts' && 'Security Alerts'}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {key === 'emailAlerts' && 'Receive important updates via email'}
                        {key === 'pushNotifications' && 'Get real-time notifications'}
                        {key === 'marketingEmails' && 'Product updates and offers'}
                        {key === 'securityAlerts' && 'Critical security notifications'}
                      </p>
                    </div>
                    <Switch
                      checked={value}
                      onCheckedChange={(checked) => 
                        setNotifications(prev => ({ ...prev, [key]: checked }))
                      }
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Billing Tab */}
          <TabsContent value="billing">
            <Card className="card-gradient neu-raised">
              <CardHeader>
                <h2 className="text-xl font-semibold text-foreground">Billing & Subscription</h2>
                <p className="text-muted-foreground">Manage your subscription and billing information</p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 border border-border rounded-lg neu-flat">
                  <div>
                    <h3 className="font-medium text-foreground">Current Plan</h3>
                    <p className="text-sm text-muted-foreground">
                      {user.subscription.plan} - Expires {user.subscription.expiresAt}
                    </p>
                  </div>
                  {getSubscriptionBadge()}
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" className="neu-flat">
                    <CreditCard className="h-4 w-4 mr-2" />
                    Update Payment Method
                  </Button>
                  <Button variant="outline" className="neu-flat">
                    View Billing History
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};