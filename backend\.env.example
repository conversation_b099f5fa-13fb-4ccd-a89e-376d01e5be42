# =============================================================================
# MEDORA Backend Environment Configuration
# Copy this file to .env and configure your values
# =============================================================================

# Server Configuration
PORT=5000
NODE_ENV=production
FRONTEND_URL=https://www.medoraai.me

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/medora?retryWrites=true&w=majority
DB_NAME=medora

# Authentication & Security
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
SESSION_SECRET=your-session-secret-key-here-also-make-it-random
JWT_EXPIRE=7d
BCRYPT_ROUNDS=12

# Redis (for caching and job queues)
REDIS_URL=redis://localhost:6379

# Machine Learning
ML_MODEL_PATH=./models
TENSORFLOW_BACKEND=cpu

# AI Service Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=500
OPENAI_TEMPERATURE=0.7

# Alternative AI Services (Optional)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# AI Feature Flags
ENABLE_AI_CHAT=true
ENABLE_ML_ANALYSIS=true
ENABLE_VOICE_FEATURES=true

# Web Scraping
SCRAPE_DELAY=1000
MAX_CONCURRENT_SCRAPES=5
USER_AGENT=MEDORA-Bot/1.0

# Medical APIs (optional)
FDA_API_KEY=your_fda_api_key
PUBMED_API_KEY=your_pubmed_api_key
DRUGS_API_KEY=your_drugs_api_key

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/medora.log

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Security
CORS_ORIGIN=https://www.medoraai.me,https://medoraai.me,https://medora-ai-backend.vercel.app
HELMET_CSP=true

# Email (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# OAuth Social Authentication
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret
APPLE_CLIENT_ID=your_apple_client_id
APPLE_PRIVATE_KEY=your_apple_private_key
APPLE_KEY_ID=your_apple_key_id
APPLE_TEAM_ID=your_apple_team_id
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret

# OAuth Callback URLs
OAUTH_CALLBACK_BASE_URL=https://medoraai.me/api/auth

# Payment Gateways
PAYSTACK_SECRET_KEY=sk_live_your_paystack_secret_key_here
PAYSTACK_PUBLIC_KEY=pk_live_your_paystack_public_key_here
FLW_SECRET_KEY=your_flutterwave_secret_key_here
FLW_PUBLIC_KEY=your_flutterwave_public_key_here
FLW_SECRET_HASH=your_flutterwave_webhook_secret_hash_here

# CORS Configuration
ALLOWED_ORIGINS=https://www.medoraai.me,https://medoraai.me,https://medora-ai-backend.vercel.app,http://localhost:3000,http://localhost:1200,http://localhost:5173

# Webhook URLs
SLACK_WEBHOOK_URL=your_slack_webhook_url
DISCORD_WEBHOOK_URL=your_discord_webhook_url