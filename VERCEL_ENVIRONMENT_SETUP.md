# 🚀 Vercel Environment Variables Setup Guide

## ✅ **Deployment Status**

### 🎯 **Successfully Deployed:**
- **Frontend**: https://medora-main-ixfecm88k-bmds-projects-6efc3abf.vercel.app
- **Backend**: https://medora-ai-backend-fg7vidc2i-bmds-projects-6efc3abf.vercel.app

---

## 🔧 **Environment Variables Configuration**

### 📱 **Frontend Environment Variables**
Set these in the **medora-main** project dashboard:

```bash
# API Configuration
VITE_API_BASE_URL=https://medora-ai-backend-fg7vidc2i-bmds-projects-6efc3abf.vercel.app/api
VITE_API_URL=https://medora-ai-backend-fg7vidc2i-bmds-projects-6efc3abf.vercel.app

# Application Configuration
VITE_APP_NAME=MEDORA
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=AI-Powered Medical Assistant Platform

# Theme Configuration
VITE_DEFAULT_THEME=dark

# Development Configuration
VITE_DEV_MODE=false
VITE_DEBUG=false

# Feature Flags
VITE_ENABLE_VOICE_CHAT=true
VITE_ENABLE_VIDEO_CALLS=true
VITE_ENABLE_AI_DIAGNOSIS=true
VITE_ENABLE_PAYMENTS=true

# Contact Information
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_CONTACT_PHONE=******-123-4567

# Social Media
VITE_TWITTER_HANDLE=medora_ai

# OAuth Configuration (Optional - Set if you have these)
VITE_GOOGLE_CLIENT_ID=your_google_client_id
VITE_FACEBOOK_APP_ID=your_facebook_app_id
VITE_GITHUB_CLIENT_ID=your_github_client_id
VITE_TWITTER_CLIENT_ID=your_twitter_client_id
VITE_APPLE_CLIENT_ID=your_apple_client_id
VITE_LINKEDIN_CLIENT_ID=your_linkedin_client_id

# Payment Configuration (Optional - Set if you have these)
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
VITE_PAYSTACK_PUBLIC_KEY=your_paystack_public_key

# Analytics (Optional)
VITE_GOOGLE_ANALYTICS_ID=your_ga_id
VITE_HOTJAR_ID=your_hotjar_id
```

### 🔧 **Backend Environment Variables**
Set these in the **medora-ai-backend** project dashboard:

```bash
# Environment
NODE_ENV=production

# Database Configuration
MONGODB_URI=your_mongodb_connection_string
MONGODB_DB_NAME=medora_production

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# CORS Configuration
ALLOWED_ORIGINS=https://medora-main-ixfecm88k-bmds-projects-6efc3abf.vercel.app,https://medora-main-bmds-projects-6efc3abf.vercel.app

# Email Configuration (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password
EMAIL_FROM=<EMAIL>

# OAuth Secrets (Optional - Set if you have these)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
TWITTER_CONSUMER_KEY=your_twitter_consumer_key
TWITTER_CONSUMER_SECRET=your_twitter_consumer_secret
APPLE_CLIENT_ID=your_apple_client_id
APPLE_CLIENT_SECRET=your_apple_client_secret
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret

# Payment Configuration (Optional)
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
PAYSTACK_SECRET_KEY=your_paystack_secret_key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=your_session_secret_key

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=/tmp/uploads

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=medora.log

# Security Configuration
BCRYPT_ROUNDS=12
```

---

## 📋 **Setup Instructions**

### 🎯 **Step 1: Frontend Environment Variables**
1. Go to https://vercel.com/bmds-projects-6efc3abf/medora-main
2. Click on **Settings** tab
3. Click on **Environment Variables** in the sidebar
4. Add each variable from the Frontend section above
5. Set **Environment** to "Production, Preview, and Development"
6. Click **Save**

### 🔧 **Step 2: Backend Environment Variables**
1. Go to https://vercel.com/bmds-projects-6efc3abf/medora-ai-backend
2. Click on **Settings** tab
3. Click on **Environment Variables** in the sidebar
4. Add each variable from the Backend section above
5. Set **Environment** to "Production, Preview, and Development"
6. Click **Save**

### 🔄 **Step 3: Redeploy Applications**
After setting environment variables:
1. Go to each project's **Deployments** tab
2. Click on the latest deployment
3. Click **Redeploy** to apply the new environment variables

---

## 🔒 **Required Environment Variables**

### ⚠️ **Critical Variables (Must Set):**
- `MONGODB_URI` - Database connection
- `JWT_SECRET` - Authentication security
- `OPENAI_API_KEY` - AI functionality
- `ALLOWED_ORIGINS` - CORS security

### 📧 **Optional but Recommended:**
- Email configuration for notifications
- OAuth secrets for social login
- Payment gateway keys for billing
- Analytics IDs for tracking

---

## 🧪 **Testing the Deployment**

### ✅ **Frontend Testing:**
1. Visit: https://medora-main-ixfecm88k-bmds-projects-6efc3abf.vercel.app
2. Check that the page loads correctly
3. Verify API calls are working (check browser console)

### ✅ **Backend Testing:**
1. Visit: https://medora-ai-backend-fg7vidc2i-bmds-projects-6efc3abf.vercel.app
2. Should see a basic response or health check
3. Test API endpoints: `/api/health`, `/api/auth/status`

### 🔧 **API Connection Testing:**
1. Open browser developer tools
2. Go to Network tab
3. Use the frontend application
4. Verify API calls are going to the correct backend URL

---

## 🚨 **Troubleshooting**

### 🔍 **Common Issues:**
1. **CORS Errors**: Check `ALLOWED_ORIGINS` includes frontend URL
2. **Database Connection**: Verify `MONGODB_URI` is correct
3. **Authentication Issues**: Check `JWT_SECRET` is set
4. **API Not Found**: Verify backend URL in frontend config

### 📊 **Monitoring:**
- Check Vercel Function logs in the dashboard
- Monitor error rates and performance
- Set up alerts for critical failures

---

## 🎉 **Next Steps**

1. ✅ Set up environment variables
2. ✅ Test both frontend and backend
3. ✅ Configure custom domain (optional)
4. ✅ Set up monitoring and alerts
5. ✅ Configure CI/CD for automatic deployments

Your MEDORA application is now live on Vercel! 🚀
