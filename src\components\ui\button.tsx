
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 btn-3d neu-raised font-unica",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 primary-gradient border-2 border-primary/50 hover:border-primary/70 dark:border-primary/30 dark:hover:border-primary/50",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90 neu-raised border-2 border-destructive/50 hover:border-destructive/70 dark:border-destructive/30 dark:hover:border-destructive/50",
        outline:
          "border-2 border-primary/60 bg-background hover:bg-accent hover:text-accent-foreground glass hover:border-primary/80 dark:border-primary/30 dark:hover:border-primary/50",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80 neu-raised border-2 border-secondary/50 hover:border-secondary/70 dark:border-secondary/30 dark:hover:border-secondary/50",
        ghost: "hover:bg-accent hover:text-accent-foreground neu-flat border-2 border-transparent hover:border-primary/30 dark:hover:border-primary/20",
        link: "text-primary underline-offset-4 hover:underline border-2 border-transparent",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
