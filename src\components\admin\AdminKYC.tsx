import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye, 
  Download,
  Al<PERSON><PERSON>riangle,
  User,
  FileText,
  Camera
} from 'lucide-react';

const AdminKYC = () => {
  const [selectedStatus, setSelectedStatus] = useState('all');

  // Mock KYC data
  const kycApplications = [
    {
      id: 1,
      userId: 'USR001',
      userName: 'Dr. <PERSON>',
      email: '<EMAIL>',
      role: 'doctor',
      status: 'pending',
      submittedDate: '2024-01-20',
      documents: ['Medical License', 'ID Card', 'Proof of Address'],
      specialty: 'Cardiology',
      licenseNumber: 'MD123456'
    },
    {
      id: 2,
      userId: 'USR002',
      userName: 'Nurse <PERSON>',
      email: '<EMAIL>',
      role: 'nurse',
      status: 'approved',
      submittedDate: '2024-01-18',
      approvedDate: '2024-01-19',
      documents: ['Nursing License', 'ID Card', 'Proof of Address'],
      department: 'Emergency',
      licenseNumber: 'RN789012'
    },
    {
      id: 3,
      userId: 'USR003',
      userName: 'Dr. Michael Brown',
      email: '<EMAIL>',
      role: 'doctor',
      status: 'rejected',
      submittedDate: '2024-01-15',
      rejectedDate: '2024-01-17',
      rejectionReason: 'Invalid medical license',
      documents: ['Medical License', 'ID Card'],
      specialty: 'Neurology'
    },
    {
      id: 4,
      userId: 'USR004',
      userName: 'Dr. Emily Davis',
      email: '<EMAIL>',
      role: 'doctor',
      status: 'under_review',
      submittedDate: '2024-01-19',
      documents: ['Medical License', 'ID Card', 'Proof of Address', 'Specialty Certificate'],
      specialty: 'Pediatrics',
      licenseNumber: 'MD654321'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'under_review':
        return <Badge className="bg-blue-100 text-blue-800"><Eye className="w-3 h-3 mr-1" />Under Review</Badge>;
      case 'approved':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Approved</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Rejected</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'doctor':
        return <User className="w-4 h-4 text-blue-600" />;
      case 'nurse':
        return <User className="w-4 h-4 text-green-600" />;
      default:
        return <User className="w-4 h-4 text-gray-600" />;
    }
  };

  const filteredApplications = kycApplications.filter(app => 
    selectedStatus === 'all' || app.status === selectedStatus
  );

  const stats = {
    total: kycApplications.length,
    pending: kycApplications.filter(app => app.status === 'pending').length,
    underReview: kycApplications.filter(app => app.status === 'under_review').length,
    approved: kycApplications.filter(app => app.status === 'approved').length,
    rejected: kycApplications.filter(app => app.status === 'rejected').length
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">KYC Management</h1>
          <p className="text-muted-foreground">Review and manage Know Your Customer applications</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">All time</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">Awaiting review</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Under Review</CardTitle>
            <Eye className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.underReview}</div>
            <p className="text-xs text-muted-foreground">Being reviewed</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
            <p className="text-xs text-muted-foreground">Verified users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
            <p className="text-xs text-muted-foreground">Failed verification</p>
          </CardContent>
        </Card>
      </div>

      {/* KYC Applications */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>KYC Applications</CardTitle>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-input rounded-md bg-background"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="under_review">Under Review</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredApplications.map((application) => (
              <div key={application.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-[#ADF802]/20 rounded-full flex items-center justify-center">
                      {getRoleIcon(application.role)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-semibold">{application.userName}</h3>
                        {getStatusBadge(application.status)}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{application.email}</p>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Role:</span> {application.role}
                        </div>
                        <div>
                          <span className="font-medium">Submitted:</span> {application.submittedDate}
                        </div>
                        {application.specialty && (
                          <div>
                            <span className="font-medium">Specialty:</span> {application.specialty}
                          </div>
                        )}
                        {application.licenseNumber && (
                          <div>
                            <span className="font-medium">License:</span> {application.licenseNumber}
                          </div>
                        )}
                      </div>
                      <div className="mt-2">
                        <span className="font-medium text-sm">Documents:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {application.documents.map((doc, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              <FileText className="w-3 h-3 mr-1" />
                              {doc}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      {application.rejectionReason && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                          <div className="flex items-center text-red-800 text-sm">
                            <AlertTriangle className="w-4 h-4 mr-1" />
                            <span className="font-medium">Rejection Reason:</span>
                          </div>
                          <p className="text-red-700 text-sm mt-1">{application.rejectionReason}</p>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-1" />
                      Review
                    </Button>
                    {application.status === 'pending' && (
                      <>
                        <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Approve
                        </Button>
                        <Button variant="destructive" size="sm">
                          <XCircle className="w-4 h-4 mr-1" />
                          Reject
                        </Button>
                      </>
                    )}
                    <Button variant="ghost" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminKYC;
