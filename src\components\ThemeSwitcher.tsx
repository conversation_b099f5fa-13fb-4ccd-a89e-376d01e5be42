import React from 'react';
import { <PERSON>, <PERSON>, Palette, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useSystemTheme, type Theme } from '@/hooks/useSystemTheme';

export const ThemeSwitcher: React.FC = () => {
  const { theme, updateTheme } = useSystemTheme();

  const getThemeIcon = (themeType: Theme) => {
    switch (themeType) {
      case 'lemon':
        return <Palette className="w-4 h-4" />;
      case 'dark':
        return <Moon className="w-4 h-4" />;
      case 'light':
        return <Sun className="w-4 h-4" />;
      case 'system':
        return <Monitor className="w-4 h-4" />;
      default:
        return <Monitor className="w-4 h-4" />;
    }
  };

  const getThemeLabel = (themeType: Theme) => {
    switch (themeType) {
      case 'lemon':
        return 'Medical Green';
      case 'dark':
        return 'Dark';
      case 'light':
        return 'Light';
      case 'system':
        return 'System';
      default:
        return 'System';
    }
  };

  const themes: Theme[] = ['system', 'light', 'dark', 'lemon'];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2 border-2 border-primary hover:bg-primary hover:text-primary-foreground transition-all duration-300"
        >
          {getThemeIcon(theme)}
          <span className="hidden sm:inline">{getThemeLabel(theme)}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {themes.map((themeOption) => (
          <DropdownMenuItem
            key={themeOption}
            onClick={() => updateTheme(themeOption)}
            className="flex items-center gap-2 cursor-pointer"
          >
            {getThemeIcon(themeOption)}
            <span>{getThemeLabel(themeOption)}</span>
            {theme === themeOption && <span className="ml-auto text-primary">✓</span>}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ThemeSwitcher;
