const express = require('express');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const multer = require('multer');
const path = require('path');

const auth = require('../middleware/auth');
const restrictTo = require('../middleware/restrictTo');
const logger = require('../utils/logger');
const AppError = require('../utils/AppError');

// Import enhanced services
const EnhancedAIService = require('../services/enhancedAIService');
const MultimodalRAGSystem = require('../services/multimodalRAG');
const AgenticMedicalSystem = require('../services/agenticSystem');
const MedicalScrapingService = require('../services/medicalScrapingService');

const router = express.Router();

// Initialize services
const enhancedAI = new EnhancedAIService();
const multimodalRAG = new MultimodalRAGSystem();
const agenticSystem = new AgenticMedicalSystem(enhancedAI, multimodalRAG);
const scrapingService = new MedicalScrapingService();

// Rate limiting for AI endpoints
const aiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // limit each IP to 50 requests per windowMs
  message: 'Too many AI requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|pdf|docx|txt/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only images, PDFs, and documents are allowed'));
    }
  }
});

// Apply rate limiting to all routes
router.use(aiRateLimit);

// Validation middleware
const validateConsultationRequest = [
  body('patientData.chiefComplaint')
    .notEmpty()
    .withMessage('Chief complaint is required')
    .isLength({ min: 3, max: 500 })
    .withMessage('Chief complaint must be between 3 and 500 characters'),
  body('patientData.symptoms')
    .isArray({ min: 1 })
    .withMessage('At least one symptom is required'),
  body('patientData.age')
    .isInt({ min: 0, max: 150 })
    .withMessage('Age must be a valid number between 0 and 150'),
  body('patientData.gender')
    .isIn(['male', 'female', 'other'])
    .withMessage('Gender must be male, female, or other'),
];

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, errors.array()));
  }
  next();
};

/**
 * @route   POST /api/enhanced-ai/consultation
 * @desc    Conduct comprehensive AI medical consultation with 25 years experience simulation
 * @access  Private (medical staff)
 */
router.post('/consultation', 
  auth, 
  restrictTo('admin', 'doctor', 'nurse'),
  validateConsultationRequest,
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { patientData, consultationType = 'comprehensive' } = req.body;
      
      logger.info(`Enhanced AI consultation requested by ${req.user.email} for patient: ${patientData.patientId || 'anonymous'}`);
      
      // Add user context to patient data
      patientData.consultingPhysician = {
        id: req.user.id,
        name: `${req.user.firstName} ${req.user.lastName}`,
        specialty: req.user.specialty
      };

      // Conduct experienced doctor consultation
      const consultation = await enhancedAI.consultAsExperiencedDoctor(patientData);
      
      // Enhance with real-time medical information
      const realtimeInfo = await scrapingService.comprehensiveSearch(
        patientData.chiefComplaint,
        { maxResultsPerSource: 3 }
      );

      res.status(200).json({
        status: 'success',
        data: {
          consultation,
          realtimeInfo,
          consultationType,
          timestamp: new Date().toISOString(),
          consultedBy: req.user.id
        }
      });
    } catch (error) {
      logger.error('Enhanced AI consultation error:', error);
      next(error);
    }
  }
);

/**
 * @route   POST /api/enhanced-ai/multi-agent-consultation
 * @desc    Conduct multi-agent medical consultation
 * @access  Private (medical staff)
 */
router.post('/multi-agent-consultation',
  auth,
  restrictTo('admin', 'doctor'),
  validateConsultationRequest,
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { patientData, consultationType = 'comprehensive' } = req.body;
      
      logger.info(`Multi-agent consultation requested by ${req.user.email}`);
      
      // Conduct multi-agent consultation
      const consultation = await agenticSystem.conductMultiAgentConsultation(
        patientData,
        consultationType
      );

      res.status(200).json({
        status: 'success',
        data: {
          consultation,
          agentCount: Object.keys(consultation.agentReports).length,
          timestamp: new Date().toISOString(),
          consultedBy: req.user.id
        }
      });
    } catch (error) {
      logger.error('Multi-agent consultation error:', error);
      next(error);
    }
  }
);

/**
 * @route   POST /api/enhanced-ai/specialist-consultation/:specialty
 * @desc    Get consultation from specific specialist agent
 * @access  Private (medical staff)
 */
router.post('/specialist-consultation/:specialty',
  auth,
  restrictTo('admin', 'doctor', 'nurse'),
  validateConsultationRequest,
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { specialty } = req.params;
      const { patientData } = req.body;
      
      const validSpecialties = ['diagnostician', 'pharmacologist', 'radiologist', 'pathologist', 'emergencyPhysician', 'internist'];
      
      if (!validSpecialties.includes(specialty)) {
        return next(new AppError(`Invalid specialty. Must be one of: ${validSpecialties.join(', ')}`, 400));
      }

      logger.info(`${specialty} consultation requested by ${req.user.email}`);
      
      const consultation = await agenticSystem.getSpecialistConsultation(specialty, patientData);

      res.status(200).json({
        status: 'success',
        data: {
          consultation,
          specialty,
          timestamp: new Date().toISOString(),
          consultedBy: req.user.id
        }
      });
    } catch (error) {
      logger.error(`${req.params.specialty} consultation error:`, error);
      next(error);
    }
  }
);

/**
 * @route   POST /api/enhanced-ai/upload-document
 * @desc    Upload and process medical document for RAG system
 * @access  Private (medical staff)
 */
router.post('/upload-document',
  auth,
  restrictTo('admin', 'doctor'),
  upload.single('document'),
  async (req, res, next) => {
    try {
      if (!req.file) {
        return next(new AppError('No document uploaded', 400));
      }

      const { title, category, description } = req.body;
      
      const metadata = {
        id: `doc_${Date.now()}`,
        title: title || req.file.originalname,
        category: category || 'general',
        description: description || '',
        uploadedBy: req.user.id,
        uploadedAt: new Date().toISOString(),
        filename: req.file.originalname,
        size: req.file.size
      };

      let result;
      
      // Process based on file type
      if (req.file.mimetype.startsWith('image/')) {
        result = await multimodalRAG.processImageDocument(req.file, metadata);
      } else {
        const document = {
          buffer: req.file.buffer,
          type: path.extname(req.file.originalname).substring(1).toLowerCase()
        };
        result = await multimodalRAG.processTextDocument(document, metadata);
      }

      logger.info(`Document processed and added to RAG system: ${metadata.title}`);

      res.status(200).json({
        status: 'success',
        data: {
          result,
          metadata,
          message: 'Document successfully processed and added to knowledge base'
        }
      });
    } catch (error) {
      logger.error('Document upload error:', error);
      next(error);
    }
  }
);

/**
 * @route   GET /api/enhanced-ai/search-medical-literature
 * @desc    Search medical literature using RAG system
 * @access  Private (medical staff)
 */
router.get('/search-medical-literature',
  auth,
  restrictTo('admin', 'doctor', 'nurse'),
  async (req, res, next) => {
    try {
      const { 
        query, 
        includeTexts = 'true',
        includeImages = 'false',
        includeDrugs = 'true',
        includeLabs = 'true',
        maxResults = '10'
      } = req.query;

      if (!query) {
        return next(new AppError('Search query is required', 400));
      }

      const options = {
        includeTexts: includeTexts === 'true',
        includeImages: includeImages === 'true',
        includeDrugs: includeDrugs === 'true',
        includeLabs: includeLabs === 'true',
        maxResults: parseInt(maxResults)
      };

      const results = await multimodalRAG.retrieveRelevantInformation(query, options);

      logger.info(`Medical literature search performed by ${req.user.email}: ${query}`);

      res.status(200).json({
        status: 'success',
        data: {
          results,
          query,
          options,
          timestamp: new Date().toISOString(),
          searchedBy: req.user.id
        }
      });
    } catch (error) {
      logger.error('Medical literature search error:', error);
      next(error);
    }
  }
);

/**
 * @route   GET /api/enhanced-ai/real-time-medical-info
 * @desc    Get real-time medical information from trusted sources
 * @access  Private (medical staff)
 */
router.get('/real-time-medical-info',
  auth,
  restrictTo('admin', 'doctor', 'nurse'),
  async (req, res, next) => {
    try {
      const { 
        query,
        sources = 'pubmed,fda,who,cdc,medlineplus',
        maxResults = '5'
      } = req.query;

      if (!query) {
        return next(new AppError('Search query is required', 400));
      }

      const sourceList = sources.split(',');
      const options = {
        includePubMed: sourceList.includes('pubmed'),
        includeFDA: sourceList.includes('fda'),
        includeWHO: sourceList.includes('who'),
        includeCDC: sourceList.includes('cdc'),
        includeMedlinePlus: sourceList.includes('medlineplus'),
        maxResultsPerSource: parseInt(maxResults)
      };

      const results = await scrapingService.comprehensiveSearch(query, options);

      logger.info(`Real-time medical info search by ${req.user.email}: ${query}`);

      res.status(200).json({
        status: 'success',
        data: {
          results,
          query,
          sources: sourceList,
          timestamp: new Date().toISOString(),
          searchedBy: req.user.id
        }
      });
    } catch (error) {
      logger.error('Real-time medical info search error:', error);
      next(error);
    }
  }
);

/**
 * @route   POST /api/enhanced-ai/update-knowledge
 * @desc    Update medical knowledge across all AI agents
 * @access  Private (admin only)
 */
router.post('/update-knowledge',
  auth,
  restrictTo('admin'),
  async (req, res, next) => {
    try {
      const { knowledgeUpdate } = req.body;

      if (!knowledgeUpdate) {
        return next(new AppError('Knowledge update data is required', 400));
      }

      await agenticSystem.updateMedicalKnowledge(knowledgeUpdate);

      logger.info(`Medical knowledge updated by admin: ${req.user.email}`);

      res.status(200).json({
        status: 'success',
        data: {
          message: 'Medical knowledge updated successfully across all agents',
          timestamp: new Date().toISOString(),
          updatedBy: req.user.id
        }
      });
    } catch (error) {
      logger.error('Knowledge update error:', error);
      next(error);
    }
  }
);

/**
 * @route   GET /api/enhanced-ai/system-status
 * @desc    Get status of all AI systems
 * @access  Private (admin only)
 */
router.get('/system-status',
  auth,
  restrictTo('admin'),
  async (req, res, next) => {
    try {
      const status = {
        enhancedAI: {
          initialized: enhancedAI.isInitialized,
          status: enhancedAI.isInitialized ? 'operational' : 'initializing'
        },
        multimodalRAG: {
          initialized: multimodalRAG.isInitialized,
          status: multimodalRAG.isInitialized ? 'operational' : 'initializing'
        },
        agenticSystem: {
          initialized: agenticSystem.isInitialized,
          agentCount: Object.keys(agenticSystem.agents).length,
          status: agenticSystem.isInitialized ? 'operational' : 'initializing'
        },
        scrapingService: {
          cacheStats: scrapingService.getCacheStats(),
          status: 'operational'
        }
      };

      res.status(200).json({
        status: 'success',
        data: {
          systemStatus: status,
          timestamp: new Date().toISOString(),
          checkedBy: req.user.id
        }
      });
    } catch (error) {
      logger.error('System status check error:', error);
      next(error);
    }
  }
);

module.exports = router;
