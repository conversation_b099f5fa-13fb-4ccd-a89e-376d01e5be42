import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { CreditCard, DollarSign, Globe, CheckCircle, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface PaymentGateway {
  id: string;
  name: string;
  icon: string;
  description: string;
  fees: string;
  countries: string[];
  status: 'active' | 'inactive' | 'pending';
}

const PaymentGatewaySelector = ({ onPaymentSelect, selectedPlan }: { 
  onPaymentSelect: (gateway: string, amount: number) => void;
  selectedPlan: { name: string; price: number; };
}) => {
  const { toast } = useToast();
  const [selectedGateway, setSelectedGateway] = useState('stripe');
  const [manualPaymentDetails, setManualPaymentDetails] = useState({
    accountName: '',
    accountNumber: '',
    bankName: '',
    reference: ''
  });

  const paymentGateways: PaymentGateway[] = [
    {
      id: 'stripe',
      name: 'Stripe',
      icon: '💳',
      description: 'Global payment processing with excellent UX',
      fees: '2.9% + 30¢',
      countries: ['US', 'UK', 'EU', 'AU', 'CA'],
      status: 'active'
    },
    {
      id: 'paystack',
      name: 'Paystack',
      icon: '🌍',
      description: 'Leading African payment gateway',
      fees: '1.5% + ₦100',
      countries: ['NG', 'GH', 'ZA', 'KE'],
      status: 'active'
    },
    {
      id: 'flutterwave',
      name: 'Flutterwave',
      icon: '🦋',
      description: 'Pan-African payment infrastructure',
      fees: '1.4% + local fees',
      countries: ['NG', 'KE', 'UG', 'GH', 'RW'],
      status: 'active'
    },
    {
      id: 'paypal',
      name: 'PayPal',
      icon: '🏦',
      description: 'Trusted global payment solution',
      fees: '2.9% + fixed fee',
      countries: ['Global'],
      status: 'active'
    },
    {
      id: 'amazon-pay',
      name: 'Amazon Pay',
      icon: '📦',
      description: 'Amazon\'s payment service',
      fees: '2.9% + 30¢',
      countries: ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'],
      status: 'active'
    },
    {
      id: 'adyen',
      name: 'Adyen',
      icon: '🔷',
      description: 'Enterprise payment platform',
      fees: 'Custom pricing',
      countries: ['Global'],
      status: 'active'
    },
    {
      id: 'worldpay',
      name: 'Worldpay',
      icon: '🌐',
      description: 'Global payment processing',
      fees: 'Custom pricing',
      countries: ['Global'],
      status: 'active'
    },
    {
      id: 'skrill',
      name: 'Skrill',
      icon: '💰',
      description: 'Digital wallet and payments',
      fees: '1.45%',
      countries: ['Global'],
      status: 'active'
    }
  ];

  const handlePayment = (gatewayId: string) => {
    if (gatewayId === 'manual') {
      // Handle manual payment
      toast({
        title: "Manual Payment Instructions Sent",
        description: "Check your email for payment details and instructions.",
      });
      return;
    }

    if (gatewayId === 'check') {
      // Handle check payment
      toast({
        title: "Check Payment Instructions",
        description: "Mailing address and instructions have been sent to your email.",
      });
      return;
    }

    // Handle other gateways
    onPaymentSelect(gatewayId, selectedPlan.price);
    
    toast({
      title: "Redirecting to Payment",
      description: `Processing payment via ${paymentGateways.find(g => g.id === gatewayId)?.name}...`,
    });
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Gateway Selection
          </CardTitle>
          <p className="text-muted-foreground">
            Choose your preferred payment method for {selectedPlan.name} plan (${selectedPlan.price}/month)
          </p>
        </CardHeader>
      </Card>

      <Tabs defaultValue="gateways" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="gateways">Payment Gateways</TabsTrigger>
          <TabsTrigger value="manual">Manual Payment</TabsTrigger>
          <TabsTrigger value="check">Check Payment</TabsTrigger>
        </TabsList>

        <TabsContent value="gateways" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {paymentGateways.map((gateway) => (
              <Card 
                key={gateway.id} 
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedGateway === gateway.id ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => setSelectedGateway(gateway.id)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-2xl">{gateway.icon}</span>
                      <CardTitle className="text-lg">{gateway.name}</CardTitle>
                    </div>
                    <Badge 
                      variant={gateway.status === 'active' ? 'default' : 'secondary'}
                      className="capitalize"
                    >
                      {gateway.status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-2">
                    {gateway.description}
                  </p>
                  <div className="space-y-1">
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-3 w-3" />
                      <span className="text-xs">Fees: {gateway.fees}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Globe className="h-3 w-3" />
                      <span className="text-xs">
                        {gateway.countries.length > 3 
                          ? `${gateway.countries.slice(0, 3).join(', ')}...` 
                          : gateway.countries.join(', ')
                        }
                      </span>
                    </div>
                  </div>
                  {selectedGateway === gateway.id && (
                    <Button 
                      className="w-full mt-4" 
                      onClick={() => handlePayment(gateway.id)}
                    >
                      Pay with {gateway.name}
                    </Button>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="manual" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Manual Payment Instructions
              </CardTitle>
              <p className="text-muted-foreground">
                Transfer payment directly to our bank account and provide details below
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="font-semibold mb-2">Bank Details</h3>
                <div className="space-y-1 text-sm">
                  <p><strong>Account Name:</strong> MEDORA Technologies Inc.</p>
                  <p><strong>Account Number:</strong> *********0</p>
                  <p><strong>Bank Name:</strong> First National Bank</p>
                  <p><strong>Routing Number:</strong> *********</p>
                  <p><strong>SWIFT Code:</strong> FNBKUS33</p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="reference">Payment Reference</Label>
                  <Input 
                    id="reference"
                    placeholder="Enter your payment reference/transaction ID"
                    value={manualPaymentDetails.reference}
                    onChange={(e) => setManualPaymentDetails(prev => ({
                      ...prev,
                      reference: e.target.value
                    }))}
                  />
                </div>
                <div>
                  <Label htmlFor="amount">Amount Paid</Label>
                  <Input 
                    id="amount"
                    type="number"
                    placeholder={`${selectedPlan.price}.00`}
                    defaultValue={selectedPlan.price}
                  />
                </div>
                <div>
                  <Label htmlFor="date">Payment Date</Label>
                  <Input 
                    id="date"
                    type="date"
                  />
                </div>
              </div>

              <Button 
                className="w-full" 
                onClick={() => handlePayment('manual')}
              >
                Submit Manual Payment Details
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="check" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Check Payment
              </CardTitle>
              <p className="text-muted-foreground">
                Send a check to our mailing address
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="font-semibold mb-2">Mailing Address</h3>
                <div className="space-y-1 text-sm">
                  <p>MEDORA Technologies Inc.</p>
                  <p>Accounts Receivable Department</p>
                  <p>123 Business Avenue, Suite 456</p>
                  <p>New York, NY 10001</p>
                  <p>United States</p>
                </div>
              </div>

              <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg border border-amber-200 dark:border-amber-800">
                <h3 className="font-semibold mb-2 text-amber-800 dark:text-amber-200">
                  Important Instructions
                </h3>
                <ul className="space-y-1 text-sm text-amber-700 dark:text-amber-300">
                  <li>• Make check payable to "MEDORA Technologies Inc."</li>
                  <li>• Write your email address on the memo line</li>
                  <li>• Include plan type: {selectedPlan.name}</li>
                  <li>• Amount: ${selectedPlan.price}.00</li>
                  <li>• Allow 7-10 business days for processing</li>
                </ul>
              </div>

              <Button 
                className="w-full" 
                onClick={() => handlePayment('check')}
              >
                Confirm Check Payment Instructions
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PaymentGatewaySelector;