import { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import { motion } from 'framer-motion';
import AOS from 'aos';
import 'aos/dist/aos.css';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Logo from '@/components/Logo';
import ThemeSwitcher from '@/components/ThemeSwitcher';
import { PagePreloader } from '@/components/ui/loading';
import ModernNavbar from '@/components/ui/modern-navbar';
import { ModernCard } from '@/components/ui/modern-card';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import ResponsiveVoiceInterface from '@/components/ui/responsive-voice-interface';
// import SEOHead from '@/components/SEO/SEOHead';
import {
  Stethoscope,
  Zap,
  BarChart3,
  <PERSON>,
  <PERSON>R<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Clock,
  Shield as <PERSON><PERSON><PERSON><PERSON>,
  Mi<PERSON>,
  <PERSON>Off,
  <PERSON>,
  Brain,
  Users
} from "lucide-react";

const Index = () => {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);


  useEffect(() => {
    AOS.init({
      duration: 1000,
      easing: 'ease-out-cubic',
      once: true,
      offset: 100
    });

    // Simulate loading time for enhanced UX
    const timer = setTimeout(() => setIsLoading(false), 1500);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);



  const features = [
    {
      icon: Brain,
      title: "AI-Powered Diagnosis",
      description: "Advanced machine learning algorithms for accurate medical diagnosis and treatment recommendations.",
      gradient: "from-purple-500 to-pink-500",
      delay: "0"
    },
    {
      icon: Stethoscope,
      title: "Virtual Consultations",
      description: "Real-time voice and text-based consultations with medical AI assistants.",
      gradient: "from-blue-500 to-cyan-500",
      delay: "100"
    },
    {
      icon: ShieldCheck,
      title: "HIPAA Compliant",
      description: "Enterprise-grade security with end-to-end encryption and compliance standards.",
      gradient: "from-green-500 to-emerald-500",
      delay: "200"
    },
    {
      icon: Zap,
      title: "Instant Analysis",
      description: "Lightning-fast symptom analysis and risk assessment powered by AI.",
      gradient: "from-yellow-500 to-orange-500",
      delay: "300"
    },
    {
      icon: Users,
      title: "Patient Management",
      description: "Comprehensive patient records, vital signs tracking, and care coordination.",
      gradient: "from-indigo-500 to-purple-500",
      delay: "400"
    },
    {
      icon: BarChart3,
      title: "Analytics & Insights",
      description: "Advanced analytics and performance monitoring for healthcare providers.",
      gradient: "from-red-500 to-pink-500",
      delay: "500"
    }
  ];

  const stats = [
    { number: "99.9%", label: "Accuracy Rate", icon: Award },
    { number: "24/7", label: "Availability", icon: Clock },
    { number: "50K+", label: "Patients Served", icon: Users },
    { number: "500+", label: "Healthcare Providers", icon: Stethoscope }
  ];



  if (isLoading) {
    return <PagePreloader isLoading={isLoading} variant="ai" />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 dark:from-slate-900 dark:via-purple-900/20 dark:to-slate-900 relative overflow-hidden">
      {/* Modern Navigation */}
      <ModernNavbar />

      <div className="relative">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-[#ADF802]/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-[#ADF802] rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>



      {/* Hero Section */}
      <section className="pt-24 lg:pt-32 pb-16 lg:pb-20 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Badge className="mb-6 lg:mb-8 bg-gradient-to-r from-primary/20 to-purple-500/20 text-foreground border border-primary/30 backdrop-blur-sm hover:scale-105 transition-all duration-300 text-sm lg:text-base">
              <Brain className="w-4 h-4 mr-2 animate-pulse" />
              AI-Powered Medical Assistant
              <Sparkles className="w-4 h-4 ml-2" />
            </Badge>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative mb-6 lg:mb-8"
          >
            <h1 className="text-4xl sm:text-6xl lg:text-8xl font-bold mb-4 lg:mb-6 bg-gradient-to-r from-foreground via-primary to-purple-600 bg-clip-text text-transparent leading-tight font-bruno uppercase">
              MEDORA
            </h1>
            <div className="absolute inset-0 bg-gradient-to-r from-primary to-purple-500 blur-3xl opacity-10 animate-pulse"></div>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl sm:text-2xl lg:text-4xl font-semibold text-foreground/90 mb-4 lg:mb-6 font-bruno uppercase"
          >
            THE FUTURE OF <span className="text-primary">MEDICAL AI</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-base sm:text-lg lg:text-xl text-muted-foreground mb-8 lg:mb-12 max-w-4xl mx-auto leading-relaxed px-4"
          >
            Revolutionizing healthcare with advanced AI-powered diagnosis, virtual consultations,
            and comprehensive patient management. Experience the next generation of medical care
            with our cutting-edge artificial intelligence platform.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="flex flex-col sm:flex-row gap-4 lg:gap-6 justify-center items-center mb-12 lg:mb-16"
          >
            <Link to="/signup">
              <EnhancedButton
                size="lg"
                gradient
                glow
                pulse
                icon={<ArrowRight className="w-5 h-5" />}
                iconPosition="right"
                className="text-base lg:text-lg px-8 lg:px-10 py-4 lg:py-6 font-semibold font-unica uppercase w-full sm:w-auto"
              >
                START FREE TRIAL
              </EnhancedButton>
            </Link>
            <Link to="/demo">
              <EnhancedButton
                size="lg"
                variant="outline"
                glow
                icon={<Brain className="w-5 h-5" />}
                className="text-base lg:text-lg px-8 lg:px-10 py-4 lg:py-6 border-2 hover:border-primary hover:bg-primary/10 backdrop-blur-sm font-unica uppercase w-full sm:w-auto"
              >
                WATCH DEMO
              </EnhancedButton>
            </Link>
          </motion.div>

          {/* Voice Interface Demo */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="mb-16 lg:mb-20"
          >
            <ResponsiveVoiceInterface />

          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section id="stats" className="py-16 lg:py-20 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12 lg:mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 font-bruno bg-gradient-to-r from-foreground via-primary to-purple-600 bg-clip-text text-transparent">
              Trusted by Healthcare Professionals
            </h2>
            <p className="text-lg lg:text-xl text-muted-foreground">Real results from real medical practices</p>
          </motion.div>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-8">
            {stats.map((stat, index) => (
              <ModernCard
                key={index}
                glassmorphism
                hover3d
                glow
                delay={index * 0.1}
                className="text-center p-6 lg:p-8"
              >
                <div className="flex justify-center mb-4">
                  <div className="w-12 h-12 lg:w-16 lg:h-16 bg-gradient-to-r from-primary to-purple-600 rounded-full flex items-center justify-center">
                    <stat.icon className="w-6 h-6 lg:w-8 lg:h-8 text-white" />
                  </div>
                </div>
                <div className="text-2xl lg:text-4xl xl:text-5xl font-bold text-primary mb-2">
                  {stat.number}
                </div>
                <div className="text-sm lg:text-base text-muted-foreground font-medium">{stat.label}</div>
              </ModernCard>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 lg:py-20 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12 lg:mb-20"
          >
            <h2 className="text-3xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-foreground via-primary to-purple-600 bg-clip-text text-transparent font-bruno">
              Powerful AI Features
            </h2>
            <p className="text-lg lg:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Everything you need to provide exceptional healthcare with cutting-edge AI assistance
            </p>
          </motion.div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
            {features.map((feature, index) => (
              <ModernCard
                key={index}
                glassmorphism
                hover3d
                glow
                borderGlow
                delay={index * 0.1}
                className="group relative overflow-hidden p-6 lg:p-8"
              >
                <div className="w-12 h-12 lg:w-16 lg:h-16 bg-gradient-to-r from-primary/20 to-purple-600/20 border border-primary/30 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <feature.icon className="w-6 h-6 lg:w-8 lg:h-8 text-primary" />
                </div>
                <h3 className="text-xl lg:text-2xl font-bold mb-4 group-hover:text-primary transition-colors duration-300">
                  {feature.title}
                </h3>
                <p className="text-muted-foreground leading-relaxed group-hover:text-foreground/90 transition-colors duration-300 mb-6">
                  {feature.description}
                </p>
                <div className="flex items-center text-primary opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                  <span className="text-sm font-medium">Learn more</span>
                  <ArrowRight className="w-4 h-4 ml-2" />
                </div>
              </ModernCard>
            ))}
          </div>
        </div>
      </section>



      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-emerald-900/20 via-teal-900/20 to-green-900/20 dark:from-emerald-950/40 dark:via-teal-950/40 dark:to-green-950/40">
        <div className="max-w-4xl mx-auto text-center">
          <ModernCard glassmorphism glow className="p-8 lg:p-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
            >
              <h2 className="text-2xl lg:text-3xl font-bold mb-4 bg-gradient-to-r from-foreground via-primary to-emerald-600 bg-clip-text text-transparent font-bruno">
                Ready to Transform Your Practice?
              </h2>
              <p className="text-lg lg:text-xl text-muted-foreground mb-8">
                Join thousands of healthcare professionals who trust MEDORA for their AI-powered medical assistance.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/signup">
                  <EnhancedButton
                    size="lg"
                    gradient
                    glow
                    pulse
                    icon={<ArrowRight className="w-5 h-5" />}
                    iconPosition="right"
                    className="text-lg px-8 py-4 font-unica uppercase"
                  >
                    START FREE TRIAL
                  </EnhancedButton>
                </Link>
                <Link to="/pricing">
                  <EnhancedButton
                    size="lg"
                    variant="outline"
                    glow
                    className="text-lg px-8 py-4 border-2 border-primary hover:border-emerald-600 hover:bg-emerald-600/10"
                  >
                    View Pricing
                  </EnhancedButton>
                </Link>
              </div>
            </motion.div>
          </ModernCard>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-br from-emerald-900/30 via-teal-900/30 to-green-900/30 dark:from-emerald-950/60 dark:via-teal-950/60 dark:to-green-950/60 border-t border-emerald-800/30 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="mb-4">
                <Logo size="sm" showText={true} />
              </div>
              <p className="text-emerald-200/80 dark:text-emerald-300/60">
                Revolutionizing healthcare with AI-powered medical assistance.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4 text-emerald-100 dark:text-emerald-200">Product</h3>
              <ul className="space-y-2 text-emerald-200/70 dark:text-emerald-300/50">
                <li><a href="#features" className="hover:text-emerald-100 dark:hover:text-emerald-200 transition-colors">Features</a></li>
                <li><Link to="/pricing" className="hover:text-emerald-100 dark:hover:text-emerald-200 transition-colors">Pricing</Link></li>
                <li><a href="#" className="hover:text-emerald-100 dark:hover:text-emerald-200 transition-colors">API</a></li>
                <li><a href="#" className="hover:text-emerald-100 dark:hover:text-emerald-200 transition-colors">Documentation</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4 text-emerald-100 dark:text-emerald-200">Company</h3>
              <ul className="space-y-2 text-emerald-200/70 dark:text-emerald-300/50">
                <li><Link to="/about" className="hover:text-emerald-100 dark:hover:text-emerald-200 transition-colors">About</Link></li>
                <li><a href="#" className="hover:text-emerald-100 dark:hover:text-emerald-200 transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-emerald-100 dark:hover:text-emerald-200 transition-colors">Careers</a></li>
                <li><Link to="/contact" className="hover:text-emerald-100 dark:hover:text-emerald-200 transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4 text-emerald-100 dark:text-emerald-200">Support</h3>
              <ul className="space-y-2 text-emerald-200/70 dark:text-emerald-300/50">
                <li><Link to="/help" className="hover:text-emerald-100 dark:hover:text-emerald-200 transition-colors">Help Center</Link></li>
                <li><Link to="/privacy" className="hover:text-emerald-100 dark:hover:text-emerald-200 transition-colors">Privacy Policy</Link></li>
                <li><Link to="/terms" className="hover:text-emerald-100 dark:hover:text-emerald-200 transition-colors">Terms of Service</Link></li>
                <li><a href="#" className="hover:text-emerald-100 dark:hover:text-emerald-200 transition-colors">Security</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-emerald-800/30 mt-8 pt-8 text-center text-emerald-200/60 dark:text-emerald-300/40">
            <p>&copy; 2025 MEDORA. All rights reserved.</p>
          </div>
        </div>
      </footer>
      </div>
    </div>
  );
};


export default Index;