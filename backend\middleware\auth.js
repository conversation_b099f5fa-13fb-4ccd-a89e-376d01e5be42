const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { AppError } = require('./errorHandler');
const config = require('../config');
const logger = require('../utils/logger');

// Protect routes - verify JWT token
const protect = async (req, res, next) => {
  try {
    let token;

    // Get token from header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    // Check if token exists
    if (!token) {
      return next(new AppError('Access denied. No token provided.', 401));
    }

    // Verify token
    const decoded = jwt.verify(token, config.auth.jwtSecret);

    // Get user from token
    const user = await User.findById(decoded.userId);
    if (!user) {
      return next(new AppError('User not found. Token may be invalid.', 401));
    }

    // Check if user account is active
    if (!user.isActive) {
      return next(new AppError('User account is deactivated.', 401));
    }

    // Check if account is locked
    if (user.isLocked) {
      return next(new AppError('Account is temporarily locked due to multiple failed login attempts.', 423));
    }

    // Add user to request object
    req.user = {
      id: user._id,
      email: user.email,
      role: user.role,
      firstName: user.firstName,
      lastName: user.lastName,
      specialty: user.specialty,
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return next(new AppError('Invalid token.', 401));
    }
    if (error.name === 'TokenExpiredError') {
      return next(new AppError('Token expired.', 401));
    }
    logger.error('Auth middleware error:', error);
    next(new AppError('Authentication failed.', 401));
  }
};

// Restrict access to specific roles
const restrictTo = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('Authentication required.', 401));
    }

    if (!roles.includes(req.user.role)) {
      return next(new AppError('Access denied. Insufficient permissions.', 403));
    }

    next();
  };
};

// Check if user is doctor or admin
const requireMedicalStaff = restrictTo('doctor', 'admin');

// Check if user is admin
const requireAdmin = restrictTo('admin');

// Check if user is doctor
const requireDoctor = restrictTo('doctor');

// Check if user is patient or medical staff (for accessing patient data)
const requirePatientOrStaff = restrictTo('patient', 'doctor', 'nurse', 'admin');

// Optional authentication - doesn't fail if no token
const optionalAuth = async (req, res, next) => {
  try {
    let token;

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (token) {
      const decoded = jwt.verify(token, config.auth.jwtSecret);
      const user = await User.findById(decoded.userId);
      
      if (user && user.isActive && !user.isLocked) {
        req.user = {
          id: user._id,
          email: user.email,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName,
          specialty: user.specialty,
        };
      }
    }

    next();
  } catch (error) {
    // Continue without authentication if token is invalid
    next();
  }
};

// Check if user can access specific patient data
const canAccessPatient = async (req, res, next) => {
  try {
    const { patientId } = req.params;
    
    // Admin can access all patient data
    if (req.user.role === 'admin') {
      return next();
    }
    
    // Patients can only access their own data
    if (req.user.role === 'patient') {
      if (req.user.id !== patientId) {
        return next(new AppError('Access denied. You can only access your own data.', 403));
      }
      return next();
    }
    
    // Medical staff (doctors, nurses) can access patient data
    // In a real system, you might want to check if they're assigned to this patient
    if (['doctor', 'nurse'].includes(req.user.role)) {
      return next();
    }
    
    return next(new AppError('Access denied.', 403));
  } catch (error) {
    logger.error('Patient access check error:', error);
    next(error);
  }
};

module.exports = {
  protect,
  restrictTo,
  requireMedicalStaff,
  requireAdmin,
  requireDoctor,
  requirePatientOrStaff,
  optionalAuth,
  canAccessPatient,
};