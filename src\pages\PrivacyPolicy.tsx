import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Shield, Lock, Eye, Database, Users, AlertTriangle } from 'lucide-react';

const PrivacyPolicy = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/')}
              className="text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
            <div className="flex items-center space-x-3">
              <img 
                src="/lovable-uploads/logo-png.png" 
                alt="MEDORA Logo" 
                className="h-8 w-8 object-contain"
              />
              <span className="text-xl font-bold" style={{color: '#9ACD32'}}>MEDORA</span>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12 max-w-4xl">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <Shield className="h-16 w-16 text-primary mx-auto mb-4" />
          <h1 className="text-4xl font-bold mb-4">Privacy Policy</h1>
          <p className="text-xl text-muted-foreground">
            Your privacy and data security are our top priorities
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>

        <div className="space-y-8">
          {/* Introduction */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Eye className="h-5 w-5 mr-2" />
                Introduction
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                MEDORA ("we," "our," or "us") is committed to protecting your privacy and ensuring the security of your personal and health information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our AI-powered medical assistance platform.
              </p>
              <p>
                As a healthcare technology platform, we comply with the Health Insurance Portability and Accountability Act (HIPAA), the General Data Protection Regulation (GDPR), and other applicable privacy laws to ensure the highest standards of data protection.
              </p>
            </CardContent>
          </Card>

          {/* Information We Collect */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2" />
                Information We Collect
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Personal Information</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>Name, email address, phone number</li>
                  <li>Date of birth, gender, address</li>
                  <li>Professional credentials (for healthcare providers)</li>
                  <li>Payment and billing information</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Health Information</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>Medical history and symptoms</li>
                  <li>Diagnostic information and test results</li>
                  <li>Treatment plans and medications</li>
                  <li>Health monitoring data</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Technical Information</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>Device information and IP address</li>
                  <li>Browser type and operating system</li>
                  <li>Usage patterns and interaction data</li>
                  <li>Cookies and similar technologies</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* How We Use Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                How We Use Your Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Primary Uses</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>Provide AI-powered medical assistance and recommendations</li>
                  <li>Facilitate communication between patients and healthcare providers</li>
                  <li>Maintain and improve our services</li>
                  <li>Process payments and manage subscriptions</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Secondary Uses</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>Conduct research to improve AI algorithms (anonymized data only)</li>
                  <li>Ensure platform security and prevent fraud</li>
                  <li>Comply with legal obligations and regulatory requirements</li>
                  <li>Send important updates and notifications</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Data Security */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lock className="h-5 w-5 mr-2" />
                Data Security & Protection
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Security Measures</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>End-to-end encryption for all data transmission</li>
                  <li>AES-256 encryption for data at rest</li>
                  <li>Multi-factor authentication and access controls</li>
                  <li>Regular security audits and penetration testing</li>
                  <li>HIPAA-compliant cloud infrastructure</li>
                  <li>Employee training on data protection protocols</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Data Retention</h4>
                <p className="text-muted-foreground">
                  We retain your personal and health information only as long as necessary to provide our services, comply with legal obligations, or as required by applicable healthcare regulations. You may request deletion of your data at any time, subject to legal and regulatory requirements.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Your Rights */}
          <Card>
            <CardHeader>
              <CardTitle>Your Privacy Rights</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Under HIPAA (US Users)</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>Right to access your health information</li>
                  <li>Right to request amendments to your records</li>
                  <li>Right to request restrictions on use and disclosure</li>
                  <li>Right to receive confidential communications</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Under GDPR (EU Users)</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>Right to access, rectify, or erase your data</li>
                  <li>Right to data portability</li>
                  <li>Right to object to processing</li>
                  <li>Right to withdraw consent</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Third-Party Sharing */}
          <Card>
            <CardHeader>
              <CardTitle>Information Sharing</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                We do not sell, trade, or rent your personal or health information to third parties. We may share your information only in the following circumstances:
              </p>
              <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                <li>With your explicit consent</li>
                <li>With healthcare providers involved in your care</li>
                <li>To comply with legal obligations or court orders</li>
                <li>With service providers under strict confidentiality agreements</li>
                <li>In case of medical emergencies</li>
              </ul>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Contact Us
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                If you have questions about this Privacy Policy or wish to exercise your privacy rights, please contact us:
              </p>
              <div className="space-y-2 text-muted-foreground">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Phone:</strong> +****************</p>
                <p><strong>Address:</strong> MEDORA Privacy Office, 123 Healthcare Blvd, Medical City, MC 12345</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicy;
