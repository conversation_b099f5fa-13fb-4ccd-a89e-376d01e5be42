import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  FileText, 
  Stethoscope, 
  Brain, 
  TrendingUp, 
  Calendar,
  Search,
  Plus,
  Activity,
  Heart,
  Pill
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiClient } from '@/services/api';
import { useAuth } from '@/hooks/useAuth';
import MedicalRecords from '@/components/MedicalRecords';
import DiagnosisInterface from '@/components/DiagnosisInterface';
import LabTestUpload from '@/components/LabTestUpload';

interface Patient {
  _id: string;
  mrn: string;
  userId: {
    firstName: string;
    lastName: string;
    email: string;
    dateOfBirth: string;
    gender: string;
  };
  status: string;
  primaryCareProvider?: {
    firstName: string;
    lastName: string;
    specialty: string;
  };
}

interface DashboardStats {
  totalPatients: number;
  activeDiagnoses: number;
  pendingLabTests: number;
  todayAppointments: number;
  recentActivity: Array<{
    type: string;
    description: string;
    timestamp: string;
    priority: string;
  }>;
}

const MedicalDashboard: React.FC = () => {
  const { user } = useAuth();
  const [patients, setPatients] = useState<Patient[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [activeView, setActiveView] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [stats, setStats] = useState<DashboardStats>({
    totalPatients: 0,
    activeDiagnoses: 0,
    pendingLabTests: 0,
    todayAppointments: 0,
    recentActivity: []
  });
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      
      // Fetch patients if user is medical staff
      if (user?.role && ['admin', 'doctor', 'nurse'].includes(user.role)) {
        const patientsResponse = await apiClient.get('/api/patients?limit=50');
        setPatients((patientsResponse as unknown).data.data.patients);
      }

      // TODO: Replace with actual API calls to fetch dashboard stats
      setStats({
        totalPatients: 0,
        activeDiagnoses: 0,
        pendingLabTests: 0,
        todayAppointments: 0,
        recentActivity: []
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Unable to fetch dashboard information.";
      toast({
        title: "Failed to load dashboard data",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [user?.role, toast]);

  const filteredPatients = patients.filter(patient =>
    patient.userId.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.userId.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.mrn.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.userId.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'diagnosis': return <Stethoscope className="h-4 w-4" />;
      case 'lab_test': return <FileText className="h-4 w-4" />;
      case 'appointment': return <Calendar className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      low: 'secondary',
      medium: 'default',
      high: 'destructive'
    };
    return <Badge variant={(variants[priority as keyof typeof variants] || 'outline') as "secondary" | "default" | "destructive" | "outline"}>{priority.toUpperCase()}</Badge>;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading medical dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Medical Dashboard</h1>
            <p className="text-gray-600">Welcome back, Dr. {user?.firstName} {user?.lastName}</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              Schedule
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Patient
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Patients</p>
                  <p className="text-2xl font-bold">{stats.totalPatients}</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Diagnoses</p>
                  <p className="text-2xl font-bold">{stats.activeDiagnoses}</p>
                </div>
                <Stethoscope className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Lab Tests</p>
                  <p className="text-2xl font-bold">{stats.pendingLabTests}</p>
                </div>
                <FileText className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Today's Appointments</p>
                  <p className="text-2xl font-bold">{stats.todayAppointments}</p>
                </div>
                <Calendar className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Sidebar - Patient List */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Patients
                </CardTitle>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search patients..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="max-h-96 overflow-y-auto">
                  {filteredPatients.map((patient) => (
                    <div
                      key={patient._id}
                      className={`p-4 border-b cursor-pointer hover:bg-gray-50 ${
                        selectedPatient?._id === patient._id ? 'bg-blue-50 border-blue-200' : ''
                      }`}
                      onClick={() => setSelectedPatient(patient)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">
                            {patient.userId.firstName} {patient.userId.lastName}
                          </p>
                          <p className="text-sm text-gray-600">MRN: {patient.mrn}</p>
                          <p className="text-xs text-gray-500">
                            {patient.userId.gender} • {Math.floor((Date.now() - new Date(patient.userId.dateOfBirth).getTime()) / (365.25 * 24 * 60 * 60 * 1000))} years
                          </p>
                        </div>
                        <Badge variant={patient.status === 'active' ? 'default' : 'secondary'}>
                          {patient.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {stats.recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="mt-1">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm">{activity.description}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <p className="text-xs text-gray-500">
                            {new Date(activity.timestamp).toLocaleTimeString()}
                          </p>
                          {getPriorityBadge(activity.priority)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-2">
            {selectedPatient ? (
              <Tabs value={activeView} onValueChange={setActiveView}>
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="records">Records</TabsTrigger>
                  <TabsTrigger value="diagnosis">Diagnosis</TabsTrigger>
                  <TabsTrigger value="labs">Lab Tests</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="mt-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>
                        {selectedPatient.userId.firstName} {selectedPatient.userId.lastName}
                      </CardTitle>
                      <CardDescription>
                        Patient Overview • MRN: {selectedPatient.mrn}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <div>
                            <h4 className="font-medium mb-2">Patient Information</h4>
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-600">Email:</span>
                                <span>{selectedPatient.userId.email}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Date of Birth:</span>
                                <span>{new Date(selectedPatient.userId.dateOfBirth).toLocaleDateString()}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Gender:</span>
                                <span>{selectedPatient.userId.gender}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Status:</span>
                                <Badge variant={selectedPatient.status === 'active' ? 'default' : 'secondary'}>
                                  {selectedPatient.status}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="space-y-4">
                          <div>
                            <h4 className="font-medium mb-2">Care Team</h4>
                            {selectedPatient.primaryCareProvider ? (
                              <div className="text-sm">
                                <p className="font-medium">
                                  Dr. {selectedPatient.primaryCareProvider.firstName} {selectedPatient.primaryCareProvider.lastName}
                                </p>
                                <p className="text-gray-600">{selectedPatient.primaryCareProvider.specialty}</p>
                              </div>
                            ) : (
                              <p className="text-sm text-gray-500">No primary care provider assigned</p>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="records" className="mt-6">
                  <MedicalRecords patientId={selectedPatient._id} />
                </TabsContent>

                <TabsContent value="diagnosis" className="mt-6">
                  <DiagnosisInterface 
                    patientId={selectedPatient._id}
                    onDiagnosisCreated={() => {
                      toast({
                        title: "Diagnosis created",
                        description: "New diagnosis has been saved successfully."
                      });
                    }}
                  />
                </TabsContent>

                <TabsContent value="labs" className="mt-6">
                  <LabTestUpload 
                    patientId={selectedPatient._id}
                    onTestUploaded={() => {
                      toast({
                        title: "Lab test uploaded",
                        description: "Lab test has been processed successfully."
                      });
                    }}
                  />
                </TabsContent>
              </Tabs>
            ) : (
              <Card>
                <CardContent className="p-12 text-center">
                  <Users className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Patient</h3>
                  <p className="text-gray-600 mb-4">
                    Choose a patient from the list to view their medical records and manage their care.
                  </p>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add New Patient
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MedicalDashboard;
