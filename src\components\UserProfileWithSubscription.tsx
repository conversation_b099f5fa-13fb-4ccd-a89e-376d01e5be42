import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Calendar } from '@/components/ui/calendar';
import { 
  User, 
  Crown, 
  CreditCard, 
  Settings, 
  Bell, 
  Shield, 
  Download,
  Calendar as CalendarIcon,
  TrendingUp,
  Key,
  Edit
} from 'lucide-react';

const UserProfileWithSubscription = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);

  // TODO: Replace with actual API call to fetch user data
  const user = {
    id: '',
    name: '',
    email: '',
    avatar: '',
    joinDate: '',
    subscription: {
      plan: '',
      status: '',
      billingCycle: '',
      nextBilling: '',
      amount: 0,
      currency: 'USD'
    },
    usage: {
      medicalConsultations: { used: 0, limit: 0 },
      reportsGenerated: { used: 0, limit: 0 },
      researchQueries: { used: 0, limit: 0 }
    },
    paymentHistory: [],
    apiKeys: []
  };

  const getSubscriptionBadge = (plan: string) => {
    const badges = {
      'Starter': { variant: 'secondary' as const, icon: User },
      'Professional': { variant: 'default' as const, icon: Crown },
      'Enterprise': { variant: 'destructive' as const, icon: Shield }
    };
    return badges[plan as keyof typeof badges] || badges.Starter;
  };

  const badgeConfig = getSubscriptionBadge(user.subscription.plan);
  const BadgeIcon = badgeConfig.icon;

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-6 mb-8">
          <Avatar className="h-20 w-20">
            <AvatarImage src={user.avatar} alt={user.name} />
            <AvatarFallback>{user.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-3xl font-bold text-foreground">{user.name}</h1>
              <Badge variant={badgeConfig.variant} className="flex items-center gap-1">
                <BadgeIcon className="h-3 w-3" />
                {user.subscription.plan}
              </Badge>
            </div>
            <p className="text-muted-foreground">{user.email}</p>
            <p className="text-sm text-muted-foreground">Member since {new Date(user.joinDate).toLocaleDateString()}</p>
          </div>

          <Button 
            variant="outline" 
            onClick={() => setIsEditing(!isEditing)}
            className="self-start"
          >
            <Edit className="h-4 w-4 mr-2" />
            {isEditing ? 'Cancel' : 'Edit Profile'}
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="subscription">Subscription</TabsTrigger>
            <TabsTrigger value="usage">Usage</TabsTrigger>
            <TabsTrigger value="billing">Billing</TabsTrigger>
            <TabsTrigger value="api-keys">API Keys</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Full Name</Label>
                    <Input 
                      id="name" 
                      defaultValue={user.name} 
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input 
                      id="email" 
                      type="email" 
                      defaultValue={user.email} 
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="company">Company</Label>
                    <Input 
                      id="company" 
                      placeholder="Your medical practice or hospital" 
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input 
                      id="phone" 
                      placeholder="+****************" 
                      disabled={!isEditing}
                    />
                  </div>
                </div>
                {isEditing && (
                  <Button>Save Changes</Button>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="subscription" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Crown className="h-5 w-5" />
                  Current Subscription
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div>
                    <h3 className="font-semibold text-lg">{user.subscription.plan} Plan</h3>
                    <p className="text-muted-foreground">
                      ${user.subscription.amount}/{user.subscription.billingCycle.toLowerCase()}
                    </p>
                  </div>
                  <Badge variant={user.subscription.status === 'Active' ? 'default' : 'destructive'}>
                    {user.subscription.status}
                  </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Next Billing Date</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(user.subscription.nextBilling).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Billing Cycle</p>
                      <p className="text-sm text-muted-foreground">{user.subscription.billingCycle}</p>
                    </div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button>Upgrade Plan</Button>
                  <Button variant="outline">Change Billing</Button>
                  <Button variant="destructive">Cancel Subscription</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="usage" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Medical Consultations</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Used</span>
                      <span>{user.usage.medicalConsultations.used}/{user.usage.medicalConsultations.limit}</span>
                    </div>
                    <Progress 
                      value={(user.usage.medicalConsultations.used / user.usage.medicalConsultations.limit) * 100} 
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Medical Reports Generated</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Used</span>
                      <span>{user.usage.reportsGenerated.used}/{user.usage.reportsGenerated.limit}</span>
                    </div>
                    <Progress 
                      value={(user.usage.reportsGenerated.used / user.usage.reportsGenerated.limit) * 100} 
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Research Queries</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Used</span>
                      <span>{user.usage.researchQueries.used}/{user.usage.researchQueries.limit}</span>
                    </div>
                    <Progress 
                      value={(user.usage.researchQueries.used / user.usage.researchQueries.limit) * 100} 
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="billing" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Payment History</CardTitle>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {user.paymentHistory.map((payment) => (
                    <div key={payment.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">${payment.amount}</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(payment.date).toLocaleDateString()} • {payment.method}
                        </p>
                      </div>
                      <Badge variant={payment.status === 'Paid' ? 'default' : 'secondary'}>
                        {payment.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="api-keys" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  API Keys
                </CardTitle>
                <Button>Generate New Key</Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {user.apiKeys.map((apiKey) => (
                    <div key={apiKey.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">{apiKey.name}</p>
                        <p className="text-sm text-muted-foreground font-mono">{apiKey.key}</p>
                        <p className="text-xs text-muted-foreground">
                          Created: {new Date(apiKey.created).toLocaleDateString()} • 
                          Last used: {new Date(apiKey.lastUsed).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">Regenerate</Button>
                        <Button size="sm" variant="destructive">Delete</Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Account Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="font-medium mb-4">Notifications</h3>
                  <div className="space-y-2">
                    <label className="flex items-center gap-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Email notifications for billing</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Usage limit alerts</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" />
                      <span className="text-sm">Product updates and news</span>
                    </label>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-4">Security</h3>
                  <div className="space-y-2">
                    <Button variant="outline">Change Password</Button>
                    <Button variant="outline">Enable Two-Factor Authentication</Button>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-4">Danger Zone</h3>
                  <Button variant="destructive">Delete Account</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default UserProfileWithSubscription;