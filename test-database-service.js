#!/usr/bin/env node

/**
 * MEDORA Database Service Test Script
 * 
 * This script tests the database service connectivity and health
 * Run with: node test-database-service.js
 */

const https = require('https');
const http = require('http');

// Configuration
const BASE_URL = 'https://medora-ai-backend-fg7vidc2i-bmds-projects-6efc3abf.vercel.app';
const ENDPOINTS = [
  '/health',
  '/api/status', 
  '/api/health/database',
  '/api'
];

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    const startTime = Date.now();
    
    const req = protocol.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            responseTime,
            data: jsonData,
            headers: res.headers
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            responseTime,
            data: data,
            headers: res.headers,
            parseError: e.message
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function testEndpoint(endpoint) {
  const url = `${BASE_URL}${endpoint}`;
  log(`\n🔍 Testing: ${endpoint}`, 'blue');
  log(`   URL: ${url}`, 'blue');
  
  try {
    const result = await makeRequest(url);
    
    if (result.statusCode === 200) {
      log(`   ✅ Status: ${result.statusCode} (${result.responseTime}ms)`, 'green');
      
      if (endpoint === '/api/health/database' && result.data) {
        const dbData = result.data.data || result.data;
        log(`   📊 Database Status:`, 'yellow');
        log(`      - Configured: ${dbData.configured}`, dbData.configured ? 'green' : 'red');
        log(`      - Connected: ${dbData.connected}`, dbData.connected ? 'green' : 'red');
        log(`      - Mode: ${dbData.mode}`, dbData.mode === 'production' ? 'green' : 'yellow');
        
        if (dbData.ping) {
          log(`      - Ping: ${dbData.ping} (${dbData.latency || 'N/A'})`, dbData.ping === 'success' ? 'green' : 'red');
        }
        
        if (dbData.message) {
          log(`      - Message: ${dbData.message}`, 'yellow');
        }
      }
      
      if (endpoint === '/api/status' && result.data) {
        log(`   🔧 Services:`, 'yellow');
        const features = result.data.features || {};
        Object.entries(features).forEach(([service, status]) => {
          log(`      - ${service}: ${status}`, status ? 'green' : 'red');
        });
      }
      
    } else {
      log(`   ❌ Status: ${result.statusCode} (${result.responseTime}ms)`, 'red');
      if (result.data && typeof result.data === 'string' && result.data.includes('Authentication Required')) {
        log(`   🔒 Error: Vercel authentication protection is enabled!`, 'red');
        log(`   💡 Solution: Disable deployment protection in Vercel dashboard`, 'yellow');
      }
    }
    
  } catch (error) {
    log(`   ❌ Error: ${error.message}`, 'red');
    
    if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
      log(`   🌐 Network Error: Cannot reach the server`, 'red');
    } else if (error.message.includes('timeout')) {
      log(`   ⏱️  Timeout: Server took too long to respond`, 'red');
    }
  }
}

async function runTests() {
  log('🚀 MEDORA Database Service Test', 'bold');
  log('=====================================', 'bold');
  log(`Testing backend at: ${BASE_URL}`, 'blue');
  
  for (const endpoint of ENDPOINTS) {
    await testEndpoint(endpoint);
  }
  
  log('\n📋 Test Summary:', 'bold');
  log('=====================================', 'bold');
  log('If you see "Authentication Required" errors:', 'yellow');
  log('1. Go to Vercel dashboard for medora-ai-backend project', 'yellow');
  log('2. Settings → Deployment Protection', 'yellow');
  log('3. Disable "Vercel Authentication" for production', 'yellow');
  log('', 'reset');
  log('If database shows as disconnected:', 'yellow');
  log('1. Set MONGODB_URI environment variable in Vercel', 'yellow');
  log('2. Redeploy the application', 'yellow');
  log('3. Check MongoDB Atlas IP whitelist and credentials', 'yellow');
  
  log('\n✅ Test completed!', 'green');
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testEndpoint };
