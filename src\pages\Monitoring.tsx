import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Activity, 
  Brain, 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  BarChart3,
  Users,
  Database,
  Server,
  Cpu,
  HardDrive,
  Network,
  MemoryStick,
  Zap,
  Target,
  Gauge,
  LineChart
} from "lucide-react";

interface SystemMetrics {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  responseTime: number;
  uptime: number;
  activeUsers: number;
  totalRequests: number;
  errorRate: number;
  aiAccuracy: number;
}

interface PerformanceData {
  timestamp: string;
  responseTime: number;
  accuracy: number;
  throughput: number;
}

const Monitoring = () => {
  const [metrics, setMetrics] = useState<SystemMetrics>({
    cpu: 0,
    memory: 0,
    disk: 0,
    network: 0,
    responseTime: 0,
    uptime: 0,
    activeUsers: 0,
    totalRequests: 0,
    errorRate: 0,
    aiAccuracy: 0
  });

  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);
  const [selectedTimeframe, setSelectedTimeframe] = useState('24h');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate real-time data updates
    const interval = setInterval(() => {
      setMetrics({
        cpu: Math.random() * 100,
        memory: Math.random() * 100,
        disk: Math.random() * 100,
        network: Math.random() * 100,
        responseTime: Math.random() * 500 + 50,
        uptime: 99.9,
        activeUsers: Math.floor(Math.random() * 1000) + 500,
        totalRequests: Math.floor(Math.random() * 10000) + 50000,
        errorRate: Math.random() * 2,
        aiAccuracy: 95 + Math.random() * 4
      });
    }, 5000);

    // Generate sample performance data
    const generatePerformanceData = () => {
      const data: PerformanceData[] = [];
      const now = new Date();
      for (let i = 23; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 60 * 1000);
        data.push({
          timestamp: time.toISOString(),
          responseTime: Math.random() * 300 + 100,
          accuracy: 90 + Math.random() * 8,
          throughput: Math.random() * 1000 + 500
        });
      }
      setPerformanceData(data);
    };

    generatePerformanceData();
    setIsLoading(false);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (value: number, threshold: number) => {
    if (value < threshold) return 'text-green-600';
    if (value < threshold * 1.5) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusIcon = (value: number, threshold: number) => {
    if (value < threshold) return <CheckCircle className="w-4 h-4 text-green-600" />;
    if (value < threshold * 1.5) return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
    return <XCircle className="w-4 h-4 text-red-600" />;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold gradient-text">System Monitoring</h1>
          <p className="text-gray-600">Real-time system performance and AI accuracy tracking</p>
        </div>
        <div className="flex space-x-2">
          {['1h', '24h', '7d', '30d'].map((timeframe) => (
            <Button
              key={timeframe}
              variant={selectedTimeframe === timeframe ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedTimeframe(timeframe)}
              className="gradient-primary"
            >
              {timeframe}
            </Button>
          ))}
        </div>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="modern-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <CheckCircle className="w-4 h-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Healthy</div>
            <p className="text-xs text-gray-600">All systems operational</p>
          </CardContent>
        </Card>

        <Card className="modern-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Uptime</CardTitle>
            <Clock className="w-4 h-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{metrics.uptime}%</div>
            <p className="text-xs text-gray-600">Last 30 days</p>
          </CardContent>
        </Card>

        <Card className="modern-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="w-4 h-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{metrics.activeUsers.toLocaleString()}</div>
            <p className="text-xs text-gray-600">Currently online</p>
          </CardContent>
        </Card>

        <Card className="modern-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Accuracy</CardTitle>
            <Brain className="w-4 h-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{metrics.aiAccuracy.toFixed(1)}%</div>
            <p className="text-xs text-gray-600">Diagnosis accuracy</p>
          </CardContent>
        </Card>
      </div>

      {/* System Resources */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="modern-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Server className="w-5 h-5" />
              <span>System Resources</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Cpu className="w-4 h-4 text-blue-600" />
                <span className="text-sm">CPU Usage</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{metrics.cpu.toFixed(1)}%</span>
                {getStatusIcon(metrics.cpu, 80)}
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${metrics.cpu > 80 ? 'bg-red-500' : metrics.cpu > 60 ? 'bg-yellow-500' : 'bg-green-500'}`}
                style={{ width: `${metrics.cpu}%` }}
              ></div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Database className="w-4 h-4 text-green-600" />
                <span className="text-sm">Memory Usage</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{metrics.memory.toFixed(1)}%</span>
                {getStatusIcon(metrics.memory, 85)}
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${metrics.memory > 85 ? 'bg-red-500' : metrics.memory > 70 ? 'bg-yellow-500' : 'bg-green-500'}`}
                style={{ width: `${metrics.memory}%` }}
              ></div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <HardDrive className="w-4 h-4 text-purple-600" />
                <span className="text-sm">Disk Usage</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{metrics.disk.toFixed(1)}%</span>
                {getStatusIcon(metrics.disk, 90)}
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${metrics.disk > 90 ? 'bg-red-500' : metrics.disk > 75 ? 'bg-yellow-500' : 'bg-green-500'}`}
                style={{ width: `${metrics.disk}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card className="modern-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="w-5 h-5" />
              <span>Performance Metrics</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Zap className="w-4 h-4 text-yellow-600" />
                <span className="text-sm">Response Time</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{metrics.responseTime.toFixed(0)}ms</span>
                {getStatusIcon(metrics.responseTime, 300)}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <BarChart3 className="w-4 h-4 text-blue-600" />
                <span className="text-sm">Total Requests</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{metrics.totalRequests.toLocaleString()}</span>
                <TrendingUp className="w-4 h-4 text-green-600" />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <XCircle className="w-4 h-4 text-red-600" />
                <span className="text-sm">Error Rate</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{metrics.errorRate.toFixed(2)}%</span>
                {getStatusIcon(metrics.errorRate, 1)}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Target className="w-4 h-4 text-green-600" />
                <span className="text-sm">AI Accuracy</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{metrics.aiAccuracy.toFixed(1)}%</span>
                <CheckCircle className="w-4 h-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Charts */}
      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <LineChart className="w-5 h-5" />
            <span>Performance Trends</span>
          </CardTitle>
          <CardDescription>Response time and accuracy over the last 24 hours</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-end justify-between space-x-2">
            {performanceData.map((data, index) => (
              <div key={index} className="flex flex-col items-center space-y-2">
                <div className="w-8 bg-gradient-to-t from-green-500 to-blue-500 rounded-t" 
                     style={{ height: `${(data.responseTime / 500) * 100}px` }}>
                </div>
                <div className="text-xs text-gray-500">
                  {new Date(data.timestamp).getHours()}:00
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 flex justify-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span>Response Time</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span>Accuracy</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alerts and Notifications */}
      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-yellow-600" />
            <span>System Alerts</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-4 h-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">High CPU usage detected</span>
              </div>
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Warning</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm text-green-800">All systems operational</span>
              </div>
              <Badge variant="secondary" className="bg-green-100 text-green-800">Normal</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Monitoring;
